import{l as te,d as le,u as z,a as ne,g as oe}from"./info-Da-CLVEk.js";import{g as se,e as ie}from"./columnUtils-DYlA-XL_.js";import{T as ce}from"./index-BWWetMd6.js";import re from"./PolicyFormDialog-Cw7cdYm8.js";import{C as ue,d as de,r as l,D as pe,I as me,e as fe,R as he,f as I,K as ge,c as L,o as d,l as y,i as _,j as i,k as c,h as ye,G as C,p as v}from"./index-DP10CBaW.js";const ve=P=>{const{sys_normal_disable:n}=P.useDict("sys_normal_disable");return{dialogWidth:"800px",dialogHeight:"60vh",labelWidth:"100px",column:[{label:"基础信息",prop:"divider_basic_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"政策名称",prop:"policyName",search:!0,searchSpan:12,minWidth:200,rules:[{required:!0,message:"政策名称不能为空",trigger:"blur"},{min:2,max:200,message:"政策名称长度必须介于 2 和 200 之间",trigger:"blur"}],span:24},{label:"政策类型",prop:"policyType",search:!0,searchSpan:12,width:120,align:"center",type:"select",dicData:[{label:"就业扶持",value:"就业扶持"},{label:"创业支持",value:"创业支持"},{label:"技能培训",value:"技能培训"},{label:"社会保障",value:"社会保障"},{label:"其他",value:"其他"}],span:12,rules:[{required:!0,message:"政策类型不能为空",trigger:"change"}]},{label:"状态",prop:"status",search:!0,searchSpan:12,width:100,align:"center",type:"select",dicData:n,span:12,slot:!0,rules:[{required:!0,message:"状态不能为空",trigger:"change"}]},{label:"政策描述",prop:"policyDescription",search:!1,type:"textarea",span:24,minRows:4,maxRows:8,showWordLimit:!0,maxlength:2e3,rules:[{max:2e3,message:"政策描述不能超过2000个字符",trigger:"blur"}]},{label:"系统信息",prop:"divider_system_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0,addDisplay:!1,editDisplay:!1},{label:"备注",prop:"remark",search:!1,type:"textarea",span:24,minRows:2,maxRows:4,showWordLimit:!0,maxlength:500,addDisplay:!1,editDisplay:!1}]}},be={class:"policy-container app-container"},_e={class:"operation-btns"},Ce={key:1,class:"loading-placeholder"},xe=ue({name:"PolicyInfo"}),Pe=Object.assign(xe,{setup(P){const{proxy:n}=de(),b=l([]),w=l(!0),x=l([]),W=l(!0),V=l(!0),R=l(0),p=l(!0),N=l([]),O=l([]),S=l(!1),k=l(!1),D=l({dialogWidth:"800px",dialogHeight:"60vh"}),A=l(null),m=l(null),F=l([]),f=l({}),q=pe({queryParams:{pageNum:1,pageSize:10,policyName:void 0,policyType:void 0,status:void 0}}),{queryParams:o}=me(q);fe(async()=>{await B(),r()});const B=async()=>{try{const e=ve(n),a=await se({baseOption:e,proxy:n}),{tableColumns:t,searchColumns:s,formFields:T,formOptions:h}=ie(a);N.value=t,O.value=s,F.value=T,D.value={...D.value,...h},k.value=!0}catch(e){k.value=!1,console.error("初始化配置失败:",e)}};function r(){S.value=!0,w.value=!0;let e={...o.value};f.value.createTime&&Array.isArray(f.value.createTime)&&f.value.createTime.length===2&&(e=n.addDateRange(e,f.value.createTime)),te(e).then(a=>{S.value=!1,w.value=!1,b.value=a.rows,R.value=a.total,he(()=>{p.value=!1})})}const E=e=>{p.value=!0,f.value={...e};const{createTime:a,...t}=e||{};Object.assign(o.value,t),o.value.pageNum=1,r()},j=()=>{p.value=!0,o.value={pageNum:1,pageSize:10,policyName:void 0,policyType:void 0,status:void 0},f.value={},r()},U=e=>{p.value=!0,o.value.pageNum=e,r()},H=e=>{p.value=!0,o.value.pageSize=e,o.value.pageNum=1,r()};function G(e){const a=e.policyId||x.value;n.$modal.confirm('是否确认删除政策编号为"'+a+'"的数据项？').then(function(){return le(a)}).then(()=>{r(),n.$modal.msgSuccess("删除成功")}).catch(()=>{})}function K(){n.download("policy/info/export",{...o.value},`policy_info_${new Date().getTime()}.xlsx`)}function M(e){if(p.value)return;let a=e.status==="0"?"启用":"停用";n.$modal.confirm('确认要"'+a+'""'+e.policyName+'"政策吗?').then(function(){return z(e)}).then(()=>{n.$modal.msgSuccess(a+"成功")}).catch(function(){e.status=e.status==="0"?"1":"0"})}function J(e){x.value=e.map(a=>a.policyId),W.value=e.length!=1,V.value=!e.length}const Q=e=>{var a;(a=m.value)==null||a.openDialog("view","查看政策",e)},$=e=>{const a=e.policyId;oe(a).then(t=>{var s;(s=m.value)==null||s.openDialog("edit","编辑政策",t.data)})},X=()=>{var a;const e={status:"0"};(a=m.value)==null||a.openDialog("add","新增政策",e)},Y=async e=>{var a,t;try{e.type==="add"?(await ne(e.data),n.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await z(e.data),n.$modal.msgSuccess("修改成功")),(a=m.value)==null||a.onSubmitSuccess(),r()}catch(s){(t=m.value)==null||t.onSubmitError(),console.error("提交失败:",s)}},Z=()=>{};function ee(){X()}function ae(e){if(e)$(e);else{const a=x.value[0],t=b.value.find(s=>s.policyId===a);t&&$(t)}}return(e,a)=>{const t=I("el-button"),s=I("el-switch"),T=I("el-empty"),h=ge("hasPermi");return d(),L("div",be,[i(k)?(d(),y(ce,{key:0,columns:i(N),data:i(b),loading:i(S),showIndex:!0,searchColumns:i(O),showOperation:!0,operationLabel:"操作",operationWidth:"200",fixedOperation:!0,ref_key:"tableListRef",ref:A,onSearch:E,onReset:j,defaultPage:{pageSize:i(o).pageSize,currentPage:i(o).pageNum,total:i(R)},onCurrentChange:U,onSizeChange:H,onSelectionChange:J},{"menu-left":c(()=>[C((d(),y(t,{type:"primary",class:"custom-btn",onClick:ee},{default:c(()=>[v("新 增")]),_:1})),[[h,["policy:info:add"]]]),C((d(),y(t,{type:"warning",plain:"",class:"custom-btn",onClick:K},{default:c(()=>[v("导 出")]),_:1})),[[h,["policy:info:export"]]])]),status:c(({row:u})=>[_(s,{modelValue:u.status,"onUpdate:modelValue":g=>u.status=g,"active-value":"0","inactive-value":"1",onChange:g=>M(u)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),menu:c(({row:u})=>[ye("div",_e,[_(t,{type:"primary",link:"",onClick:g=>Q(u)},{default:c(()=>[v("查看")]),_:2},1032,["onClick"]),C((d(),y(t,{type:"primary",link:"",onClick:g=>ae(u)},{default:c(()=>[v("编辑")]),_:2},1032,["onClick"])),[[h,["policy:info:edit"]]]),C((d(),y(t,{type:"danger",link:"",onClick:g=>G(u)},{default:c(()=>[v("删除")]),_:2},1032,["onClick"])),[[h,["policy:info:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(d(),L("div",Ce,[_(T,{description:"正在加载表格配置..."})])),_(re,{ref_key:"policyFormDialogRef",ref:m,formFields:i(F),formOption:i(D),onSubmit:Y,onCancel:Z},null,8,["formFields","formOption"])])}}});export{Pe as default};
