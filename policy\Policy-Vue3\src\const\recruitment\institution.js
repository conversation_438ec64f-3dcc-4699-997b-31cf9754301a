/**
 * 机构招募管理表格配置
 */
export function createInstitutionRecruitmentTableOption(proxy) {
  return {
    // 表格列配置
    column: [
      {
        label: "招募标题",
        prop: "recruitmentTitle",
        minWidth: 200,
        search: true,
        searchType: "input",
        searchPlaceholder: "请输入招募标题",
        showOverflowTooltip: true
      },
      {
        label: "培训类别",
        prop: "trainingCategory",
        width: 120,
        search: true,
        searchType: "select",
        searchPlaceholder: "请选择培训类别",
        dicData: [
          { label: "IT技能", value: "IT技能" },
          { label: "管理培训", value: "管理培训" },
          { label: "职业技能", value: "职业技能" },
          { label: "语言培训", value: "语言培训" },
          { label: "财务培训", value: "财务培训" },
          { label: "营销培训", value: "营销培训" },
          { label: "其他", value: "其他" }
        ]
      },
      {
        label: "培训级别",
        prop: "trainingLevel",
        width: 100,
        search: true,
        searchType: "select",
        searchPlaceholder: "请选择培训级别",
        dicData: [
          { label: "初级", value: "初级" },
          { label: "中级", value: "中级" },
          { label: "高级", value: "高级" }
        ]
      },
      {
        label: "培训时长",
        prop: "trainingDuration",
        width: 100,
        formatter: (row) => {
          return row.trainingDuration ? `${row.trainingDuration}小时` : '-'
        }
      },
      {
        label: "参与人数",
        prop: "maxParticipants",
        width: 100,
        formatter: (row) => {
          return row.maxParticipants ? `${row.maxParticipants}人` : '-'
        }
      },
      {
        label: "培训费用",
        prop: "trainingFee",
        width: 150,
        slot: true // 使用插槽显示费用范围
      },
      {
        label: "培训地点",
        prop: "trainingLocation",
        minWidth: 150,
        showOverflowTooltip: true
      },
      {
        label: "线上支持",
        prop: "onlineSupport",
        width: 80,
        formatter: (row) => {
          return row.onlineSupport === '1' ? '是' : '否'
        }
      },
      {
        label: "线下支持",
        prop: "offlineSupport",
        width: 80,
        formatter: (row) => {
          return row.offlineSupport === '1' ? '是' : '否'
        }
      },
      {
        label: "联系人",
        prop: "contactPerson",
        width: 100,
        search: true,
        searchType: "input",
        searchPlaceholder: "请输入联系人"
      },
      {
        label: "联系电话",
        prop: "contactPhone",
        width: 120
      },
      {
        label: "申请截止时间",
        prop: "applicationEndDate",
        width: 160,
        formatter: (row) => {
          return row.applicationEndDate ? proxy.parseTime(row.applicationEndDate, '{y}-{m}-{d} {h}:{i}') : '-'
        }
      },
      {
        label: "预期开始时间",
        prop: "expectedStartDate",
        width: 160,
        formatter: (row) => {
          return row.expectedStartDate ? proxy.parseTime(row.expectedStartDate, '{y}-{m}-{d} {h}:{i}') : '-'
        }
      },
      {
        label: "招募状态",
        prop: "recruitmentStatus",
        width: 100,
        search: true,
        searchType: "select",
        searchPlaceholder: "请选择招募状态",
        slot: true, // 使用插槽显示状态标签
        dicData: [
          { label: "草稿", value: "0" },
          { label: "发布", value: "1" },
          { label: "进行中", value: "2" },
          { label: "已完成", value: "3" },
          { label: "已取消", value: "4" }
        ]
      },
      {
        label: "是否推荐",
        prop: "isFeatured",
        width: 100,
        search: true,
        searchType: "select",
        searchPlaceholder: "请选择是否推荐",
        slot: true, // 使用插槽显示开关
        dicData: [
          { label: "是", value: "1" },
          { label: "否", value: "0" }
        ]
      },
      {
        label: "是否紧急",
        prop: "isUrgent",
        width: 100,
        search: true,
        searchType: "select",
        searchPlaceholder: "请选择是否紧急",
        slot: true, // 使用插槽显示开关
        dicData: [
          { label: "是", value: "1" },
          { label: "否", value: "0" }
        ]
      },
      {
        label: "浏览次数",
        prop: "viewCount",
        width: 100,
        formatter: (row) => {
          return row.viewCount || 0
        }
      },
      {
        label: "申请次数",
        prop: "applicationCount",
        width: 100,
        formatter: (row) => {
          return row.applicationCount || 0
        }
      },
      {
        label: "已选中数量",
        prop: "selectedCount",
        width: 100,
        formatter: (row) => {
          return row.selectedCount || 0
        }
      },
      {
        label: "预算金额",
        prop: "budgetAmount",
        width: 120,
        formatter: (row) => {
          return row.budgetAmount ? `¥${row.budgetAmount}` : '-'
        }
      },
      {
        label: "付款方式",
        prop: "paymentMethod",
        width: 120
      },
      {
        label: "创建时间",
        prop: "createTime",
        width: 160,
        search: true,
        searchType: "daterange",
        searchPlaceholder: "请选择创建时间",
        formatter: (row) => {
          return row.createTime ? proxy.parseTime(row.createTime, '{y}-{m}-{d} {h}:{i}') : '-'
        }
      },
      {
        label: "备注",
        prop: "remark",
        minWidth: 150,
        showOverflowTooltip: true
      }
    ],
    
    // 表单配置
    formOption: {
      dialogWidth: '1200px',
      dialogHeight: '80vh',
      labelWidth: '120px',
      gutter: 20,
      submitBtn: true,
      emptyBtn: true,
      menuBtn: true,
      submitText: '确定',
      emptyText: '重置'
    },
    
    // 表单字段配置
    formFields: [
      {
        label: "招募标题",
        prop: "recruitmentTitle",
        type: "input",
        placeholder: "请输入招募标题",
        rules: [
          { required: true, message: "招募标题不能为空", trigger: "blur" }
        ]
      },
      {
        label: "培训类别",
        prop: "trainingCategory",
        type: "select",
        placeholder: "请选择培训类别",
        dicData: [
          { label: "IT技能", value: "IT技能" },
          { label: "管理培训", value: "管理培训" },
          { label: "职业技能", value: "职业技能" },
          { label: "语言培训", value: "语言培训" },
          { label: "财务培训", value: "财务培训" },
          { label: "营销培训", value: "营销培训" },
          { label: "其他", value: "其他" }
        ],
        rules: [
          { required: true, message: "培训类别不能为空", trigger: "change" }
        ]
      },
      {
        label: "培训级别",
        prop: "trainingLevel",
        type: "select",
        placeholder: "请选择培训级别",
        dicData: [
          { label: "初级", value: "初级" },
          { label: "中级", value: "中级" },
          { label: "高级", value: "高级" }
        ]
      },
      {
        label: "培训时长(小时)",
        prop: "trainingDuration",
        type: "number",
        placeholder: "请输入培训时长",
        min: 1,
        max: 1000
      },
      {
        label: "最大参与人数",
        prop: "maxParticipants",
        type: "number",
        placeholder: "请输入最大参与人数",
        min: 1,
        max: 1000
      },
      {
        label: "培训费用最低价",
        prop: "trainingFeeMin",
        type: "number",
        placeholder: "请输入最低价",
        min: 0,
        precision: 2
      },
      {
        label: "培训费用最高价",
        prop: "trainingFeeMax",
        type: "number",
        placeholder: "请输入最高价",
        min: 0,
        precision: 2
      },
      {
        label: "培训地点",
        prop: "trainingLocation",
        type: "input",
        placeholder: "请输入培训地点"
      },
      {
        label: "招募描述",
        prop: "recruitmentDescription",
        type: "textarea",
        placeholder: "请输入招募描述",
        rows: 3
      },
      {
        label: "支持线上培训",
        prop: "onlineSupport",
        type: "switch",
        activeValue: "1",
        inactiveValue: "0"
      },
      {
        label: "支持线下培训",
        prop: "offlineSupport",
        type: "switch",
        activeValue: "1",
        inactiveValue: "0"
      },
      {
        label: "机构资质要求",
        prop: "qualificationRequirements",
        type: "textarea",
        placeholder: "请输入机构资质要求",
        rows: 3
      },
      {
        label: "经验要求",
        prop: "experienceRequirements",
        type: "textarea",
        placeholder: "请输入经验要求",
        rows: 3
      },
      {
        label: "师资要求",
        prop: "teacherRequirements",
        type: "textarea",
        placeholder: "请输入师资要求",
        rows: 3
      },
      {
        label: "设施设备要求",
        prop: "facilityRequirements",
        type: "textarea",
        placeholder: "请输入设施设备要求",
        rows: 3
      },
      {
        label: "联系人",
        prop: "contactPerson",
        type: "input",
        placeholder: "请输入联系人",
        rules: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ]
      },
      {
        label: "联系电话",
        prop: "contactPhone",
        type: "input",
        placeholder: "请输入联系电话",
        rules: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
        ]
      },
      {
        label: "联系邮箱",
        prop: "contactEmail",
        type: "input",
        placeholder: "请输入联系邮箱",
        rules: [
          { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
        ]
      },
      {
        label: "申请开始时间",
        prop: "applicationStartDate",
        type: "datetime",
        placeholder: "请选择申请开始时间"
      },
      {
        label: "申请截止时间",
        prop: "applicationEndDate",
        type: "datetime",
        placeholder: "请选择申请截止时间"
      },
      {
        label: "预期开始时间",
        prop: "expectedStartDate",
        type: "datetime",
        placeholder: "请选择预期开始时间"
      },
      {
        label: "预期结束时间",
        prop: "expectedEndDate",
        type: "datetime",
        placeholder: "请选择预期结束时间"
      },
      {
        label: "预算金额",
        prop: "budgetAmount",
        type: "number",
        placeholder: "请输入预算金额",
        min: 0,
        precision: 2
      },
      {
        label: "付款方式",
        prop: "paymentMethod",
        type: "select",
        placeholder: "请选择付款方式",
        dicData: [
          { label: "一次性付款", value: "一次性付款" },
          { label: "分期付款", value: "分期付款" },
          { label: "按阶段付款", value: "按阶段付款" },
          { label: "培训后付款", value: "培训后付款" }
        ]
      },
      {
        label: "评选标准",
        prop: "evaluationCriteria",
        type: "textarea",
        placeholder: "请输入评选标准",
        rows: 3
      },
      {
        label: "其他要求",
        prop: "additionalRequirements",
        type: "textarea",
        placeholder: "请输入其他要求",
        rows: 3
      },
      {
        label: "是否推荐",
        prop: "isFeatured",
        type: "switch",
        activeValue: "1",
        inactiveValue: "0"
      },
      {
        label: "是否紧急",
        prop: "isUrgent",
        type: "switch",
        activeValue: "1",
        inactiveValue: "0"
      },
      {
        label: "备注",
        prop: "remark",
        type: "textarea",
        placeholder: "请输入备注",
        rows: 2
      }
    ]
  }
}
