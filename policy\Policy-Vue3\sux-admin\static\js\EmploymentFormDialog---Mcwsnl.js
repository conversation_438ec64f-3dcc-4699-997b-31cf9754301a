import{_ as P,r as g,D as q,A as D,f as r,l as F,o as B,a2 as O,k as l,h as z,i as e,p as V}from"./index-DP10CBaW.js";const E={class:"dialog-footer"},R={__name:"EmploymentFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(y,{expose:h,emit:U}){const _=U,m=g(!1),u=g("add"),v=g(null),a=q({employmentId:null,title:"",employmentType:"",workCategory:"",workLocation:"",salaryType:"",salaryMin:null,salaryMax:null,positionsNeeded:1,positionsFilled:0,companyName:"",contactPerson:"",contactPhone:"",urgencyLevel:"normal",status:"draft",workDescription:"",welfareBenefits:""}),x={title:[{required:!0,message:"用工标题不能为空",trigger:"blur"}],employmentType:[{required:!0,message:"用工类型不能为空",trigger:"change"}],workCategory:[{required:!0,message:"工作类别不能为空",trigger:"change"}],workLocation:[{required:!0,message:"工作地点不能为空",trigger:"blur"}],salaryType:[{required:!0,message:"薪资类型不能为空",trigger:"change"}],positionsNeeded:[{required:!0,message:"需要人数不能为空",trigger:"blur"}],companyName:[{required:!0,message:"公司名称不能为空",trigger:"blur"}],contactPerson:[{required:!0,message:"联系人不能为空",trigger:"blur"}],contactPhone:[{required:!0,message:"联系电话不能为空",trigger:"blur"}]},N=D(()=>({add:"新增用工信息",edit:"编辑用工信息",view:"查看用工信息"})[u.value]||"用工信息"),k=(f,o={})=>{u.value=f,m.value=!0,Object.keys(a).forEach(n=>{o[n]!==void 0?a[n]=o[n]:n==="positionsNeeded"?a[n]=1:n==="positionsFilled"?a[n]=0:n==="urgencyLevel"?a[n]="normal":n==="status"?a[n]="draft":a[n]=null}),v.value&&v.value.clearValidate()},T=()=>{u.value!=="view"&&v.value.validate(f=>{f&&(_("submit",{...a},u.value),m.value=!1)})},C=()=>{m.value=!1,_("cancel")};return h({openDialog:k}),(f,o)=>{const n=r("el-input"),s=r("el-form-item"),i=r("el-col"),d=r("el-option"),b=r("el-select"),p=r("el-row"),c=r("el-input-number"),L=r("el-form"),w=r("el-button"),M=r("el-dialog");return B(),F(M,{title:N.value,modelValue:m.value,"onUpdate:modelValue":o[16]||(o[16]=t=>m.value=t),width:y.formOption.dialogWidth||"1200px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},O({default:l(()=>[e(L,{ref_key:"formRef",ref:v,model:a,rules:x,"label-width":y.formOption.labelWidth||"120px",size:y.formOption.size||"default"},{default:l(()=>[e(p,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"用工标题",prop:"title"},{default:l(()=>[e(n,{modelValue:a.title,"onUpdate:modelValue":o[0]||(o[0]=t=>a.title=t),placeholder:"请输入用工标题",disabled:u.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"用工类型",prop:"employmentType"},{default:l(()=>[e(b,{modelValue:a.employmentType,"onUpdate:modelValue":o[1]||(o[1]=t=>a.employmentType=t),placeholder:"请选择用工类型",disabled:u.value==="view",style:{width:"100%"}},{default:l(()=>[e(d,{label:"日结",value:"日结"}),e(d,{label:"周结",value:"周结"}),e(d,{label:"月结",value:"月结"}),e(d,{label:"计件",value:"计件"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"工作类别",prop:"workCategory"},{default:l(()=>[e(b,{modelValue:a.workCategory,"onUpdate:modelValue":o[2]||(o[2]=t=>a.workCategory=t),placeholder:"请选择工作类别",disabled:u.value==="view",style:{width:"100%"}},{default:l(()=>[e(d,{label:"服务员",value:"服务员"}),e(d,{label:"保洁",value:"保洁"}),e(d,{label:"搬运工",value:"搬运工"}),e(d,{label:"销售",value:"销售"}),e(d,{label:"厨师助手",value:"厨师助手"}),e(d,{label:"快递员",value:"快递员"}),e(d,{label:"保安",value:"保安"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"工作地点",prop:"workLocation"},{default:l(()=>[e(n,{modelValue:a.workLocation,"onUpdate:modelValue":o[3]||(o[3]=t=>a.workLocation=t),placeholder:"请输入工作地点",disabled:u.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(s,{label:"薪资类型",prop:"salaryType"},{default:l(()=>[e(b,{modelValue:a.salaryType,"onUpdate:modelValue":o[4]||(o[4]=t=>a.salaryType=t),placeholder:"请选择薪资类型",disabled:u.value==="view",style:{width:"100%"}},{default:l(()=>[e(d,{label:"小时薪",value:"hourly"}),e(d,{label:"日薪",value:"daily"}),e(d,{label:"月薪",value:"monthly"}),e(d,{label:"计件",value:"piece"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(s,{label:"最低薪资",prop:"salaryMin"},{default:l(()=>[e(c,{modelValue:a.salaryMin,"onUpdate:modelValue":o[5]||(o[5]=t=>a.salaryMin=t),placeholder:"请输入最低薪资",disabled:u.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(s,{label:"最高薪资",prop:"salaryMax"},{default:l(()=>[e(c,{modelValue:a.salaryMax,"onUpdate:modelValue":o[6]||(o[6]=t=>a.salaryMax=t),placeholder:"请输入最高薪资",disabled:u.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(i,{span:8},{default:l(()=>[e(s,{label:"需要人数",prop:"positionsNeeded"},{default:l(()=>[e(c,{modelValue:a.positionsNeeded,"onUpdate:modelValue":o[7]||(o[7]=t=>a.positionsNeeded=t),placeholder:"请输入需要人数",disabled:u.value==="view",style:{width:"100%"},min:1},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(s,{label:"已招人数",prop:"positionsFilled"},{default:l(()=>[e(c,{modelValue:a.positionsFilled,"onUpdate:modelValue":o[8]||(o[8]=t=>a.positionsFilled=t),placeholder:"请输入已招人数",disabled:u.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:8},{default:l(()=>[e(s,{label:"紧急程度",prop:"urgencyLevel"},{default:l(()=>[e(b,{modelValue:a.urgencyLevel,"onUpdate:modelValue":o[9]||(o[9]=t=>a.urgencyLevel=t),placeholder:"请选择紧急程度",disabled:u.value==="view",style:{width:"100%"}},{default:l(()=>[e(d,{label:"紧急",value:"urgent"}),e(d,{label:"高",value:"high"}),e(d,{label:"普通",value:"normal"}),e(d,{label:"低",value:"low"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"公司名称",prop:"companyName"},{default:l(()=>[e(n,{modelValue:a.companyName,"onUpdate:modelValue":o[10]||(o[10]=t=>a.companyName=t),placeholder:"请输入公司名称",disabled:u.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"联系人",prop:"contactPerson"},{default:l(()=>[e(n,{modelValue:a.contactPerson,"onUpdate:modelValue":o[11]||(o[11]=t=>a.contactPerson=t),placeholder:"请输入联系人",disabled:u.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(i,{span:12},{default:l(()=>[e(s,{label:"联系电话",prop:"contactPhone"},{default:l(()=>[e(n,{modelValue:a.contactPhone,"onUpdate:modelValue":o[12]||(o[12]=t=>a.contactPhone=t),placeholder:"请输入联系电话",disabled:u.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(i,{span:12},{default:l(()=>[e(s,{label:"状态",prop:"status"},{default:l(()=>[e(b,{modelValue:a.status,"onUpdate:modelValue":o[13]||(o[13]=t=>a.status=t),placeholder:"请选择状态",disabled:u.value==="view",style:{width:"100%"}},{default:l(()=>[e(d,{label:"草稿",value:"draft"}),e(d,{label:"已发布",value:"published"}),e(d,{label:"已暂停",value:"paused"}),e(d,{label:"已关闭",value:"closed"}),e(d,{label:"已完成",value:"completed"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:l(()=>[e(i,{span:24},{default:l(()=>[e(s,{label:"工作描述",prop:"workDescription"},{default:l(()=>[e(n,{modelValue:a.workDescription,"onUpdate:modelValue":o[14]||(o[14]=t=>a.workDescription=t),type:"textarea",rows:3,placeholder:"请输入工作描述",disabled:u.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:l(()=>[e(i,{span:24},{default:l(()=>[e(s,{label:"福利待遇",prop:"welfareBenefits"},{default:l(()=>[e(n,{modelValue:a.welfareBenefits,"onUpdate:modelValue":o[15]||(o[15]=t=>a.welfareBenefits=t),type:"textarea",rows:3,placeholder:"请输入福利待遇",disabled:u.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","label-width","size"])]),_:2},[u.value!=="view"?{name:"footer",fn:l(()=>[z("div",E,[e(w,{onClick:C},{default:l(()=>[V("取 消")]),_:1}),e(w,{type:"primary",onClick:T},{default:l(()=>[V("确 定")]),_:1})])]),key:"0"}:void 0]),1032,["title","modelValue","width"])}}},A=P(R,[["__scopeId","data-v-7c253c02"]]);export{A as default};
