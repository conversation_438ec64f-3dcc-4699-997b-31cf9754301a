package com.sux.system.service.impl;

import java.util.List;

import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.system.domain.OnlineTrainingInstitutionRecruitment;
import com.sux.system.mapper.OnlineTrainingInstitutionRecruitmentMapper;
import com.sux.system.service.IOnlineTrainingInstitutionRecruitmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 线上招募培训机构发布Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class OnlineTrainingInstitutionRecruitmentServiceImpl implements IOnlineTrainingInstitutionRecruitmentService
{
    @Autowired
    private OnlineTrainingInstitutionRecruitmentMapper onlineTrainingInstitutionRecruitmentMapper;

    /**
     * 查询线上招募培训机构发布
     * 
     * @param recruitmentId 线上招募培训机构发布主键
     * @return 线上招募培训机构发布
     */
    @Override
    public OnlineTrainingInstitutionRecruitment selectOnlineTrainingInstitutionRecruitmentByRecruitmentId(Long recruitmentId)
    {
        return onlineTrainingInstitutionRecruitmentMapper.selectOnlineTrainingInstitutionRecruitmentByRecruitmentId(recruitmentId);
    }

    /**
     * 查询线上招募培训机构发布列表
     * 
     * @param onlineTrainingInstitutionRecruitment 线上招募培训机构发布
     * @return 线上招募培训机构发布
     */
    @Override
    public List<OnlineTrainingInstitutionRecruitment> selectOnlineTrainingInstitutionRecruitmentList(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        return onlineTrainingInstitutionRecruitmentMapper.selectOnlineTrainingInstitutionRecruitmentList(onlineTrainingInstitutionRecruitment);
    }

    /**
     * 新增线上招募培训机构发布
     * 
     * @param onlineTrainingInstitutionRecruitment 线上招募培训机构发布
     * @return 结果
     */
    @Override
    public int insertOnlineTrainingInstitutionRecruitment(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        onlineTrainingInstitutionRecruitment.setCreateTime(DateUtils.getNowDate());
        onlineTrainingInstitutionRecruitment.setCreateId(SecurityUtils.getUserId());
        onlineTrainingInstitutionRecruitment.setPublisherUserId(SecurityUtils.getUserId());
        // 设置默认值
        if (onlineTrainingInstitutionRecruitment.getViewCount() == null) {
            onlineTrainingInstitutionRecruitment.setViewCount(0);
        }
        if (onlineTrainingInstitutionRecruitment.getApplicationCount() == null) {
            onlineTrainingInstitutionRecruitment.setApplicationCount(0);
        }
        if (onlineTrainingInstitutionRecruitment.getSelectedCount() == null) {
            onlineTrainingInstitutionRecruitment.setSelectedCount(0);
        }
        if (onlineTrainingInstitutionRecruitment.getRecruitmentStatus() == null) {
            onlineTrainingInstitutionRecruitment.setRecruitmentStatus("0"); // 默认草稿状态
        }
        if (onlineTrainingInstitutionRecruitment.getIsFeatured() == null) {
            onlineTrainingInstitutionRecruitment.setIsFeatured("0");
        }
        if (onlineTrainingInstitutionRecruitment.getIsUrgent() == null) {
            onlineTrainingInstitutionRecruitment.setIsUrgent("0");
        }
        if (onlineTrainingInstitutionRecruitment.getOnlineSupport() == null) {
            onlineTrainingInstitutionRecruitment.setOnlineSupport("0");
        }
        if (onlineTrainingInstitutionRecruitment.getOfflineSupport() == null) {
            onlineTrainingInstitutionRecruitment.setOfflineSupport("0");
        }
        return onlineTrainingInstitutionRecruitmentMapper.insertOnlineTrainingInstitutionRecruitment(onlineTrainingInstitutionRecruitment);
    }

    /**
     * 修改线上招募培训机构发布
     * 
     * @param onlineTrainingInstitutionRecruitment 线上招募培训机构发布
     * @return 结果
     */
    @Override
    public int updateOnlineTrainingInstitutionRecruitment(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        onlineTrainingInstitutionRecruitment.setUpdateTime(DateUtils.getNowDate());
        onlineTrainingInstitutionRecruitment.setUpdateId(SecurityUtils.getUserId());
        return onlineTrainingInstitutionRecruitmentMapper.updateOnlineTrainingInstitutionRecruitment(onlineTrainingInstitutionRecruitment);
    }

    /**
     * 批量删除线上招募培训机构发布
     * 
     * @param recruitmentIds 需要删除的线上招募培训机构发布主键
     * @return 结果
     */
    @Override
    public int deleteOnlineTrainingInstitutionRecruitmentByRecruitmentIds(Long[] recruitmentIds)
    {
        return onlineTrainingInstitutionRecruitmentMapper.deleteOnlineTrainingInstitutionRecruitmentByRecruitmentIds(recruitmentIds);
    }

    /**
     * 删除线上招募培训机构发布信息
     * 
     * @param recruitmentId 线上招募培训机构发布主键
     * @return 结果
     */
    @Override
    public int deleteOnlineTrainingInstitutionRecruitmentByRecruitmentId(Long recruitmentId)
    {
        return onlineTrainingInstitutionRecruitmentMapper.deleteOnlineTrainingInstitutionRecruitmentByRecruitmentId(recruitmentId);
    }

    /**
     * 发布招募信息
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    @Override
    public int publishRecruitment(Long recruitmentId)
    {
        return onlineTrainingInstitutionRecruitmentMapper.publishRecruitment(recruitmentId);
    }

    /**
     * 取消招募信息
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    @Override
    public int cancelRecruitment(Long recruitmentId)
    {
        return onlineTrainingInstitutionRecruitmentMapper.cancelRecruitment(recruitmentId);
    }

    /**
     * 增加浏览次数
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    @Override
    public int incrementViewCount(Long recruitmentId)
    {
        return onlineTrainingInstitutionRecruitmentMapper.incrementViewCount(recruitmentId);
    }

    /**
     * 增加申请次数
     * 
     * @param recruitmentId 招募ID
     * @return 结果
     */
    @Override
    public int incrementApplicationCount(Long recruitmentId)
    {
        return onlineTrainingInstitutionRecruitmentMapper.incrementApplicationCount(recruitmentId);
    }

    /**
     * 更新选中机构数量
     * 
     * @param recruitmentId 招募ID
     * @param selectedCount 选中数量
     * @return 结果
     */
    @Override
    public int updateSelectedCount(Long recruitmentId, Integer selectedCount)
    {
        return onlineTrainingInstitutionRecruitmentMapper.updateSelectedCount(recruitmentId, selectedCount);
    }

    /**
     * 获取招募统计信息
     * 
     * @return 统计信息
     */
    @Override
    public List<OnlineTrainingInstitutionRecruitment> selectRecruitmentStats()
    {
        return onlineTrainingInstitutionRecruitmentMapper.selectRecruitmentStats();
    }

    /**
     * 批量更新招募状态
     * 
     * @param recruitmentIds 招募ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateRecruitmentStatus(Long[] recruitmentIds, String status)
    {
        return onlineTrainingInstitutionRecruitmentMapper.batchUpdateRecruitmentStatus(recruitmentIds, status);
    }
}
