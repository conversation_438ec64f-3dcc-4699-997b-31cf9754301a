import{_ as X,C as Y,a8 as j,A as F,d as z,r as o,f as x,l as p,o as g,k as v,h as _,Z as I,N as P,i as D,m as R,p as b,t as k}from"./index-DP10CBaW.js";import{F as Z}from"./index-BylsdGrt.js";import{V as q}from"./index-B-A7bGAb.js";const E={class:"dialog-footer"},G=Y({name:"PostFormDialog"}),J=Object.assign(G,{props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({dialogWidth:"600px",dialogHeight:"60vh"})}},emits:["submit","cancel"],setup(c,{expose:O,emit:W}){j(e=>({"028893b0":r.value.maxHeight,"7a006e27":r.value.minHeight||"auto","0761fb14":r.value.overflowY,db9e3464:r.value.padding}));const{proxy:K}=z(),u=c,w=W,m=o(!1),l=o("add"),y=o("新增岗位"),h=o(null),t=o({}),s=o(!1),i=o(!1),n=o(!1),C=F(()=>u.formFields.length?l.value==="add"?u.formFields.filter(e=>e.addDisplay!==!1):l.value==="edit"?u.formFields.filter(e=>e.editDisplay!==!1):u.formFields.filter(e=>e.viewDisplay!==!1):[]),r=F(()=>{const e={overflow:"visible",padding:"20px 10px",overflowX:"hidden"};return n.value?{...e,maxHeight:"calc(100vh - 180px)",overflowY:"auto",overflowX:"hidden"}:{...e,maxHeight:u.formOption.dialogHeight||"60vh",overflowY:"auto",overflowX:"hidden",minHeight:"auto"}}),H=()=>{n.value=!n.value},S=(e,a,d={})=>{l.value=e,y.value=a,t.value={...d},e==="add"&&(t.value={postSort:0,status:"0",...d}),m.value=!0},V=()=>{m.value=!1},L=(e,a)=>{},N=async()=>{if(!(s.value||i.value)&&h.value)try{s.value=!0,i.value=!0,await h.value.validate(),w("submit",{type:l.value,data:t.value})}catch{s.value=!1,i.value=!1}},A=()=>{w("cancel"),V()},B=()=>{s.value=!1,i.value=!1},T=()=>{s.value=!1,i.value=!1,n.value=!1,t.value={}};return O({openDialog:S,closeDialog:V}),(e,a)=>{const d=x("el-button"),U=x("el-dialog");return g(),p(U,{modelValue:m.value,"onUpdate:modelValue":a[2]||(a[2]=f=>m.value=f),title:y.value,width:c.formOption.dialogWidth,"destroy-on-close":"","close-on-click-modal":!1,fullscreen:n.value,onClosed:T,onOpen:B,class:"custom-dialog"},{footer:v(()=>[_("span",E,[D(d,{class:"custom-btn",onClick:H},{default:v(()=>[b(k(n.value?"退出全屏":"全屏显示"),1)]),_:1}),D(d,{class:"custom-btn",onClick:A},{default:v(()=>[b(k(l.value==="view"?"关闭":"取消"),1)]),_:1}),l.value!=="view"?(g(),p(d,{key:0,type:"primary",class:"custom-btn",onClick:N,loading:s.value,disabled:i.value},{default:v(()=>[b(" 确 认 ")]),_:1},8,["loading","disabled"])):R("",!0)])]),default:v(()=>[_("div",{class:P(["dialog-content",{"view-mode":l.value==="view"}]),style:I(r.value)},[l.value!=="view"?(g(),p(Z,{key:0,ref_key:"formListRef",ref:h,modelValue:t.value,"onUpdate:modelValue":a[0]||(a[0]=f=>t.value=f),fields:C.value,"is-view":l.value==="view","is-edit":l.value==="edit",showActions:!1,labelWidth:c.formOption.labelWidth,inline:!1,onFieldChange:L},null,8,["modelValue","fields","is-view","is-edit","labelWidth"])):(g(),p(q,{key:1,modelValue:t.value,"onUpdate:modelValue":a[1]||(a[1]=f=>t.value=f),fields:C.value,labelWidth:c.formOption.labelWidth},null,8,["modelValue","fields","labelWidth"]))],6)]),_:1},8,["modelValue","title","width","fullscreen"])}}}),ee=X(J,[["__scopeId","data-v-0c2fa0fe"]]);export{ee as default};
