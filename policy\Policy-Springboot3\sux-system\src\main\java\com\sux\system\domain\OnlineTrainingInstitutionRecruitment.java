package com.sux.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 线上招募培训机构发布对象 online_training_institution_recruitment
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class OnlineTrainingInstitutionRecruitment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 招募ID */
    private Long recruitmentId;

    /** 招募标题 */
    @Excel(name = "招募标题")
    private String recruitmentTitle;

    /** 招募描述 */
    @Excel(name = "招募描述")
    private String recruitmentDescription;

    /** 培训类别（IT技能/管理培训/职业技能等） */
    @Excel(name = "培训类别", readConverterExp = "I=T技能/管理培训/职业技能等")
    private String trainingCategory;

    /** 培训级别（初级/中级/高级） */
    @Excel(name = "培训级别", readConverterExp = "初=级/中级/高级")
    private String trainingLevel;

    /** 培训时长(小时) */
    @Excel(name = "培训时长(小时)")
    private Integer trainingDuration;

    /** 最大参与人数 */
    @Excel(name = "最大参与人数")
    private Integer maxParticipants;

    /** 培训费用最低价 */
    @Excel(name = "培训费用最低价")
    private BigDecimal trainingFeeMin;

    /** 培训费用最高价 */
    @Excel(name = "培训费用最高价")
    private BigDecimal trainingFeeMax;

    /** 培训地点 */
    @Excel(name = "培训地点")
    private String trainingLocation;

    /** 是否支持线上培训（0否 1是） */
    @Excel(name = "是否支持线上培训", readConverterExp = "0=否,1=是")
    private String onlineSupport;

    /** 是否支持线下培训（0否 1是） */
    @Excel(name = "是否支持线下培训", readConverterExp = "0=否,1=是")
    private String offlineSupport;

    /** 机构资质要求 */
    @Excel(name = "机构资质要求")
    private String qualificationRequirements;

    /** 经验要求 */
    @Excel(name = "经验要求")
    private String experienceRequirements;

    /** 师资要求 */
    @Excel(name = "师资要求")
    private String teacherRequirements;

    /** 设施设备要求 */
    @Excel(name = "设施设备要求")
    private String facilityRequirements;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    private String contactEmail;

    /** 申请开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationStartDate;

    /** 申请截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationEndDate;

    /** 预期培训开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预期培训开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expectedStartDate;

    /** 预期培训结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预期培训结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expectedEndDate;

    /** 招募状态（0草稿 1发布 2进行中 3已完成 4已取消） */
    @Excel(name = "招募状态", readConverterExp = "0=草稿,1=发布,2=进行中,3=已完成,4=已取消")
    private String recruitmentStatus;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private String isFeatured;

    /** 是否紧急（0否 1是） */
    @Excel(name = "是否紧急", readConverterExp = "0=否,1=是")
    private String isUrgent;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 申请次数 */
    @Excel(name = "申请次数")
    private Integer applicationCount;

    /** 已选中机构数量 */
    @Excel(name = "已选中机构数量")
    private Integer selectedCount;

    /** 预算金额 */
    @Excel(name = "预算金额")
    private BigDecimal budgetAmount;

    /** 付款方式（一次性/分期等） */
    @Excel(name = "付款方式", readConverterExp = "一=次性/分期等")
    private String paymentMethod;

    /** 评选标准 */
    @Excel(name = "评选标准")
    private String evaluationCriteria;

    /** 其他要求 */
    @Excel(name = "其他要求")
    private String additionalRequirements;

    /** 附件文件（JSON格式存储） */
    @Excel(name = "附件文件", readConverterExp = "J=SON格式存储")
    private String attachmentFiles;

    /** 发布者用户ID */
    @Excel(name = "发布者用户ID")
    private Long publisherUserId;

    public void setRecruitmentId(Long recruitmentId) 
    {
        this.recruitmentId = recruitmentId;
    }

    public Long getRecruitmentId() 
    {
        return recruitmentId;
    }
    public void setRecruitmentTitle(String recruitmentTitle) 
    {
        this.recruitmentTitle = recruitmentTitle;
    }

    public String getRecruitmentTitle() 
    {
        return recruitmentTitle;
    }
    public void setRecruitmentDescription(String recruitmentDescription) 
    {
        this.recruitmentDescription = recruitmentDescription;
    }

    public String getRecruitmentDescription() 
    {
        return recruitmentDescription;
    }
    public void setTrainingCategory(String trainingCategory) 
    {
        this.trainingCategory = trainingCategory;
    }

    public String getTrainingCategory() 
    {
        return trainingCategory;
    }
    public void setTrainingLevel(String trainingLevel) 
    {
        this.trainingLevel = trainingLevel;
    }

    public String getTrainingLevel() 
    {
        return trainingLevel;
    }
    public void setTrainingDuration(Integer trainingDuration)
    {
        this.trainingDuration = trainingDuration;
    }

    public Integer getTrainingDuration()
    {
        return trainingDuration;
    }
    public void setMaxParticipants(Integer maxParticipants)
    {
        this.maxParticipants = maxParticipants;
    }

    public Integer getMaxParticipants()
    {
        return maxParticipants;
    }
    public void setTrainingFeeMin(BigDecimal trainingFeeMin) 
    {
        this.trainingFeeMin = trainingFeeMin;
    }

    public BigDecimal getTrainingFeeMin() 
    {
        return trainingFeeMin;
    }
    public void setTrainingFeeMax(BigDecimal trainingFeeMax) 
    {
        this.trainingFeeMax = trainingFeeMax;
    }

    public BigDecimal getTrainingFeeMax() 
    {
        return trainingFeeMax;
    }
    public void setTrainingLocation(String trainingLocation) 
    {
        this.trainingLocation = trainingLocation;
    }

    public String getTrainingLocation() 
    {
        return trainingLocation;
    }
    public void setOnlineSupport(String onlineSupport) 
    {
        this.onlineSupport = onlineSupport;
    }

    public String getOnlineSupport() 
    {
        return onlineSupport;
    }
    public void setOfflineSupport(String offlineSupport) 
    {
        this.offlineSupport = offlineSupport;
    }

    public String getOfflineSupport() 
    {
        return offlineSupport;
    }
    public void setQualificationRequirements(String qualificationRequirements) 
    {
        this.qualificationRequirements = qualificationRequirements;
    }

    public String getQualificationRequirements() 
    {
        return qualificationRequirements;
    }
    public void setExperienceRequirements(String experienceRequirements) 
    {
        this.experienceRequirements = experienceRequirements;
    }

    public String getExperienceRequirements() 
    {
        return experienceRequirements;
    }
    public void setTeacherRequirements(String teacherRequirements) 
    {
        this.teacherRequirements = teacherRequirements;
    }

    public String getTeacherRequirements() 
    {
        return teacherRequirements;
    }
    public void setFacilityRequirements(String facilityRequirements) 
    {
        this.facilityRequirements = facilityRequirements;
    }

    public String getFacilityRequirements() 
    {
        return facilityRequirements;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }
    public void setApplicationStartDate(Date applicationStartDate) 
    {
        this.applicationStartDate = applicationStartDate;
    }

    public Date getApplicationStartDate() 
    {
        return applicationStartDate;
    }
    public void setApplicationEndDate(Date applicationEndDate) 
    {
        this.applicationEndDate = applicationEndDate;
    }

    public Date getApplicationEndDate() 
    {
        return applicationEndDate;
    }
    public void setExpectedStartDate(Date expectedStartDate) 
    {
        this.expectedStartDate = expectedStartDate;
    }

    public Date getExpectedStartDate() 
    {
        return expectedStartDate;
    }
    public void setExpectedEndDate(Date expectedEndDate) 
    {
        this.expectedEndDate = expectedEndDate;
    }

    public Date getExpectedEndDate() 
    {
        return expectedEndDate;
    }
    public void setRecruitmentStatus(String recruitmentStatus) 
    {
        this.recruitmentStatus = recruitmentStatus;
    }

    public String getRecruitmentStatus() 
    {
        return recruitmentStatus;
    }
    public void setIsFeatured(String isFeatured) 
    {
        this.isFeatured = isFeatured;
    }

    public String getIsFeatured() 
    {
        return isFeatured;
    }
    public void setIsUrgent(String isUrgent) 
    {
        this.isUrgent = isUrgent;
    }

    public String getIsUrgent() 
    {
        return isUrgent;
    }
    public void setViewCount(Integer viewCount)
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount()
    {
        return viewCount;
    }
    public void setApplicationCount(Integer applicationCount)
    {
        this.applicationCount = applicationCount;
    }

    public Integer getApplicationCount()
    {
        return applicationCount;
    }
    public void setSelectedCount(Integer selectedCount)
    {
        this.selectedCount = selectedCount;
    }

    public Integer getSelectedCount()
    {
        return selectedCount;
    }
    public void setBudgetAmount(BigDecimal budgetAmount) 
    {
        this.budgetAmount = budgetAmount;
    }

    public BigDecimal getBudgetAmount() 
    {
        return budgetAmount;
    }
    public void setPaymentMethod(String paymentMethod) 
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() 
    {
        return paymentMethod;
    }
    public void setEvaluationCriteria(String evaluationCriteria) 
    {
        this.evaluationCriteria = evaluationCriteria;
    }

    public String getEvaluationCriteria() 
    {
        return evaluationCriteria;
    }
    public void setAdditionalRequirements(String additionalRequirements) 
    {
        this.additionalRequirements = additionalRequirements;
    }

    public String getAdditionalRequirements() 
    {
        return additionalRequirements;
    }
    public void setAttachmentFiles(String attachmentFiles) 
    {
        this.attachmentFiles = attachmentFiles;
    }

    public String getAttachmentFiles() 
    {
        return attachmentFiles;
    }
    public void setPublisherUserId(Long publisherUserId) 
    {
        this.publisherUserId = publisherUserId;
    }

    public Long getPublisherUserId() 
    {
        return publisherUserId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recruitmentId", getRecruitmentId())
            .append("recruitmentTitle", getRecruitmentTitle())
            .append("recruitmentDescription", getRecruitmentDescription())
            .append("trainingCategory", getTrainingCategory())
            .append("trainingLevel", getTrainingLevel())
            .append("trainingDuration", getTrainingDuration())
            .append("maxParticipants", getMaxParticipants())
            .append("trainingFeeMin", getTrainingFeeMin())
            .append("trainingFeeMax", getTrainingFeeMax())
            .append("trainingLocation", getTrainingLocation())
            .append("onlineSupport", getOnlineSupport())
            .append("offlineSupport", getOfflineSupport())
            .append("qualificationRequirements", getQualificationRequirements())
            .append("experienceRequirements", getExperienceRequirements())
            .append("teacherRequirements", getTeacherRequirements())
            .append("facilityRequirements", getFacilityRequirements())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("contactEmail", getContactEmail())
            .append("applicationStartDate", getApplicationStartDate())
            .append("applicationEndDate", getApplicationEndDate())
            .append("expectedStartDate", getExpectedStartDate())
            .append("expectedEndDate", getExpectedEndDate())
            .append("recruitmentStatus", getRecruitmentStatus())
            .append("isFeatured", getIsFeatured())
            .append("isUrgent", getIsUrgent())
            .append("viewCount", getViewCount())
            .append("applicationCount", getApplicationCount())
            .append("selectedCount", getSelectedCount())
            .append("budgetAmount", getBudgetAmount())
            .append("paymentMethod", getPaymentMethod())
            .append("evaluationCriteria", getEvaluationCriteria())
            .append("additionalRequirements", getAdditionalRequirements())
            .append("attachmentFiles", getAttachmentFiles())
            .append("publisherUserId", getPublisherUserId())
            .append("createId", getCreateId())
            .append("createTime", getCreateTime())
            .append("updateId", getUpdateId())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
