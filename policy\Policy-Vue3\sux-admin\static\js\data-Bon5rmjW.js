import{_ as le,C as ne,d as oe,u as ce,r as t,e as ie,S as ue,f as L,K as de,c as P,o as c,l as g,i as b,k as o,h as re,G as D,p as r,t as V,N as me,T as pe,U as ve,V as B,W as fe,X as ge}from"./index-DP10CBaW.js";import{c as he,g as Ce,o as ye}from"./type-CNISLLEN.js";import{g as _e,e as be}from"./columnUtils-DYlA-XL_.js";import{T as De}from"./index-BWWetMd6.js";import Se from"./DictFormDialog-DaQKc8ko.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";const ke={class:"dict-data-container app-container"},Te={key:0},xe={class:"operation-btns"},Le={key:1,class:"loading-placeholder"},Pe=ne({name:"Data"}),Fe=Object.assign(Pe,{setup(Oe){const{proxy:i}=oe(),F=ce(),O=t([]),$=t([]),C=t(!1),S=t(!1),k=t({dialogWidth:"700px",dialogHeight:"65vh"}),m=t(null),p=t(null),u=t(1),y=t(10),T=t(0),h=t({}),v=t(""),E=t([]),_=t([]),I=t(!0),j=t(!0),z=t([]),R=t([]);ie(async()=>{await W(),w(),q(F.params&&F.params.dictId)});const W=async()=>{try{const e=he(i),a=await _e({baseOption:e,proxy:i}),{tableColumns:s,searchColumns:n,formFields:x,formOptions:f}=be(a);O.value=s,$.value=n,R.value=x,k.value={...k.value,...f},S.value=!0}catch(e){S.value=!1,console.error("初始化配置失败:",e)}};function q(e){Ce(e).then(a=>{h.value.dictType=a.data.dictType,v.value=a.data.dictType,A()})}function w(){ye().then(e=>{E.value=e.data})}function A(){d()}function G(){}function H(){var a;const e={dictType:v.value,listClass:"default",dictSort:0,status:"0"};(a=p.value)==null||a.openDialog("add","新增字典数据",e)}function K(e){_.value=e,I.value=e.length!==1,j.value=!e.length}const M=async e=>{var a,s;try{e.type==="add"?(await fe(e.data),i.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await ge(e.data),i.$modal.msgSuccess("修改成功")),B().removeDict(v.value),(a=p.value)==null||a.onSubmitSuccess(),d()}catch(n){(s=p.value)==null||s.onSubmitError(),console.error("提交失败:",n)}};function U(e){const a=e?e.dictCode:_.value.map(n=>n.dictCode),s=e?e.dictLabel:_.value.map(n=>n.dictLabel).join("、");i.$modal.confirm("是否确认删除字典数据【"+s+"】？").then(()=>ve(a)).then(()=>{d(),i.$modal.msgSuccess("删除成功"),B().removeDict(v.value)}).catch(()=>{})}function X(){const e={...h.value};i.download("system/dict/data/export",e,`dict_data_${new Date().getTime()}.xlsx`)}function J(){const e={path:"/system/dict"};i.$tab.closeOpenPage(e)}const d=()=>{C.value=!0;const e={pageNum:u.value,pageSize:y.value,...h.value};ue(e).then(a=>{z.value=a.rows,T.value=a.total,C.value=!1,Q()}).catch(()=>{C.value=!1})},Q=()=>{m.value&&m.value.page&&(m.value.page.total=T.value,m.value.page.currentPage=u.value,m.value.page.pageSize=y.value)},Y=e=>{h.value={...e,dictType:v.value},u.value=1,d()},Z=()=>{h.value={dictType:v.value},u.value=1,d()},ee=e=>{u.value=e,d()},ae=e=>{y.value=e,u.value=1,d()},te=e=>{var a;(a=p.value)==null||a.openDialog("view","查看字典数据",e)},se=e=>{const a=e?e.dictCode:_.value[0];a&&pe(a).then(s=>{var n;(n=p.value)==null||n.openDialog("edit","编辑字典数据",s.data)})};return(e,a)=>{const s=L("el-button"),n=L("el-tag"),x=L("el-empty"),f=de("hasPermi");return c(),P("div",ke,[S.value?(c(),g(De,{key:0,columns:O.value,data:z.value,loading:C.value,showIndex:!0,searchColumns:$.value,showOperation:!0,operationLabel:"操作",operationWidth:"180",fixedOperation:!0,ref_key:"tableListRef",ref:m,onSearch:Y,onReset:Z,defaultPage:{pageSize:y.value,currentPage:u.value,total:T.value},onCurrentChange:ee,onSizeChange:ae,onSelectionChange:K},{"menu-left":o(()=>[D((c(),g(s,{type:"primary",class:"custom-btn",onClick:H},{default:o(()=>[r(" 新 增 ")]),_:1})),[[f,["system:dict:add"]]]),D((c(),g(s,{type:"warning",class:"custom-btn",onClick:X},{default:o(()=>[r(" 导 出 ")]),_:1})),[[f,["system:dict:export"]]]),b(s,{type:"warning",class:"custom-btn",plain:"",onClick:J},{default:o(()=>[r(" 关 闭 ")]),_:1})]),dictLabel:o(({row:l})=>[(l.listClass==""||l.listClass=="default")&&(l.cssClass==""||l.cssClass==null)?(c(),P("span",Te,V(l.dictLabel),1)):(c(),g(n,{key:1,type:l.listClass=="primary"?"":l.listClass,class:me(l.cssClass)},{default:o(()=>[r(V(l.dictLabel),1)]),_:2},1032,["type","class"]))]),menu:o(({row:l})=>[re("div",xe,[b(s,{type:"primary",link:"",onClick:N=>te(l)},{default:o(()=>[r("查看")]),_:2},1032,["onClick"]),D((c(),g(s,{type:"primary",link:"",onClick:N=>se(l)},{default:o(()=>[r("编辑")]),_:2},1032,["onClick"])),[[f,["system:dict:edit"]]]),D((c(),g(s,{type:"danger",link:"",onClick:N=>U(l)},{default:o(()=>[r("删除")]),_:2},1032,["onClick"])),[[f,["system:dict:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(c(),P("div",Le,[b(x,{description:"正在加载表格配置..."})])),b(Se,{ref_key:"dictFormDialogRef",ref:p,formFields:R.value,formOption:k.value,onSubmit:M,onCancel:G},null,8,["formFields","formOption"])])}}}),Ie=le(Fe,[["__scopeId","data-v-9a2fe971"]]);export{Ie as default};
