import{c as ie}from"./job-Co8Wkw3E.js";import{l as ce,g as E,d as le,r as ue,c as de,a as me,u as pe}from"./job-DYlEC7Ga.js";import{g as fe,e as he}from"./columnUtils-DYlA-XL_.js";import{_ as be,C as _e,a as ge,d as $e,r as p,e as Ce,f as F,K as ye,c as T,o as u,l as b,i as J,k as d,h as V,G as j,p as k,N as je,j as W,ad as ke,t as ve}from"./index-DP10CBaW.js";import{T as Ie}from"./index-BWWetMd6.js";import xe from"./JobFormDialog-1LH-z8Lc.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";function De(e){if(!e||typeof e!="string")return"无效的cron表达式";try{const s=e.trim().split(/\s+/);if(s.length<6||s.length>7)return"无效的cron表达式格式";const[t,o,r,m,P,D,S]=s,N=z(t,"second"),_=z(o,"minute"),f=z(r,"hour"),h=Se(m),v=Ne(P),g=Fe(D),I=S?Je(S):"";let c="";return I&&(c+=I+"，"),v!=="每月"&&(c+=v+"，"),g!=="?"&&g!=="*"?c+=g+"，":h!=="每天"&&h!=="?"&&(c+=h+"，"),f!=="每小时"&&(c+=f+"，"),_!=="每分钟"&&(c+=_+"，"),N!=="每秒"&&(c+=N),c=c.replace(/，$/,""),c?c+="执行":c="每秒执行",c}catch{return"无法解析cron表达式"}}function z(e,s){const o={second:{name:"秒",max:59},minute:{name:"分钟",max:59},hour:{name:"小时",max:23}}[s];if(!o)return e;if(e==="*")return`每${o.name}`;if(e.includes("-")){const[r,m]=e.split("-");return`${r}-${m}${o.name}`}if(e.includes("/")){const[r,m]=e.split("/");return r==="*"?`每${m}${o.name}`:`从${r}${o.name}开始，每${m}${o.name}`}return e.includes(",")?`${e.split(",").join("、")}${o.name}`:/^\d+$/.test(e)?`第${e}${o.name}`:e}function Se(e){if(e==="*")return"每天";if(e==="?")return"?";if(e.includes("-")){const[s,t]=e.split("-");return`${s}-${t}号`}if(e.includes("/")){const[s,t]=e.split("/");return s==="*"?`每${t}天`:`从${s}号开始，每${t}天`}return e.includes(",")?`${e.split(",").join("、")}号`:e.includes("W")?`${e.replace("W","")}号最近的工作日`:e==="L"?"本月最后一天":/^\d+$/.test(e)?`${e}号`:e}function Ne(e){if(e==="*")return"每月";const s=["","一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"];if(e.includes("-")){const[t,o]=e.split("-"),r=s[parseInt(t)]||t,m=s[parseInt(o)]||o;return`${r}-${m}`}if(e.includes("/")){const[t,o]=e.split("/");return t==="*"?`每${o}个月`:`从${s[parseInt(t)]||t}开始，每${o}个月`}return e.includes(",")?e.split(",").map(r=>s[parseInt(r)]||r).join("、"):/^\d+$/.test(e)?s[parseInt(e)]||`第${e}月`:e}function Fe(e){if(e==="*")return"*";if(e==="?")return"?";const s=["星期日","星期一","星期二","星期三","星期四","星期五","星期六"];if(e.includes("-")){const[t,o]=e.split("-"),r=s[parseInt(t)-1]||t,m=s[parseInt(o)-1]||o;return`${r}-${m}`}if(e.includes("/")){const[t,o]=e.split("/");return t==="*"?`每${o}周`:`从${s[parseInt(t)-1]||t}开始，每${o}周`}if(e.includes(","))return e.split(",").map(r=>s[parseInt(r)-1]||r).join("、");if(e.includes("#")){const[t,o]=e.split("#"),r=s[parseInt(t)-1]||t;return`第${o}周的${r}`}if(e.includes("L")){const t=e.replace("L","");return`本月最后一个${s[parseInt(t)-1]||t}`}return/^\d+$/.test(e)?s[parseInt(e)-1]||`第${e}周`:e}function Je(e){if(e==="*")return"每年";if(e.includes("-")){const[s,t]=e.split("-");return`${s}-${t}年`}if(e.includes("/")){const[s,t]=e.split("/");return s==="*"?`每${t}年`:`从${s}年开始，每${t}年`}return e.includes(",")?`${e.split(",").join("、")}年`:/^\d+$/.test(e)?`${e}年`:e}function Pe(e){if(!e||typeof e!="string")return!1;const s=e.trim().split(/\s+/);return s.length>=6&&s.length<=7}const Te={class:"job-container app-container"},Ve={key:1},Le={class:"cron-expression-cell"},Oe={class:"cron-value"},ze={key:1,class:"no-cron"},Re={class:"operation-btns"},Ue={key:1,class:"loading-placeholder"},Ee=_e({name:"Job"}),We=Object.assign(Ee,{setup(e){const s=ge(),{proxy:t}=$e(),{sys_job_group:o,sys_job_status:r}=t.useDict("sys_job_group","sys_job_status"),m=p([]),P=p([]),D=p(!1),S=p(!1),N=p({dialogWidth:"900px",dialogHeight:"70vh"}),_=p(null),f=p(null),h=p(1),v=p(10),g=p(0),I=p({}),c=p([]),R=p([]);Ce(()=>{c.value=[],q()});const q=async()=>{try{const n=ie(t),a=await fe({baseOption:n,proxy:t}),{tableColumns:l,searchColumns:x,formFields:L,formOptions:O}=he(a);m.value=l,P.value=x,R.value=L,N.value={...N.value,...O},S.value=!0,$()}catch(n){S.value=!1,console.error("初始化配置失败:",n)}},$=()=>{D.value=!0;const n={pageNum:h.value,pageSize:v.value,...I.value};ce(n).then(a=>{const l=(a.rows||[]).filter(x=>x&&x.jobId&&x.jobName);c.value=l,g.value=a.total||0,B(),D.value=!1}).catch(a=>{console.error("加载任务列表失败:",a),c.value=[],g.value=0,D.value=!1})},B=()=>{_.value&&_.value.page&&(_.value.page.total=g.value,_.value.page.currentPage=h.value,_.value.page.pageSize=v.value)},G=n=>{I.value=n,h.value=1,$()},M=()=>{I.value={},h.value=1,$()},A=n=>{h.value=n,$()},H=n=>{v.value=n,h.value=1,$()},K=(n,a)=>{if(!n||!n.jobId||!n.jobName||n.status===a)return;let l=a==="0"?"启用":"停用";t.$modal.confirm('确认要"'+l+'""'+n.jobName+'"任务吗?').then(function(){return de(n.jobId,a)}).then(()=>{t.$modal.msgSuccess(l+"成功"),n.status=a}).catch(function(){n.status=n.status==="0"?"1":"0"})},Y=n=>{t.$modal.confirm('确认要立即执行一次"'+n.jobName+'"任务吗?').then(function(){return ue(n.jobId,n.jobGroup)}).then(()=>{t.$modal.msgSuccess("执行成功")}).catch(()=>{})},Q=()=>{f.value.openDialog("add","新增任务")},X=n=>{E(n.jobId).then(a=>{f.value.openDialog("edit","修改任务",a.data)})},Z=n=>{E(n.jobId).then(a=>{f.value.openDialog("view","任务详细",a.data)})},w=n=>{const a=n.jobId;t.$modal.confirm('是否确认删除定时任务编号为"'+a+'"的数据项?').then(function(){return le(a)}).then(()=>{$(),t.$modal.msgSuccess("删除成功")}).catch(()=>{})},ee=()=>{t.download("monitor/job/export",{...I.value},`job_${new Date().getTime()}.xlsx`)},U=n=>{const a=(n==null?void 0:n.jobId)||0;s.push("/system/job-log/index/"+a)},te=({type:n,data:a})=>{n==="add"?me(a).then(l=>{t.$modal.msgSuccess("新增成功"),f.value.onSubmitSuccess(),$()}).catch(()=>{f.value.onSubmitError()}):n==="edit"&&pe(a).then(l=>{t.$modal.msgSuccess("修改成功"),f.value.onSubmitSuccess(),$()}).catch(()=>{f.value.onSubmitError()})},ne=()=>{},se=n=>{if(!n)return"无cron表达式";try{return`${De(n)}

表达式：${n}`}catch{return`无法解析cron表达式：${n}`}},oe=n=>Pe(n);return(n,a)=>{const l=F("el-button"),x=F("dict-tag"),L=F("el-switch"),O=F("el-icon"),ae=F("el-tooltip"),re=F("el-empty"),C=ye("hasPermi");return u(),T("div",Te,[S.value&&m.value.length>0?(u(),b(Ie,{key:0,columns:m.value,data:c.value,loading:D.value,showIndex:!0,searchColumns:P.value,showOperation:!0,operationLabel:"操作",operationWidth:"220",fixedOperation:!0,ref_key:"tableListRef",ref:_,onSearch:G,onReset:M,defaultPage:{pageSize:v.value,currentPage:h.value,total:g.value},onCurrentChange:A,onSizeChange:H},{"menu-left":d(()=>[j((u(),b(l,{type:"primary",class:"custom-btn",onClick:Q},{default:d(()=>[k(" 新 增 ")]),_:1})),[[C,["monitor:job:add"]]]),j((u(),b(l,{type:"warning",class:"custom-btn",onClick:ee},{default:d(()=>[k(" 导 出 ")]),_:1})),[[C,["monitor:job:export"]]]),j((u(),b(l,{type:"info",class:"custom-btn",onClick:U},{default:d(()=>[k(" 日 志 ")]),_:1})),[[C,["monitor:job:query"]]])]),jobGroup:d(({row:i})=>[J(x,{options:W(o),value:i.jobGroup},null,8,["options","value"])]),status:d(({row:i})=>[i&&i.jobId&&i.jobName?(u(),b(L,{key:0,modelValue:i.status,"onUpdate:modelValue":y=>i.status=y,"active-value":"0","inactive-value":"1",onChange:y=>K(i,y)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(u(),T("span",Ve,"-"))]),cronExpression:d(({row:i})=>[V("div",Le,[i.cronExpression?(u(),b(ae,{key:0,content:se(i.cronExpression),placement:"top",effect:"dark","show-after":300,"hide-after":100,"popper-class":"cron-tooltip"},{default:d(()=>[V("div",{class:je(["cron-text",{"invalid-cron":!oe(i.cronExpression)}])},[J(O,{class:"cron-icon"},{default:d(()=>[J(W(ke))]),_:1}),V("span",Oe,ve(i.cronExpression),1)],2)]),_:2},1032,["content"])):(u(),T("span",ze,"-"))])]),menu:d(({row:i})=>[V("div",Re,[j((u(),b(l,{type:"primary",link:"",onClick:y=>X(i)},{default:d(()=>[k(" 修改 ")]),_:2},1032,["onClick"])),[[C,["monitor:job:edit"]]]),j((u(),b(l,{type:"danger",link:"",onClick:y=>w(i)},{default:d(()=>[k(" 删除 ")]),_:2},1032,["onClick"])),[[C,["monitor:job:remove"]]]),j((u(),b(l,{type:"success",link:"",onClick:y=>Y(i)},{default:d(()=>[k(" 执行 ")]),_:2},1032,["onClick"])),[[C,["monitor:job:changeStatus"]]]),j((u(),b(l,{type:"info",link:"",onClick:y=>Z(i)},{default:d(()=>[k(" 详细 ")]),_:2},1032,["onClick"])),[[C,["monitor:job:query"]]]),j((u(),b(l,{type:"warning",link:"",onClick:y=>U(i)},{default:d(()=>[k(" 日志 ")]),_:2},1032,["onClick"])),[[C,["monitor:job:query"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(u(),T("div",Ue,[J(re,{description:"正在加载表格配置..."})])),J(xe,{ref_key:"jobFormDialogRef",ref:f,formFields:R.value,formOption:N.value,onSubmit:te,onCancel:ne},null,8,["formFields","formOption"])])}}}),Qe=be(We,[["__scopeId","data-v-9e9188db"]]);export{Qe as default};
