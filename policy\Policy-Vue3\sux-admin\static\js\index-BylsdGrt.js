import{_ as de,A as le,r as E,f,c as v,o as u,a4 as z,l as r,m as F,i as k,t as D,k as m,L as x,M as Y,p as B,j as w,a9 as ue,N as te,aa as ne,ab as se,h as O,x as ie,y as oe,d as re,D as fe,I as ce,w as Z,e as me}from"./index-DP10CBaW.js";const be=e=>(ie("data-v-e433a521"),e=e(),oe(),e),ye={class:"form-field-component"},ve={key:5,class:"number-suffix-wrapper"},Ve={key:0,class:"suffix-icon"},he={key:23,class:"icon-selector-wrapper"},pe=be(()=>O("span",{class:"mention-prefix"},"@",-1)),ke={key:25,class:"file-upload-wrapper"},ge={key:0,class:"el-upload__tip"},we={__name:"FormFieldComponent",props:{field:{type:Object,required:!0},modelValue:{type:[String,Number,Boolean,Array,Object],default:null},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:q}){const n=e,c=q,o=le({get:()=>n.modelValue===void 0||n.modelValue===null?n.field.type==="checkbox"?[]:n.field.type==="switch"?n.field.inactiveValue!==void 0?n.field.inactiveValue:!1:n.field.type==="number"||n.field.type==="number-suffix"?n.field.min||0:n.field.type==="rate"?n.field.defaultValue!==void 0?n.field.defaultValue:0:n.field.type==="slider"?n.field.defaultValue!==void 0?n.field.defaultValue:n.field.min||0:n.field.defaultValue!==void 0?n.field.defaultValue:"":n.modelValue,set:b=>{JSON.stringify(b)!==JSON.stringify(n.modelValue)&&c("update:modelValue",b)}}),$=le(()=>{if(!n.field.dicData||!Array.isArray(n.field.dicData))return[];const b=a=>{if(!a||typeof a!="object")return null;const V={...a,value:a.value!==void 0?a.value:a.id,label:a.label||a.name||a.title||String(a.id)};return a.children&&Array.isArray(a.children)&&(V.children=a.children.map(b).filter(R=>R!==null)),V};return n.field.dicData.map(b).filter(a=>a!==null)}),t=E(!1),S=E([]),j=E(!1),C=E([]),h=()=>n.field.placeholder?n.field.placeholder:`${n.field.type==="select"||n.field.type==="cascader"||n.field.type==="tree-select"||n.field.type==="api-select"||n.field.type==="api-tree-select"||n.field.type==="remote-search"?"请选择":"请输入"}${n.field.label||""}`,_=b=>{b&&typeof n.field.remoteMethod=="function"&&(t.value=!0,n.field.remoteMethod(b).then(a=>{S.value=a,t.value=!1}).catch(()=>{t.value=!1}))},g=b=>n.field.beforeUpload&&typeof n.field.beforeUpload=="function"?n.field.beforeUpload(b):!0,W=(b,a)=>{var V;n.field.onSuccess&&typeof n.field.onSuccess=="function"&&n.field.onSuccess(b,a),C.value.push({name:a.name,url:b.url||((V=b.data)==null?void 0:V.url)}),c("update:modelValue",C.value)},P=(b,a)=>{n.field.onError&&typeof n.field.onError=="function"&&n.field.onError(b,a)};return(b,a)=>{const V=f("el-input"),R=f("el-input-number"),M=f("el-option"),A=f("el-select"),L=f("el-tree-select"),H=f("el-radio-button"),J=f("el-radio"),G=f("el-radio-group"),K=f("el-checkbox-button"),Q=f("el-checkbox"),ee=f("el-checkbox-group"),d=f("el-switch"),i=f("el-slider"),y=f("el-date-picker"),p=f("el-time-picker"),U=f("el-color-picker"),I=f("el-rate"),X=f("el-cascader"),s=f("el-icon"),N=f("el-button"),T=f("el-upload");return u(),v("div",ye,[e.field.formSlot?z(b.$slots,e.field.prop,{key:0,field:e.field,value:o.value,disabled:e.disabled},void 0,!0):e.field.type==="input"||!e.field.type?(u(),r(V,{key:1,modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=l=>o.value=l),placeholder:h(),disabled:e.disabled,maxlength:e.field.maxlength,"show-word-limit":e.field.showWordLimit,clearable:e.field.clearable!==!1,class:"modern-input"},null,8,["modelValue","placeholder","disabled","maxlength","show-word-limit","clearable"])):e.field.type==="password"?(u(),r(V,{key:2,modelValue:o.value,"onUpdate:modelValue":a[1]||(a[1]=l=>o.value=l),type:"password",placeholder:h(),disabled:e.disabled,maxlength:e.field.maxlength,"show-password":!0,clearable:e.field.clearable!==!1,class:"modern-input"},null,8,["modelValue","placeholder","disabled","maxlength","clearable"])):e.field.type==="textarea"?(u(),r(V,{key:3,modelValue:o.value,"onUpdate:modelValue":a[2]||(a[2]=l=>o.value=l),type:"textarea",placeholder:h(),disabled:e.disabled,maxlength:e.field.maxlength,"show-word-limit":e.field.showWordLimit,rows:e.field.rows||3,autosize:e.field.autosize,class:"modern-textarea"},null,8,["modelValue","placeholder","disabled","maxlength","show-word-limit","rows","autosize"])):e.field.type==="number"?(u(),r(R,{key:4,modelValue:o.value,"onUpdate:modelValue":a[3]||(a[3]=l=>o.value=l),placeholder:h(),disabled:e.disabled,min:e.field.min,max:e.field.max,step:e.field.step,precision:e.field.precision,controls:e.field.controls!==!1,class:"modern-number full-width"},null,8,["modelValue","placeholder","disabled","min","max","step","precision","controls"])):e.field.type==="number-suffix"?(u(),v("div",ve,[k(R,{modelValue:o.value,"onUpdate:modelValue":a[4]||(a[4]=l=>o.value=l),placeholder:h(),disabled:e.disabled,min:e.field.min,max:e.field.max,step:e.field.step,precision:e.field.precision,controls:e.field.controls!==!1,class:"modern-number"},null,8,["modelValue","placeholder","disabled","min","max","step","precision","controls"]),e.field.suffix?(u(),v("span",Ve,D(e.field.suffix),1)):F("",!0)])):e.field.type==="api-select"?(u(),r(A,{key:6,modelValue:o.value,"onUpdate:modelValue":a[5]||(a[5]=l=>o.value=l),placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,multiple:e.field.multiple,"collapse-tags":e.field.collapseTags,filterable:e.field.filterable,class:"modern-select full-width"},{default:m(()=>[(u(!0),v(x,null,Y(e.field.dicData,l=>(u(),r(M,{key:l.value,label:l.label,value:l.value,disabled:l.disabled},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue","placeholder","disabled","clearable","multiple","collapse-tags","filterable"])):e.field.type==="remote-search"?(u(),r(A,{key:7,modelValue:o.value,"onUpdate:modelValue":a[6]||(a[6]=l=>o.value=l),placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,filterable:!0,remote:"","remote-method":_,loading:t.value,class:"modern-select full-width"},{default:m(()=>[(u(!0),v(x,null,Y(S.value,l=>(u(),r(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","disabled","clearable","loading"])):e.field.type==="api-tree-select"?(u(),r(L,{key:8,modelValue:o.value,"onUpdate:modelValue":a[7]||(a[7]=l=>o.value=l),data:$.value,placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,"check-strictly":e.field.checkStrictly,multiple:e.field.multiple,class:"modern-tree-select full-width"},null,8,["modelValue","data","placeholder","disabled","clearable","check-strictly","multiple"])):e.field.type==="select"?(u(),r(A,{key:9,modelValue:o.value,"onUpdate:modelValue":a[8]||(a[8]=l=>o.value=l),placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,multiple:e.field.multiple,"collapse-tags":e.field.collapseTags,filterable:e.field.filterable,class:"modern-select full-width"},{default:m(()=>[(u(!0),v(x,null,Y(e.field.dicData,l=>(u(),r(M,{key:l.value,label:l.label,value:l.value,disabled:l.disabled},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue","placeholder","disabled","clearable","multiple","collapse-tags","filterable"])):e.field.type==="radio"?(u(),r(G,{key:10,modelValue:o.value,"onUpdate:modelValue":a[9]||(a[9]=l=>o.value=l),disabled:e.disabled,size:e.field.size,class:"modern-radio-group"},{default:m(()=>[e.field.button?(u(!0),v(x,{key:0},Y(e.field.dicData,l=>(u(),r(H,{key:l.value,label:l.label,disabled:l.disabled,value:l.value,class:"modern-radio-button"},null,8,["label","disabled","value"]))),128)):(u(!0),v(x,{key:1},Y(e.field.dicData,l=>(u(),r(J,{key:l.value,label:l.label,disabled:l.disabled,value:l.value,class:"modern-radio"},null,8,["label","disabled","value"]))),128))]),_:1},8,["modelValue","disabled","size"])):e.field.type==="checkbox"?(u(),r(ee,{key:11,modelValue:o.value,"onUpdate:modelValue":a[10]||(a[10]=l=>o.value=l),disabled:e.disabled,size:e.field.size,class:"modern-checkbox-group"},{default:m(()=>[e.field.button?(u(!0),v(x,{key:0},Y(e.field.dicData,l=>(u(),r(K,{key:l.value,value:l.value,disabled:l.disabled,class:"modern-checkbox-button"},{default:m(()=>[B(D(l.label),1)]),_:2},1032,["value","disabled"]))),128)):(u(!0),v(x,{key:1},Y(e.field.dicData,l=>(u(),r(Q,{key:l.value,value:l.value,disabled:l.disabled,class:"modern-checkbox"},{default:m(()=>[B(D(l.label),1)]),_:2},1032,["value","disabled"]))),128))]),_:1},8,["modelValue","disabled","size"])):e.field.type==="switch"?(u(),r(d,{key:12,modelValue:o.value,"onUpdate:modelValue":a[11]||(a[11]=l=>o.value=l),disabled:e.disabled,"active-value":e.field.activeValue!==void 0?e.field.activeValue:!0,"inactive-value":e.field.inactiveValue!==void 0?e.field.inactiveValue:!1,"active-text":e.field.activeText,"inactive-text":e.field.inactiveText,class:"modern-switch"},null,8,["modelValue","disabled","active-value","inactive-value","active-text","inactive-text"])):e.field.type==="slider"?(u(),r(i,{key:13,modelValue:o.value,"onUpdate:modelValue":a[12]||(a[12]=l=>o.value=l),disabled:e.disabled,min:e.field.min,max:e.field.max,step:e.field.step,"show-input":e.field.showInput,range:e.field.range,class:"modern-slider"},null,8,["modelValue","disabled","min","max","step","show-input","range"])):e.field.type==="date"?(u(),r(y,{key:14,modelValue:o.value,"onUpdate:modelValue":a[13]||(a[13]=l=>o.value=l),type:"date",placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,format:e.field.format||"YYYY-MM-DD","value-format":e.field.valueFormat||"YYYY-MM-DD",class:"modern-date-picker full-width"},null,8,["modelValue","placeholder","disabled","clearable","format","value-format"])):e.field.type==="datetime"?(u(),r(y,{key:15,modelValue:o.value,"onUpdate:modelValue":a[14]||(a[14]=l=>o.value=l),type:"datetime",placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,format:e.field.format||"YYYY-MM-DD HH:mm:ss","value-format":e.field.valueFormat||"YYYY-MM-DD HH:mm:ss",class:"modern-date-picker full-width"},null,8,["modelValue","placeholder","disabled","clearable","format","value-format"])):e.field.type==="time"?(u(),r(p,{key:16,modelValue:o.value,"onUpdate:modelValue":a[15]||(a[15]=l=>o.value=l),placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,format:e.field.format||"HH:mm:ss","value-format":e.field.valueFormat||"HH:mm:ss",class:"modern-time-picker full-width"},null,8,["modelValue","placeholder","disabled","clearable","format","value-format"])):e.field.type==="daterange"?(u(),r(y,{key:17,modelValue:o.value,"onUpdate:modelValue":a[16]||(a[16]=l=>o.value=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",disabled:e.disabled,clearable:e.field.clearable!==!1,format:e.field.format||"YYYY-MM-DD","value-format":e.field.valueFormat||"YYYY-MM-DD",class:"modern-date-picker full-width"},null,8,["modelValue","disabled","clearable","format","value-format"])):e.field.type==="timerange"?(u(),r(p,{key:18,modelValue:o.value,"onUpdate:modelValue":a[17]||(a[17]=l=>o.value=l),"is-range":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",disabled:e.disabled,clearable:e.field.clearable!==!1,format:e.field.format||"HH:mm:ss","value-format":e.field.valueFormat||"HH:mm:ss",class:"modern-time-picker full-width"},null,8,["modelValue","disabled","clearable","format","value-format"])):e.field.type==="color"?(u(),r(U,{key:19,modelValue:o.value,"onUpdate:modelValue":a[18]||(a[18]=l=>o.value=l),disabled:e.disabled,"show-alpha":e.field.showAlpha,class:"modern-color-picker"},null,8,["modelValue","disabled","show-alpha"])):e.field.type==="rate"?(u(),r(I,{key:20,modelValue:o.value,"onUpdate:modelValue":a[19]||(a[19]=l=>o.value=l),disabled:e.disabled,max:e.field.max||5,"allow-half":e.field.allowHalf,class:"modern-rate"},null,8,["modelValue","disabled","max","allow-half"])):e.field.type==="cascader"?(u(),r(X,{key:21,modelValue:o.value,"onUpdate:modelValue":a[20]||(a[20]=l=>o.value=l),options:e.field.dicData,props:e.field.props||{checkStrictly:!1},placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,filterable:e.field.filterable,class:"modern-cascader full-width"},null,8,["modelValue","options","props","placeholder","disabled","clearable","filterable"])):e.field.type==="tree-select"?(u(),r(L,{key:22,modelValue:o.value,"onUpdate:modelValue":a[21]||(a[21]=l=>o.value=l),data:$.value,placeholder:h(),disabled:e.disabled,clearable:e.field.clearable!==!1,"check-strictly":e.field.checkStrictly,multiple:e.field.multiple,class:"modern-tree-select full-width"},null,8,["modelValue","data","placeholder","disabled","clearable","check-strictly","multiple"])):e.field.type==="icon"?(u(),v("div",he,[k(V,{modelValue:o.value,"onUpdate:modelValue":a[22]||(a[22]=l=>o.value=l),placeholder:h(),disabled:e.disabled,readonly:"",class:"modern-input icon-input",onClick:a[23]||(a[23]=l=>j.value=!0)},{prefix:m(()=>[o.value?(u(),v("i",{key:0,class:te(o.value)},null,2)):(u(),r(s,{key:1},{default:m(()=>[k(w(ne))]),_:1}))]),suffix:m(()=>[k(s,{class:"icon-arrow"},{default:m(()=>[k(w(ue))]),_:1})]),_:1},8,["modelValue","placeholder","disabled"])])):e.field.type==="mention"?(u(),r(V,{key:24,modelValue:o.value,"onUpdate:modelValue":a[24]||(a[24]=l=>o.value=l),placeholder:h(),disabled:e.disabled,maxlength:e.field.maxlength,clearable:e.field.clearable!==!1,class:"modern-input mention-input"},{prefix:m(()=>[pe]),_:1},8,["modelValue","placeholder","disabled","maxlength","clearable"])):e.field.type==="file"?(u(),v("div",ke,[k(T,{disabled:e.disabled,action:e.field.action||"#","before-upload":g,"on-success":W,"on-error":P,"file-list":C.value,"list-type":e.field.listType||"text",accept:e.field.accept,multiple:e.field.multiple,class:"modern-upload"},{trigger:m(()=>[k(N,{type:"primary",class:"upload-btn"},{default:m(()=>[k(s,null,{default:m(()=>[k(w(se))]),_:1}),B(" "+D(e.field.uploadText||"点击上传图片"),1)]),_:1})]),tip:m(()=>[e.field.tip?(u(),v("div",ge,D(e.field.tip),1)):F("",!0)]),_:1},8,["disabled","action","file-list","list-type","accept","multiple"])])):F("",!0)])}}},ae=de(we,[["__scopeId","data-v-e433a521"]]),xe=e=>(ie("data-v-ee775cde"),e=e(),oe(),e),De={class:"form-container"},Se={class:"form-divider-wrapper"},Fe={class:"divider-title"},Ue=xe(()=>O("div",{class:"divider-line"},null,-1)),Ye={key:2,class:"el-form-item-tip"},$e={key:2,class:"el-form-item-tip"},Re={key:0,class:"form-actions"},Ae={__name:"index",props:{formOption:{type:Object,default:()=>({})},fields:{type:Array,default:()=>[]},modelValue:{type:Object,default:()=>({})},isView:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},labelWidth:{type:String,default:"120px"},labelPosition:{type:String,default:"right"},inline:{type:Boolean,default:!1},showActions:{type:Boolean,default:!0},submitText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},size:{type:String,default:"default"},fetchFields:{type:Boolean,default:!1},menuCode:{type:String,default:""},submitApi:{type:Function},loadApi:{type:Function},idField:{type:String,default:"id"}},emits:["update:modelValue","submit","cancel","field-change"],setup(e,{expose:q,emit:n}){const c=e,o=n,{proxy:$}=re(),t=fe({formData:{...c.modelValue},formFields:[...c.fields],formRules:{},submitLoading:!1,fieldState:{},_applyingRules:!1}),{formData:S,formFields:j,formRules:C,submitLoading:h,fieldState:_}=ce(t),g=E(null);Z(()=>c.modelValue,d=>{d&&(t.formData={...d},V())},{deep:!0}),Z(()=>c.fields,d=>{d&&d.length>0&&(t.formFields=[...d],b(),V())},{deep:!0}),Z(()=>t.formData,()=>{$.$nextTick(()=>{V(),R(),a()})},{deep:!0}),me(()=>{c.fetchFields&&c.menuCode?W():(b(),V()),c.loadApi&&c.modelValue&&c.modelValue[c.idField]&&P(c.modelValue[c.idField])});const W=async()=>{try{const d=[];t.formFields=d.filter(i=>i.addDisplay!==!1&&i.editDisplay!==!1),b(),V()}catch{}},P=async d=>{if(c.loadApi)try{const i=await c.loadApi(d);t.formData={...i.data},o("update:modelValue",t.formData),V()}catch{}},b=()=>{const d={};t.formFields.forEach(i=>{i.rules?d[i.prop]=i.rules:i.required&&(d[i.prop]=[{required:!0,message:`请${i.type==="select"?"选择":"输入"}${i.label}`,trigger:["blur","change"]}])}),t.formRules=d},a=()=>{const d={...t.formRules};t.formFields.forEach(i=>{if(i.dynamicRules&&typeof i.dynamicRules=="function"){const y=i.dynamicRules(t.formData);y&&(d[i.prop]=y)}t.fieldState[i.prop]&&t.fieldState[i.prop].rules&&(d[i.prop]=t.fieldState[i.prop].rules)}),t.formRules=d,g.value&&$.$nextTick(()=>{g.value.clearValidate()})},V=()=>{if(!t._applyingRules){t._applyingRules=!0;try{const d={};t.formFields.forEach(i=>{d[i.prop]={viewDisplay:!0,addDisplay:!0,editDisplay:!0,disabled:i.disabled||!1,label:i.label,rules:i.rules||[]}}),t.fieldState={...d},t.formFields.forEach(i=>{if(i.control&&typeof i.control=="function"){const y=t.formData[i.prop],p=i.control(y,t.formData);p&&Object.keys(p).forEach(U=>{t.fieldState[U]&&(t.fieldState[U]={...t.fieldState[U],...p[U]})})}})}finally{t._applyingRules=!1}}},R=()=>{t.formFields.forEach((d,i)=>{if(d.dynamicLabel&&typeof d.dynamicLabel=="function"){const y=d.dynamicLabel(t.formData);t.formFields[i].label=y,t.fieldState[d.prop]&&(t.fieldState[d.prop].label=y)}t.fieldState[d.prop]&&t.fieldState[d.prop].label&&(t.formFields[i].label=t.fieldState[d.prop].label)})},M=(d,i)=>{const y=t.formFields.findIndex(p=>p.prop===d);y!==-1&&(t.formFields[y].label=i,t.fieldState[d]?t.fieldState[d].label=i:t.fieldState[d]={label:i})},A=d=>{const i=t.fieldState[d.prop];return c.isView&&(i&&i.viewDisplay===!1||d.viewDisplay===!1)||!c.isView&&c.isEdit&&(i&&i.editDisplay===!1||d.editDisplay===!1)||!c.isView&&!c.isEdit&&(i&&i.addDisplay===!1||d.addDisplay===!1)?!1:typeof d.display=="function"?d.display(t.formData):!0},L=(d,i)=>{t.formData[d]=i,o("update:modelValue",t.formData),o("field-change",{field:d,value:i,formData:t.formData})},H=()=>{g.value&&g.value.validate(async d=>{if(d)if(c.submitApi){t.submitLoading=!0;try{const i=await c.submitApi(t.formData);$.$modal.msgSuccess("保存成功"),o("submit",{data:t.formData,response:i})}catch{}finally{t.submitLoading=!1}}else o("submit",{data:t.formData})})},J=()=>{o("cancel")},G=()=>{var d;return(d=g.value)==null?void 0:d.validate()},K=()=>{var d;(d=g.value)==null||d.resetFields()},Q=d=>{let i=!1;for(let y=d+1;y<t.formFields.length;y++){const p=t.formFields[y];if(p.divider&&A(p))break;if(A(p)&&!p.divider){i=!0;break}}return i};return q({formData:S,formRef:g,validate:G,resetFields:K,submit:H,applyControlRules:V,setFieldLabel:M,updateFieldLabels:R,setFieldRules:(d,i)=>{t.formRules[d]=i,t.fieldState[d]?t.fieldState[d].rules=i:t.fieldState[d]={rules:i},g.value&&$.$nextTick(()=>{g.value.clearValidate(d)})},updateFieldRules:a}),(d,i)=>{const y=f("el-col"),p=f("el-form-item"),U=f("el-row"),I=f("el-button"),X=f("el-form");return u(),v("div",De,[k(X,{ref_key:"formRef",ref:g,model:w(S),rules:w(C),"label-width":e.labelWidth,"label-position":e.labelPosition,disabled:e.isView||e.disabled,inline:e.inline,size:e.size,"validate-on-rule-change":!1},{default:m(()=>[k(U,{gutter:24},{default:m(()=>[(u(!0),v(x,null,Y(w(j),(s,N)=>(u(),v(x,{key:N},[A(s)?(u(),v(x,{key:0},[s.divider?(u(),r(y,{key:0,span:24,class:"form-divider-col"},{default:m(()=>[Q(N)?z(d.$slots,s.prop,{key:0,field:s},()=>[O("div",Se,[O("div",Fe,D(s.label),1),Ue])],!0):F("",!0)]),_:2},1024)):e.inline?(u(),r(p,{key:2,label:s.label,prop:s.prop,required:s.required,class:te({"form-item-block":s.block})},{default:m(()=>[s.formSlot?z(d.$slots,s.prop,{key:0,field:s,value:w(S)[s.prop],disabled:s.disabled||e.isView||e.disabled},void 0,!0):(u(),r(ae,{key:1,field:s,"model-value":w(S)[s.prop],"onUpdate:modelValue":T=>L(s.prop,T),disabled:s.disabled||e.isView||e.disabled},null,8,["field","model-value","onUpdate:modelValue","disabled"])),s.tip?(u(),v("div",$e,D(s.tip),1)):F("",!0)]),_:2},1032,["label","prop","required","class"])):(u(),r(y,{key:1,span:s.span||12},{default:m(()=>[k(p,{label:s.label,prop:s.prop,required:s.required},{default:m(()=>[s.formSlot?z(d.$slots,s.prop,{key:0,field:s,value:w(S)[s.prop],disabled:s.disabled||e.isView||e.disabled},void 0,!0):(u(),r(ae,{key:1,field:s,"model-value":w(S)[s.prop],"onUpdate:modelValue":T=>L(s.prop,T),disabled:s.disabled||e.isView||e.disabled},null,8,["field","model-value","onUpdate:modelValue","disabled"])),s.tip?(u(),v("div",Ye,D(s.tip),1)):F("",!0)]),_:2},1032,["label","prop","required"])]),_:2},1032,["span"]))],64)):F("",!0)],64))),128))]),_:3}),z(d.$slots,"fields",{},void 0,!0),e.showActions?(u(),v("div",Re,[z(d.$slots,"actions",{},()=>[k(I,{onClick:J},{default:m(()=>[B(D(e.cancelText),1)]),_:1}),e.isView?F("",!0):(u(),r(I,{key:0,type:"primary",onClick:H,loading:w(h)},{default:m(()=>[B(D(e.submitText),1)]),_:1},8,["loading"]))],!0)])):F("",!0)]),_:3},8,["model","rules","label-width","label-position","disabled","inline","size"])])}}},Ce=de(Ae,[["__scopeId","data-v-ee775cde"]]);export{Ce as F};
