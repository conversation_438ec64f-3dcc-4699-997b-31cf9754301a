import{_ as T,r as P,D as q,A as E,f as r,l as I,o as L,a2 as z,k as l,h as B,i as e,p as m}from"./index-DP10CBaW.js";const R={class:"dialog-footer"},j={__name:"PlaceInfoFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(w,{expose:C,emit:x}){const U=x,v=P(!1),t=P("add"),c=P(null),a=q({placeId:null,placeName:"",placeCode:"",placeType:"",placeLevel:"",placeArea:null,usableArea:null,address:"",regionCode:"",regionName:"",contactPerson:"",contactPhone:"",contactEmail:"",companyCount:0,availablePositions:0,occupiedPositions:0,rentPriceMin:null,rentPriceMax:null,operationMode:"",industryDirection:"",description:"",status:"0",isFeatured:0,isOpenSettle:1,remark:""}),M={placeName:[{required:!0,message:"场地名称不能为空",trigger:"blur"}],placeType:[{required:!0,message:"场地类型不能为空",trigger:"change"}],address:[{required:!0,message:"详细地址不能为空",trigger:"blur"}],contactPerson:[{required:!0,message:"联系人不能为空",trigger:"blur"}],contactPhone:[{required:!0,message:"联系电话不能为空",trigger:"blur"}]},N=E(()=>({add:"新增场地信息",edit:"编辑场地信息",view:"查看场地信息"})[t.value]||"场地信息"),O=(V,d={})=>{t.value=V,v.value=!0,Object.keys(a).forEach(u=>{d[u]!==void 0?a[u]=d[u]:u==="companyCount"||u==="availablePositions"||u==="occupiedPositions"?a[u]=0:u==="status"?a[u]="0":u==="isFeatured"?a[u]=0:u==="isOpenSettle"?a[u]=1:a[u]=null}),c.value&&c.value.clearValidate()},A=()=>{t.value!=="view"&&c.value.validate(V=>{V&&(U("submit",{...a},t.value),v.value=!1)})},D=()=>{v.value=!1,U("cancel")};return C({openDialog:O}),(V,d)=>{const u=r("el-input"),i=r("el-form-item"),n=r("el-col"),p=r("el-row"),s=r("el-option"),_=r("el-select"),f=r("el-input-number"),b=r("el-radio"),g=r("el-radio-group"),F=r("el-form"),y=r("el-button"),S=r("el-dialog");return L(),I(S,{title:N.value,modelValue:v.value,"onUpdate:modelValue":d[24]||(d[24]=o=>v.value=o),width:w.formOption.dialogWidth||"1200px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},z({default:l(()=>[e(F,{ref_key:"formRef",ref:c,model:a,rules:M,"label-width":w.formOption.labelWidth||"120px",size:w.formOption.size||"default"},{default:l(()=>[e(p,{gutter:20},{default:l(()=>[e(n,{span:12},{default:l(()=>[e(i,{label:"场地名称",prop:"placeName"},{default:l(()=>[e(u,{modelValue:a.placeName,"onUpdate:modelValue":d[0]||(d[0]=o=>a.placeName=o),placeholder:"请输入场地名称",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:12},{default:l(()=>[e(i,{label:"场地编码",prop:"placeCode"},{default:l(()=>[e(u,{modelValue:a.placeCode,"onUpdate:modelValue":d[1]||(d[1]=o=>a.placeCode=o),placeholder:"请输入场地编码",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:12},{default:l(()=>[e(i,{label:"场地类型",prop:"placeType"},{default:l(()=>[e(_,{modelValue:a.placeType,"onUpdate:modelValue":d[2]||(d[2]=o=>a.placeType=o),placeholder:"请选择场地类型",disabled:t.value==="view",style:{width:"100%"}},{default:l(()=>[e(s,{label:"创业园区",value:"创业园区"}),e(s,{label:"孵化器",value:"孵化器"}),e(s,{label:"众创空间",value:"众创空间"}),e(s,{label:"产业园",value:"产业园"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:12},{default:l(()=>[e(i,{label:"场地等级",prop:"placeLevel"},{default:l(()=>[e(_,{modelValue:a.placeLevel,"onUpdate:modelValue":d[3]||(d[3]=o=>a.placeLevel=o),placeholder:"请选择场地等级",disabled:t.value==="view",style:{width:"100%"}},{default:l(()=>[e(s,{label:"国家级",value:"国家级"}),e(s,{label:"省级",value:"省级"}),e(s,{label:"市级",value:"市级"}),e(s,{label:"区级",value:"区级"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:12},{default:l(()=>[e(i,{label:"场地面积",prop:"placeArea"},{default:l(()=>[e(f,{modelValue:a.placeArea,"onUpdate:modelValue":d[4]||(d[4]=o=>a.placeArea=o),placeholder:"请输入场地面积（平方米）",disabled:t.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:12},{default:l(()=>[e(i,{label:"可使用面积",prop:"usableArea"},{default:l(()=>[e(f,{modelValue:a.usableArea,"onUpdate:modelValue":d[5]||(d[5]=o=>a.usableArea=o),placeholder:"请输入可使用面积（平方米）",disabled:t.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:l(()=>[e(n,{span:24},{default:l(()=>[e(i,{label:"详细地址",prop:"address"},{default:l(()=>[e(u,{modelValue:a.address,"onUpdate:modelValue":d[6]||(d[6]=o=>a.address=o),placeholder:"请输入详细地址",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:12},{default:l(()=>[e(i,{label:"区域代码",prop:"regionCode"},{default:l(()=>[e(_,{modelValue:a.regionCode,"onUpdate:modelValue":d[7]||(d[7]=o=>a.regionCode=o),placeholder:"请选择区域",disabled:t.value==="view",style:{width:"100%"}},{default:l(()=>[e(s,{label:"市南区",value:"370202"}),e(s,{label:"市北区",value:"370203"}),e(s,{label:"崂山区",value:"370212"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:12},{default:l(()=>[e(i,{label:"区域名称",prop:"regionName"},{default:l(()=>[e(u,{modelValue:a.regionName,"onUpdate:modelValue":d[8]||(d[8]=o=>a.regionName=o),placeholder:"请输入区域名称",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:12},{default:l(()=>[e(i,{label:"联系人",prop:"contactPerson"},{default:l(()=>[e(u,{modelValue:a.contactPerson,"onUpdate:modelValue":d[9]||(d[9]=o=>a.contactPerson=o),placeholder:"请输入联系人",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:12},{default:l(()=>[e(i,{label:"联系电话",prop:"contactPhone"},{default:l(()=>[e(u,{modelValue:a.contactPhone,"onUpdate:modelValue":d[10]||(d[10]=o=>a.contactPhone=o),placeholder:"请输入联系电话",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:l(()=>[e(n,{span:24},{default:l(()=>[e(i,{label:"联系邮箱",prop:"contactEmail"},{default:l(()=>[e(u,{modelValue:a.contactEmail,"onUpdate:modelValue":d[11]||(d[11]=o=>a.contactEmail=o),placeholder:"请输入联系邮箱",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:8},{default:l(()=>[e(i,{label:"入驻企业数",prop:"companyCount"},{default:l(()=>[e(f,{modelValue:a.companyCount,"onUpdate:modelValue":d[12]||(d[12]=o=>a.companyCount=o),disabled:t.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:8},{default:l(()=>[e(i,{label:"可提供工位",prop:"availablePositions"},{default:l(()=>[e(f,{modelValue:a.availablePositions,"onUpdate:modelValue":d[13]||(d[13]=o=>a.availablePositions=o),disabled:t.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:8},{default:l(()=>[e(i,{label:"已占用工位",prop:"occupiedPositions"},{default:l(()=>[e(f,{modelValue:a.occupiedPositions,"onUpdate:modelValue":d[14]||(d[14]=o=>a.occupiedPositions=o),disabled:t.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:12},{default:l(()=>[e(i,{label:"最低租金",prop:"rentPriceMin"},{default:l(()=>[e(f,{modelValue:a.rentPriceMin,"onUpdate:modelValue":d[15]||(d[15]=o=>a.rentPriceMin=o),placeholder:"元/月/平方米",disabled:t.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:12},{default:l(()=>[e(i,{label:"最高租金",prop:"rentPriceMax"},{default:l(()=>[e(f,{modelValue:a.rentPriceMax,"onUpdate:modelValue":d[16]||(d[16]=o=>a.rentPriceMax=o),placeholder:"元/月/平方米",disabled:t.value==="view",style:{width:"100%"},min:0},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:12},{default:l(()=>[e(i,{label:"运营模式",prop:"operationMode"},{default:l(()=>[e(_,{modelValue:a.operationMode,"onUpdate:modelValue":d[17]||(d[17]=o=>a.operationMode=o),placeholder:"请选择运营模式",disabled:t.value==="view",style:{width:"100%"}},{default:l(()=>[e(s,{label:"自营",value:"自营"}),e(s,{label:"委托运营",value:"委托运营"}),e(s,{label:"合作运营",value:"合作运营"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:12},{default:l(()=>[e(i,{label:"行业方向",prop:"industryDirection"},{default:l(()=>[e(u,{modelValue:a.industryDirection,"onUpdate:modelValue":d[18]||(d[18]=o=>a.industryDirection=o),placeholder:"多个用逗号分隔",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:l(()=>[e(n,{span:24},{default:l(()=>[e(i,{label:"场地描述",prop:"description"},{default:l(()=>[e(u,{modelValue:a.description,"onUpdate:modelValue":d[19]||(d[19]=o=>a.description=o),type:"textarea",rows:3,placeholder:"请输入场地详细描述",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,{gutter:20},{default:l(()=>[e(n,{span:8},{default:l(()=>[e(i,{label:"是否推荐",prop:"isFeatured"},{default:l(()=>[e(g,{modelValue:a.isFeatured,"onUpdate:modelValue":d[20]||(d[20]=o=>a.isFeatured=o),disabled:t.value==="view"},{default:l(()=>[e(b,{label:1},{default:l(()=>[m("是")]),_:1}),e(b,{label:0},{default:l(()=>[m("否")]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:8},{default:l(()=>[e(i,{label:"开放入驻",prop:"isOpenSettle"},{default:l(()=>[e(g,{modelValue:a.isOpenSettle,"onUpdate:modelValue":d[21]||(d[21]=o=>a.isOpenSettle=o),disabled:t.value==="view"},{default:l(()=>[e(b,{label:1},{default:l(()=>[m("是")]),_:1}),e(b,{label:0},{default:l(()=>[m("否")]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(n,{span:8},{default:l(()=>[e(i,{label:"状态",prop:"status"},{default:l(()=>[e(g,{modelValue:a.status,"onUpdate:modelValue":d[22]||(d[22]=o=>a.status=o),disabled:t.value==="view"},{default:l(()=>[e(b,{label:"0"},{default:l(()=>[m("正常")]),_:1}),e(b,{label:"1"},{default:l(()=>[m("停用")]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:l(()=>[e(n,{span:24},{default:l(()=>[e(i,{label:"备注",prop:"remark"},{default:l(()=>[e(u,{modelValue:a.remark,"onUpdate:modelValue":d[23]||(d[23]=o=>a.remark=o),type:"textarea",rows:3,placeholder:"请输入内容",disabled:t.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","label-width","size"])]),_:2},[t.value!=="view"?{name:"footer",fn:l(()=>[B("div",R,[e(y,{onClick:D},{default:l(()=>[m("取 消")]),_:1}),e(y,{type:"primary",onClick:A},{default:l(()=>[m("确 定")]),_:1})])]),key:"0"}:void 0]),1032,["title","modelValue","width"])}}},k=T(j,[["__scopeId","data-v-bc3bda11"]]);export{k as default};
