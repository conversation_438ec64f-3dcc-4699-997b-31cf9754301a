import{C as F,u as O,d as z,r as i,R as G,f as s,K as H,c as P,o as y,h as m,i as t,G as C,k as a,j as o,l as q,t as S,H as J,F as V,p as $}from"./index-DP10CBaW.js";import{a as L,u as M}from"./user-qlgfTUyD.js";const Q={class:"app-container"},W=m("h4",{class:"form-header h4"},"基本信息",-1),X=m("h4",{class:"form-header h4"},"角色信息",-1),Y={style:{"text-align":"center","margin-left":"-120px","margin-top":"30px"}},Z=F({name:"AuthRole"}),oe=Object.assign(Z,{setup(ee){const v=O(),{proxy:f}=z(),g=i(!0),b=i(0),u=i(1),c=i(10),h=i([]),_=i([]),r=i({nickName:void 0,userName:void 0,userId:void 0});function T(l){k(l)&&f.$refs.roleRef.toggleRowSelection(l)}function j(l){h.value=l.map(e=>e.roleId)}function B(l){return l.roleId}function k(l){return l.status==="0"}function w(){const l={path:"/system/user"};f.$tab.closeOpenPage(l)}function U(){const l=r.value.userId,e=h.value.join(",");M({userId:l,roleIds:e}).then(d=>{f.$modal.msgSuccess("授权成功"),w()})}return(()=>{const l=v.params&&v.params.userId;l&&(g.value=!0,L(l).then(e=>{r.value=e.user,_.value=e.roles,b.value=_.value.length,G(()=>{_.value.forEach(d=>{d.flag&&f.$refs.roleRef.toggleRowSelection(d)})}),g.value=!1}))})(),(l,e)=>{const d=s("el-input"),R=s("el-form-item"),I=s("el-col"),A=s("el-row"),N=s("el-form"),p=s("el-table-column"),D=s("el-table"),K=s("pagination"),x=s("el-button"),E=H("loading");return y(),P("div",Q,[W,t(N,{model:o(r),"label-width":"80px"},{default:a(()=>[t(A,null,{default:a(()=>[t(I,{span:8,offset:2},{default:a(()=>[t(R,{label:"用户昵称",prop:"nickName"},{default:a(()=>[t(d,{modelValue:o(r).nickName,"onUpdate:modelValue":e[0]||(e[0]=n=>o(r).nickName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),t(I,{span:8,offset:2},{default:a(()=>[t(R,{label:"登录账号",prop:"userName"},{default:a(()=>[t(d,{modelValue:o(r).userName,"onUpdate:modelValue":e[1]||(e[1]=n=>o(r).userName=n),disabled:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"]),X,C((y(),q(D,{"row-key":B,onRowClick:T,ref:"roleRef",onSelectionChange:j,data:o(_).slice((o(u)-1)*o(c),o(u)*o(c))},{default:a(()=>[t(p,{label:"序号",width:"55",type:"index",align:"center"},{default:a(n=>[m("span",null,S((o(u)-1)*o(c)+n.$index+1),1)]),_:1}),t(p,{type:"selection","reserve-selection":!0,selectable:k,width:"55"}),t(p,{label:"角色编号",align:"center",prop:"roleId"}),t(p,{label:"角色名称",align:"center",prop:"roleName"}),t(p,{label:"权限字符",align:"center",prop:"roleKey"}),t(p,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:a(n=>[m("span",null,S(l.parseTime(n.row.createTime)),1)]),_:1})]),_:1},8,["data"])),[[E,o(g)]]),C(t(K,{total:o(b),page:o(u),"onUpdate:page":e[2]||(e[2]=n=>V(u)?u.value=n:null),limit:o(c),"onUpdate:limit":e[3]||(e[3]=n=>V(c)?c.value=n:null)},null,8,["total","page","limit"]),[[J,o(b)>0]]),t(N,{"label-width":"100px"},{default:a(()=>[m("div",Y,[t(x,{type:"primary",class:"custom-btn",onClick:e[4]||(e[4]=n=>U())},{default:a(()=>[$("提交")]),_:1}),t(x,{class:"custom-btn",onClick:e[5]||(e[5]=n=>w())},{default:a(()=>[$("返回")]),_:1})])]),_:1})])}}});export{oe as default};
