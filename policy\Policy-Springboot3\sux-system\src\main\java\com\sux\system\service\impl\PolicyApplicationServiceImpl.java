package com.sux.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.system.domain.PolicyApplication;
import com.sux.system.domain.PolicyApprovalRecord;
import com.sux.system.mapper.PolicyApplicationMapper;
import com.sux.system.mapper.PolicyApprovalRecordMapper;
import com.sux.system.service.IPolicyApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 政策申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class PolicyApplicationServiceImpl extends ServiceImpl<PolicyApplicationMapper, PolicyApplication> implements IPolicyApplicationService {
    @Autowired
    private PolicyApplicationMapper policyApplicationMapper;

    @Autowired
    private PolicyApprovalRecordMapper policyApprovalRecordMapper;

    /**
     * 查询政策申请列表
     *
     * @param policyApplication 政策申请
     * @return 政策申请
     */
    @Override
    public List<PolicyApplication> selectPolicyApplicationList(PolicyApplication policyApplication) {
        return policyApplicationMapper.selectPolicyApplicationList(policyApplication);
    }

    /**
     * 查询政策申请
     *
     * @param applicationId 政策申请主键
     * @return 政策申请
     */
    @Override
    public PolicyApplication selectPolicyApplicationByApplicationId(Long applicationId) {
        return policyApplicationMapper.selectPolicyApplicationByApplicationId(applicationId);
    }

    /**
     * 新增政策申请
     *
     * @param policyApplication 政策申请
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPolicyApplication(PolicyApplication policyApplication) {
        Long currentUserId = SecurityUtils.getUserId();

        policyApplication.setApplicantUserId(currentUserId); // 设置申请人为当前登录用户

        // 如果前端没有传递申请人姓名和手机号，则从用户信息中获取
        if (policyApplication.getApplicantName() == null || policyApplication.getApplicantPhone() == null) {
            // 这里可以从用户服务获取用户信息，暂时使用默认值
            // 实际项目中应该调用用户服务获取真实的用户姓名和手机号
            if (policyApplication.getApplicantName() == null) {
                policyApplication.setApplicantName(""); // 可以从用户表获取真实姓名
            }
            if (policyApplication.getApplicantPhone() == null) {
                policyApplication.setApplicantPhone(""); // 可以从用户表获取真实手机号
            }
        }

        policyApplication.setCreateId(currentUserId);
        policyApplication.setCreateTime(DateUtils.getNowDate());
        policyApplication.setSubmitTime(DateUtils.getNowDate());
        policyApplication.setApplicationStatus("0"); // 默认待初审状态

        int result = policyApplicationMapper.insertPolicyApplication(policyApplication);

        // 创建初审和终审记录
        if (result > 0) {
            createApprovalRecords(policyApplication.getApplicationId());
        }

        return result;
    }

    /**
     * 修改政策申请
     *
     * @param policyApplication 政策申请
     * @return 结果
     */
    @Override
    public int updatePolicyApplication(PolicyApplication policyApplication) {
        policyApplication.setUpdateId(SecurityUtils.getUserId());
        policyApplication.setUpdateTime(DateUtils.getNowDate());
        return policyApplicationMapper.updatePolicyApplication(policyApplication);
    }

    /**
     * 批量删除政策申请
     *
     * @param applicationIds 需要删除的政策申请主键
     * @return 结果
     */
    @Override
    public int deletePolicyApplicationByApplicationIds(Long[] applicationIds) {
        return policyApplicationMapper.deletePolicyApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除政策申请信息
     *
     * @param applicationId 政策申请主键
     * @return 结果
     */
    @Override
    public int deletePolicyApplicationByApplicationId(Long applicationId) {
        return policyApplicationMapper.deletePolicyApplicationByApplicationId(applicationId);
    }

    /**
     * 查询待初审申请列表
     *
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    @Override
    public List<PolicyApplication> selectPendingFirstReviewList(PolicyApplication policyApplication) {
        return policyApplicationMapper.selectPendingFirstReviewList(policyApplication);
    }

    /**
     * 查询待终审申请列表
     *
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    @Override
    public List<PolicyApplication> selectPendingFinalReviewList(PolicyApplication policyApplication) {
        return policyApplicationMapper.selectPendingFinalReviewList(policyApplication);
    }

    /**
     * 查询我的申请列表
     *
     * @param policyApplication 政策申请
     * @param userId            用户ID
     * @return 政策申请集合
     */
    @Override
    public List<PolicyApplication> selectMyApplicationsList(PolicyApplication policyApplication, Long userId) {
        return policyApplicationMapper.selectMyApplicationsList(policyApplication, userId);
    }

    /**
     * 查询所有申请列表（管理员权限）
     *
     * @param policyApplication 政策申请
     * @return 政策申请集合
     */
    @Override
    public List<PolicyApplication> selectAllApplicationsList(PolicyApplication policyApplication) {
        return policyApplicationMapper.selectAllApplicationsList(policyApplication);
    }

    /**
     * 查询未申请的政策列表
     *
     * @param policyApplication 政策申请查询条件
     * @param userId            用户ID
     * @return 政策信息集合（未申请的政策）
     */
    @Override
    public List<PolicyApplication> selectUnappliedPolicies(PolicyApplication policyApplication, Long userId) {
        return policyApplicationMapper.selectUnappliedPolicies(policyApplication, userId);
    }

    /**
     * 查询已申请的政策列表
     *
     * @param policyApplication 政策申请查询条件
     * @param userId            用户ID
     * @return 政策申请集合（已申请的政策及申请信息）
     */
    @Override
    public List<PolicyApplication> selectAppliedPolicies(PolicyApplication policyApplication, Long userId) {
        return policyApplicationMapper.selectAppliedPolicies(policyApplication, userId);
    }

    /**
     * 初审操作
     *
     * @param applicationId   申请ID
     * @param approvalStatus  审批状态（1通过 2拒绝）
     * @param approvalComment 审批意见
     * @param approvalFiles   审批文件
     * @return 结果
     */
    @Override
    @Transactional
    public int firstReview(Long applicationId, String approvalStatus, String approvalComment, String approvalFiles) {
        // 更新审批记录
        PolicyApprovalRecord record = policyApprovalRecordMapper.selectRecordByApplicationIdAndLevel(applicationId, 1);
        if (record != null) {
            policyApprovalRecordMapper.updateApprovalRecord(record.getRecordId(), approvalStatus,
                    SecurityUtils.getUserId(), approvalComment, approvalFiles);
        }

        // 更新申请状态
        String newStatus;
        if ("1".equals(approvalStatus)) {
            // 初审通过，流转到待终审
            newStatus = "3";
        } else {
            // 初审拒绝
            newStatus = "2";
        }

        return policyApplicationMapper.updateApplicationStatus(applicationId, newStatus);
    }

    /**
     * 终审操作
     *
     * @param applicationId   申请ID
     * @param approvalStatus  审批状态（1通过 2拒绝）
     * @param approvalComment 审批意见
     * @param approvalFiles   审批文件
     * @return 结果
     */
    @Override
    @Transactional
    public int finalReview(Long applicationId, String approvalStatus, String approvalComment, String approvalFiles) {
        // 更新审批记录
        PolicyApprovalRecord record = policyApprovalRecordMapper.selectRecordByApplicationIdAndLevel(applicationId, 2);
        if (record != null) {
            policyApprovalRecordMapper.updateApprovalRecord(record.getRecordId(), approvalStatus,
                    SecurityUtils.getUserId(), approvalComment, approvalFiles);
        }

        // 更新申请状态
        String newStatus;
        if ("1".equals(approvalStatus)) {
            // 终审通过，完成申请
            newStatus = "6";
            policyApplicationMapper.completeApplication(applicationId);
        } else {
            // 终审拒绝
            newStatus = "5";
        }

        return policyApplicationMapper.updateApplicationStatus(applicationId, newStatus);
    }

    /**
     * 查询审批记录
     *
     * @param applicationId 申请ID
     * @return 审批记录集合
     */
    @Override
    public List<PolicyApprovalRecord> getApprovalRecords(Long applicationId) {
        return policyApprovalRecordMapper.selectRecordsByApplicationId(applicationId);
    }

    /**
     * 创建审批记录
     *
     * @param applicationId 申请ID
     */
    private void createApprovalRecords(Long applicationId) {
        // 创建初审记录
        PolicyApprovalRecord firstRecord = new PolicyApprovalRecord();
        firstRecord.setApplicationId(applicationId);
        firstRecord.setApprovalLevel(1);
        firstRecord.setApprovalStatus("0"); // 待审批
        firstRecord.setCreateId(SecurityUtils.getUserId());
        firstRecord.setCreateTime(DateUtils.getNowDate());
        policyApprovalRecordMapper.insertPolicyApprovalRecord(firstRecord);

        // 创建终审记录
        PolicyApprovalRecord finalRecord = new PolicyApprovalRecord();
        finalRecord.setApplicationId(applicationId);
        finalRecord.setApprovalLevel(2);
        finalRecord.setApprovalStatus("0"); // 待审批
        finalRecord.setCreateId(SecurityUtils.getUserId());
        finalRecord.setCreateTime(DateUtils.getNowDate());
        policyApprovalRecordMapper.insertPolicyApprovalRecord(finalRecord);
    }
}
