import{_ as F,C as G,d as q,r,f as c,l as g,o as b,k as d,i as o,G as z,c as J,L as P,M as Q,h as x,p as i,H as W,R as S}from"./index-DP10CBaW.js";import{e as X}from"./role-FWqI-CVH.js";const Y={class:"tree-controls"},Z={class:"dialog-footer"},$=G({name:"DataScopeDialog"}),ee=Object.assign($,{emits:["submit","cancel"],setup(le,{expose:K,emit:D}){const{proxy:te}=q(),w=r([{value:"1",label:"全部数据权限"},{value:"2",label:"自定数据权限"},{value:"3",label:"本部门数据权限"},{value:"4",label:"本部门及以下数据权限"},{value:"5",label:"仅本人数据权限"}]),h=D,m=r(!1),N=r("分配数据权限"),t=r({}),u=r(null),v=r([]),f=r(!0),_=r(!1),T=async a=>{t.value={...a,deptCheckStrictly:!0},f.value=!0,_.value=!1,await C(a.roleId),m.value=!0},k=()=>{m.value=!1},C=async a=>{try{const e=await X(a);return v.value=e.depts,S(()=>{e.checkedKeys.forEach(n=>{S(()=>{u.value&&u.value.setChecked(n,!0,!1)})})}),e}catch(e){console.error("获取部门树失败:",e)}},U=a=>{a==="2"?C(t.value.roleId):u.value&&u.value.setCheckedKeys([])},I=(a,e)=>{var s;{let n=v.value;for(let p=0;p<n.length;p++)(s=u.value)!=null&&s.store.nodesMap[n[p].id]&&(u.value.store.nodesMap[n[p].id].expanded=a)}},B=(a,e)=>{var s;(s=u.value)==null||s.setCheckedNodes(a?v.value:[])},E=(a,e)=>{t.value.deptCheckStrictly=!!a},A=()=>{var s,n;let a=((s=u.value)==null?void 0:s.getCheckedKeys())||[],e=((n=u.value)==null?void 0:n.getHalfCheckedKeys())||[];return a.unshift.apply(a,e),a},L=()=>{t.value.dataScope==="2"&&(t.value.deptIds=A()),h("submit",t.value),k()},M=()=>{h("cancel"),k()};return K({openDialog:T,closeDialog:k}),(a,e)=>{const s=c("el-input"),n=c("el-form-item"),p=c("el-option"),O=c("el-select"),y=c("el-checkbox"),R=c("el-tree"),H=c("el-form"),V=c("el-button"),j=c("el-dialog");return b(),g(j,{title:N.value,modelValue:m.value,"onUpdate:modelValue":e[9]||(e[9]=l=>m.value=l),width:"500px","append-to-body":""},{footer:d(()=>[x("div",Z,[o(V,{type:"primary",class:"common-btn",onClick:L},{default:d(()=>[i("确 定")]),_:1}),o(V,{class:"common-btn",onClick:M},{default:d(()=>[i("取 消")]),_:1})])]),default:d(()=>[o(H,{model:t.value,"label-width":"80px"},{default:d(()=>[o(n,{label:"角色名称"},{default:d(()=>[o(s,{modelValue:t.value.roleName,"onUpdate:modelValue":e[0]||(e[0]=l=>t.value.roleName=l),disabled:!0},null,8,["modelValue"])]),_:1}),o(n,{label:"权限字符"},{default:d(()=>[o(s,{modelValue:t.value.roleKey,"onUpdate:modelValue":e[1]||(e[1]=l=>t.value.roleKey=l),disabled:!0},null,8,["modelValue"])]),_:1}),o(n,{label:"权限范围"},{default:d(()=>[o(O,{modelValue:t.value.dataScope,"onUpdate:modelValue":e[2]||(e[2]=l=>t.value.dataScope=l),onChange:U},{default:d(()=>[(b(!0),J(P,null,Q(w.value,l=>(b(),g(p,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),z(o(n,{label:"数据权限"},{default:d(()=>[x("div",Y,[o(y,{modelValue:f.value,"onUpdate:modelValue":e[3]||(e[3]=l=>f.value=l),onChange:e[4]||(e[4]=l=>I(l,"dept"))},{default:d(()=>[i("展开/折叠")]),_:1},8,["modelValue"]),o(y,{modelValue:_.value,"onUpdate:modelValue":e[5]||(e[5]=l=>_.value=l),onChange:e[6]||(e[6]=l=>B(l,"dept"))},{default:d(()=>[i("全选/全不选")]),_:1},8,["modelValue"]),o(y,{modelValue:t.value.deptCheckStrictly,"onUpdate:modelValue":e[7]||(e[7]=l=>t.value.deptCheckStrictly=l),onChange:e[8]||(e[8]=l=>E(l,"dept"))},{default:d(()=>[i("父子联动")]),_:1},8,["modelValue"])]),o(R,{class:"tree-border",data:v.value,"show-checkbox":"","default-expand-all":"",ref_key:"deptRef",ref:u,"node-key":"id","check-strictly":!t.value.deptCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),_:1},512),[[W,t.value.dataScope==2]])]),_:1},8,["model"])]),_:1},8,["title","modelValue"])}}}),ne=F(ee,[["__scopeId","data-v-f3a25e0f"]]);export{ne as default};
