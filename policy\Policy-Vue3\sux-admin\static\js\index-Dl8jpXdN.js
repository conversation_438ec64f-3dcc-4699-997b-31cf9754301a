import{a7 as Pe,C as Fe,a as ze,aj as Ae,d as We,r as n,D as X,ak as Le,I as Be,e as qe,w as je,R as Ee,f as c,K as Me,c as O,o as p,l as k,i as r,j as s,k as l,h as w,G as S,m as W,p as d,a9 as He}from"./index-DP10CBaW.js";import{l as Ke,e as Je,f as Ge,h as Qe,i as Xe,j as Ye,k as Y,r as Ze}from"./user-qlgfTUyD.js";import{g as et,e as tt}from"./columnUtils-DYlA-XL_.js";import{T as at}from"./index-BWWetMd6.js";import st from"./UserFormDialog-zdUtPBDX.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";const lt=L=>{const{sys_user_sex:$,sys_normal_disable:o}=L.useDict("sys_user_sex","sys_normal_disable"),N=[{label:"系统用户",value:"00"},{label:"普通用户",value:"01"}];return{dialogWidth:"800px",dialogHeight:"60vh",labelWidth:"100px",column:[{label:"基础信息",prop:"divider_basic_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"用户编号",prop:"userId",width:120,align:"center",addDisplay:!1,editDisplay:!1,search:!1},{label:"用户名称",prop:"userName",search:!0,searchSpan:12,minWidth:150,rules:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],span:12,editDisplay:!1},{label:"用户昵称",prop:"nickName",search:!1,width:150,rules:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],span:12},{label:"部门",prop:"deptName",minWidth:150,formSlot:!0,search:!0,searchSlot:!0,addDisplay:!0,editDisplay:!0,viewDisplay:!0,span:12,formatter:(m,b,v)=>{var h;return((h=m.dept)==null?void 0:h.deptName)||"-"}},{label:"手机号码",prop:"phonenumber",search:!0,width:120,rules:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],span:12},{label:"邮箱",prop:"email",minWidth:200,rules:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],span:12,search:!0},{label:"用户密码",prop:"password",type:"password",span:12,addDisplay:!0,editDisplay:!1,viewDisplay:!1,showColumn:!1,rules:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\\\ |`,trigger:"blur"}]},{label:"用户性别",prop:"sex",type:"select",span:12,minWidth:100,search:!0,dicData:$,showColumn:!1},{label:"用户类型",prop:"userType",type:"select",span:12,minWidth:120,search:!0,dicData:N,rules:[{required:!0,message:"用户类型不能为空",trigger:"change"}],formatter:(m,b,v)=>{const h=N.find(U=>U.value===v);return h?h.label:v},change:({value:m,column:b})=>({userType:m})},{label:"状态",prop:"status",type:"select",span:12,minWidth:100,search:!0,dicData:o,formSlot:!0,slot:!0},{label:"岗位",prop:"postIds",type:"select",multiple:!0,span:12,formSlot:!0,showColumn:!1,search:!1,display:m=>m.userType==="00"},{label:"角色",prop:"roleIds",type:"select",multiple:!0,span:12,formSlot:!0,showColumn:!1,search:!1,display:m=>m.userType==="00"},{label:"备注",prop:"remark",type:"textarea",span:24,showColumn:!1,search:!1},{label:"创建时间",prop:"createTime",type:"datetime",width:160,align:"center",addDisplay:!1,editDisplay:!1,search:!0,searchRange:!0,formatter:(m,b,v)=>Pe(v)}]}},ot={class:"user-container app-container"},nt={class:"operation-btns"},rt={key:1,class:"loading-placeholder"},it=w("div",{class:"el-upload__text"},[d("将文件拖到此处，或"),w("em",null,"点击上传")],-1),ut={class:"el-upload__tip text-center"},dt={class:"el-upload__tip"},ct=w("span",null,"仅允许导入xls、xlsx格式文件。",-1),pt={class:"dialog-footer"},mt=Fe({name:"User"}),Ct=Object.assign(mt,{setup(L){const $=ze();Ae();const{proxy:o}=We(),{sys_normal_disable:N,sys_user_sex:m}=o.useDict("sys_normal_disable","sys_user_sex"),b=n([]),v=n(!0),h=n([]),U=n(!0),Z=n(!0),B=n(0),ee=n(""),q=n(void 0),j=n(void 0),E=n(void 0),V=n([]),P=n([]),D=n(!0),M=n([]),H=n([]),F=n(!1),z=n(!1),A=n({dialogWidth:"800px",dialogHeight:"60vh"}),te=n(null),T=n(null),K=n([]),_=n({}),u=X({open:!1,title:"",isUploading:!1,updateSupport:0,headers:{"ADMIN-Authorization":"Bearer "+Le()},url:"/sux-admin/system/user/importData"});n([{key:0,label:"用户编号",visible:!0},{key:1,label:"用户名称",visible:!0},{key:2,label:"用户昵称",visible:!0},{key:3,label:"部门",visible:!0},{key:4,label:"手机号码",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"创建时间",visible:!0}]);const ae=X({queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0,email:void 0,sex:void 0,userType:void 0}}),{queryParams:f}=Be(ae);qe(async()=>{await se(),le(),y(),o.getConfigKey("sys.user.initPassword").then(e=>{E.value=e.msg})});const se=async()=>{try{const e=lt(o),t=await et({baseOption:e,proxy:o}),{tableColumns:a,searchColumns:g,formFields:I,formOptions:R}=tt(t);M.value=a,H.value=g,K.value=I,A.value={...A.value,...R},z.value=!0}catch(e){z.value=!1,console.error("初始化配置失败:",e)}};je(ee,e=>{o.$refs.deptTreeRef.filter(e)});function y(){F.value=!0,v.value=!0;let e={...f.value};_.value.createTime&&Array.isArray(_.value.createTime)&&_.value.createTime.length===2&&(e=o.addDateRange(e,_.value.createTime)),Ke(e).then(t=>{F.value=!1,v.value=!1,b.value=t.rows,B.value=t.total,Ee(()=>{D.value=!1})})}function le(){Je().then(e=>{q.value=e.data,j.value=J(JSON.parse(JSON.stringify(e.data)))})}function J(e){return e.filter(t=>t.disabled?!1:(t.children&&t.children.length&&(t.children=J(t.children)),!0))}const oe=e=>{D.value=!0,_.value={...e};const{createTime:t,...a}=e||{};Object.assign(f.value,a),f.value.pageNum=1,y()},ne=()=>{D.value=!0,f.value={pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0,email:void 0,sex:void 0,userType:void 0},_.value={},o.$refs.deptTreeRef&&o.$refs.deptTreeRef.setCurrentKey(null),y()},re=e=>{D.value=!0,f.value.pageNum=e,y()},ie=e=>{D.value=!0,f.value.pageSize=e,f.value.pageNum=1,y()};function ue(e){const t=e.userId||h.value;o.$modal.confirm('是否确认删除用户编号为"'+t+'"的数据项？').then(function(){return Ge(t)}).then(()=>{y(),o.$modal.msgSuccess("删除成功")}).catch(()=>{})}function de(){o.download("system/user/export",{...f.value},`user_${new Date().getTime()}.xlsx`)}function ce(e){if(D.value)return;let t=e.status==="0"?"启用":"停用";o.$modal.confirm('确认要"'+t+'""'+e.userName+'"用户吗?').then(function(){return Qe(e.userId,e.status)}).then(()=>{o.$modal.msgSuccess(t+"成功")}).catch(function(){e.status=e.status==="0"?"1":"0"})}function pe(e,t){switch(e){case"handleResetPwd":fe(t);break;case"handleAuthRole":me(t);break}}function me(e){const t=e.userId;$.push("/system/user-auth/role/"+t)}function fe(e){o.$prompt('请输入"'+e.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间",inputValidator:t=>{if(/<|>|"|'|\||\\/.test(t))return`不能包含非法字符：< > " ' \\ |`}}).then(({value:t})=>{Ze(e.userId,t).then(a=>{o.$modal.msgSuccess("修改成功，新密码是："+t)})}).catch(()=>{})}function he(e){h.value=e.map(t=>t.userId),U.value=e.length!=1,Z.value=!e.length}function ge(){u.title="用户导入",u.open=!0}function ve(){o.download("system/user/importTemplate",{},`user_template_${new Date().getTime()}.xlsx`)}const _e=(e,t,a)=>{u.isUploading=!0},ye=(e,t,a)=>{u.open=!1,u.isUploading=!1,o.$refs.uploadRef.handleRemove(t),o.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),y()};function be(){o.$refs.uploadRef.submit()}const Ce=e=>{var t;(t=T.value)==null||t.openDialog("view","查看用户",e)},G=e=>{const t=e.userId;Y(t).then(a=>{var I;const g={...a.data,postIds:a.postIds,roleIds:a.roleIds};V.value=a.posts,P.value=a.roles,(I=T.value)==null||I.openDialog("edit","编辑用户",g)})},xe=()=>{Y().then(e=>{var a;V.value=e.posts,P.value=e.roles;const t={password:E.value,status:"0",postIds:[],roleIds:[]};(a=T.value)==null||a.openDialog("add","新增用户",t)})},ke=async e=>{var t,a;try{e.type==="add"?(await Xe(e.data),o.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await Ye(e.data),o.$modal.msgSuccess("修改成功")),(t=T.value)==null||t.onSubmitSuccess(),y()}catch(g){(a=T.value)==null||a.onSubmitError(),console.error("提交失败:",g)}},Se=()=>{};function we(){xe()}function De(e){if(e)G(e);else{const t=h.value[0],a=b.value.find(g=>g.userId===t);a&&G(a)}}return(e,t)=>{const a=c("el-button"),g=c("el-tree-select"),I=c("el-switch"),R=c("el-icon"),Q=c("el-dropdown-item"),Te=c("el-dropdown-menu"),Ie=c("el-dropdown"),Re=c("el-empty"),Oe=c("upload-filled"),$e=c("el-checkbox"),Ne=c("el-link"),Ue=c("el-upload"),Ve=c("el-dialog"),C=Me("hasPermi");return p(),O("div",ot,[s(z)?(p(),k(at,{key:0,columns:s(M),data:s(b),loading:s(F),showIndex:!0,searchColumns:s(H),showOperation:!0,operationLabel:"操作",operationWidth:"230",fixedOperation:!0,ref_key:"tableListRef",ref:te,onSearch:oe,onReset:ne,defaultPage:{pageSize:s(f).pageSize,currentPage:s(f).pageNum,total:s(B)},onCurrentChange:re,onSizeChange:ie,onSelectionChange:he},{"menu-left":l(()=>[S((p(),k(a,{type:"primary",class:"custom-btn",onClick:we},{default:l(()=>[d("新 增")]),_:1})),[[C,["system:user:add"]]]),S((p(),k(a,{type:"info",plain:"",class:"custom-btn",onClick:ge},{default:l(()=>[d("导 入")]),_:1})),[[C,["system:user:import"]]]),S((p(),k(a,{type:"warning",plain:"",class:"custom-btn",onClick:de},{default:l(()=>[d("导 出")]),_:1})),[[C,["system:user:export"]]])]),"search-deptName":l(()=>[r(g,{modelValue:s(_).deptId,"onUpdate:modelValue":t[0]||(t[0]=i=>s(_).deptId=i),data:s(q),props:{value:"id",label:"label",children:"children"},"value-key":"id",placeholder:"请选择部门","check-strictly":"",clearable:"",class:"search-form-item"},null,8,["modelValue","data"])]),status:l(({row:i})=>[r(I,{modelValue:i.status,"onUpdate:modelValue":x=>i.status=x,"active-value":"0","inactive-value":"1",onChange:x=>ce(i)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),menu:l(({row:i})=>[w("div",nt,[r(a,{type:"primary",link:"",onClick:x=>Ce(i)},{default:l(()=>[d("查看")]),_:2},1032,["onClick"]),i.userId!==1?S((p(),k(a,{key:0,type:"primary",link:"",onClick:x=>De(i)},{default:l(()=>[d("编辑")]),_:2},1032,["onClick"])),[[C,["system:user:edit"]]]):W("",!0),i.userId!==1?S((p(),k(a,{key:1,type:"danger",link:"",onClick:x=>ue(i)},{default:l(()=>[d("删除")]),_:2},1032,["onClick"])),[[C,["system:user:remove"]]]):W("",!0),i.userId!==1?(p(),k(Ie,{key:2,onCommand:x=>pe(x,i)},{dropdown:l(()=>[r(Te,null,{default:l(()=>[r(Q,{command:"handleResetPwd"},{default:l(()=>[S((p(),O("span",null,[d("重置密码")])),[[C,["system:user:resetPwd"]]])]),_:1}),r(Q,{command:"handleAuthRole"},{default:l(()=>[S((p(),O("span",null,[d("分配角色")])),[[C,["system:user:edit"]]])]),_:1})]),_:1})]),default:l(()=>[r(a,{type:"primary",link:""},{default:l(()=>[d(" 更多"),r(R,{class:"el-icon--right"},{default:l(()=>[r(s(He))]),_:1})]),_:1})]),_:2},1032,["onCommand"])):W("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(p(),O("div",rt,[r(Re,{description:"正在加载表格配置..."})])),r(st,{ref_key:"userFormDialogRef",ref:T,formFields:s(K),formOption:s(A),deptOptions:s(j),postOptions:s(V),roleOptions:s(P),onSubmit:ke,onCancel:Se},null,8,["formFields","formOption","deptOptions","postOptions","roleOptions"]),r(Ve,{title:s(u).title,modelValue:s(u).open,"onUpdate:modelValue":t[3]||(t[3]=i=>s(u).open=i),width:"400px","append-to-body":""},{footer:l(()=>[w("div",pt,[r(a,{type:"primary",class:"common-btn",onClick:be},{default:l(()=>[d("确 定")]),_:1}),r(a,{class:"common-btn",onClick:t[2]||(t[2]=i=>s(u).open=!1)},{default:l(()=>[d("取 消")]),_:1})])]),default:l(()=>[r(Ue,{ref:"uploadRef",limit:1,accept:".xlsx, .xls",headers:s(u).headers,action:s(u).url+"?updateSupport="+s(u).updateSupport,disabled:s(u).isUploading,"on-progress":_e,"on-success":ye,"auto-upload":!1,drag:""},{tip:l(()=>[w("div",ut,[w("div",dt,[r($e,{modelValue:s(u).updateSupport,"onUpdate:modelValue":t[1]||(t[1]=i=>s(u).updateSupport=i)},null,8,["modelValue"]),d("是否更新已经存在的用户数据 ")]),ct,r(Ne,{type:"primary",underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},onClick:ve},{default:l(()=>[d("下载模板")]),_:1})])]),default:l(()=>[r(R,{class:"el-icon--upload"},{default:l(()=>[r(Oe)]),_:1}),it]),_:1},8,["headers","action","disabled"])]),_:1},8,["title","modelValue"])])}}});export{Ct as default};
