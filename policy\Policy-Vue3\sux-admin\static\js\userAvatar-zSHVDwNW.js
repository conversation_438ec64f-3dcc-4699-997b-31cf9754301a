import{C as U,c as M,o as Y,m as I,h as m,G as E,H as T,Z as S,N as R,t as $,_ as D,b as F,d as V,r as L,D as j,f as H,i as C,j as y,k as x,l as q,p as N,F as G,x as Z,y as J}from"./index-DP10CBaW.js";import{b as K}from"./user-qlgfTUyD.js";const A={};A.getData=t=>new Promise((e,s)=>{let i={};Q(t).then(o=>{i.arrayBuffer=o;try{i.orientation=it(o)}catch{i.orientation=-1}e(i)}).catch(o=>{s(o)})});function Q(t){let e=null;return new Promise((s,i)=>{if(t.src)if(/^data\:/i.test(t.src))e=et(t.src),s(e);else if(/^blob\:/i.test(t.src)){var o=new FileReader;o.onload=function(r){e=r.target.result,s(e)},tt(t.src,function(r){o.readAsArrayBuffer(r)})}else{var h=new XMLHttpRequest;h.onload=function(){if(this.status==200||this.status===0)e=h.response,s(e);else throw"Could not load image";h=null},h.open("GET",t.src,!0),h.responseType="arraybuffer",h.send(null)}else i("img error")})}function tt(t,e){var s=new XMLHttpRequest;s.open("GET",t,!0),s.responseType="blob",s.onload=function(i){(this.status==200||this.status===0)&&e(this.response)},s.send()}function et(t,e){e=e||t.match(/^data\:([^\;]+)\;base64,/mi)[1]||"",t=t.replace(/^data\:([^\;]+)\;base64,/gmi,"");for(var s=atob(t),i=s.length%2==0?s.length:s.length+1,o=new ArrayBuffer(i),h=new Uint16Array(o),r=0;r<i;r++)h[r]=s.charCodeAt(r);return o}function st(t,e,s){var i="",o;for(o=e,s+=e;o<s;o++)i+=String.fromCharCode(t.getUint8(o));return i}function it(t){var e=new DataView(t),s=e.byteLength,i,o,h,r,c,l,n,a,p,u;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(p=2;p<s;){if(e.getUint8(p)===255&&e.getUint8(p+1)===225){n=p;break}p++}if(n&&(o=n+4,h=n+10,st(e,o,4)==="Exif"&&(l=e.getUint16(h),c=l===18761,(c||l===19789)&&e.getUint16(h+2,c)===42&&(r=e.getUint32(h+4,c),r>=8&&(a=h+r)))),a){for(s=e.getUint16(a,c),u=0;u<s;u++)if(p=a+u*12+2,e.getUint16(p,c)===274){p+=8,i=e.getUint16(p,c);break}}return i}const ot=(t,e)=>{const s=t.__vccOpts||t;for(const[i,o]of e)s[i]=o;return s},rt=U({data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:()=>[1,1]},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:()=>10,validator:function(t){return Array.isArray(t)?Number(t[0])>=0&&Number(t[1])>=0:Number(t)>=0}},fillColor:{type:String,default:""}},computed:{cropInfo(){let t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){let e=1;this.high&&!this.full&&(e=window.devicePixelRatio),this.enlarge!==1&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE(){return!!window.ActiveXObject||"ActiveXObject"in window},passive(){return this.isIE?null:{passive:!1}}},watch:{img(){this.checkedImg()},imgs(t){t!==""&&this.reload()},cropW(){this.showPreview()},cropH(){this.showPreview()},cropOffsertX(){this.showPreview()},cropOffsertY(){this.showPreview()},scale(t,e){this.showPreview()},x(){this.showPreview()},y(){this.showPreview()},autoCrop(t){t&&this.goAutoCrop()},autoCropWidth(){this.autoCrop&&this.goAutoCrop()},autoCropHeight(){this.autoCrop&&this.goAutoCrop()},mode(){this.checkedImg()},rotate(){this.showPreview(),this.autoCrop?this.goAutoCrop(this.cropW,this.cropH):(this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion(t){var e=navigator.userAgent.split(" "),s="";let i=0;const o=new RegExp(t,"i");for(var h=0;h<e.length;h++)o.test(e[h])&&(s=e[h]);return s?i=s.split("/")[1].split("."):i=["0","0","0"],i},checkOrientationImage(t,e,s,i){if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){const r=this.getVersion("version");r[0]>13&&r[1]>1&&(e=-1)}else{const r=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(r){let c=r[1];c=c.split("_"),(c[0]>13||c[0]>=13&&c[1]>=4)&&(e=-1)}}let o=document.createElement("canvas"),h=o.getContext("2d");switch(h.save(),e){case 2:o.width=s,o.height=i,h.translate(s,0),h.scale(-1,1);break;case 3:o.width=s,o.height=i,h.translate(s/2,i/2),h.rotate(180*Math.PI/180),h.translate(-s/2,-i/2);break;case 4:o.width=s,o.height=i,h.translate(0,i),h.scale(1,-1);break;case 5:o.height=s,o.width=i,h.rotate(.5*Math.PI),h.scale(1,-1);break;case 6:o.width=i,o.height=s,h.translate(i/2,s/2),h.rotate(90*Math.PI/180),h.translate(-s/2,-i/2);break;case 7:o.height=s,o.width=i,h.rotate(.5*Math.PI),h.translate(s,-i),h.scale(-1,1);break;case 8:o.height=s,o.width=i,h.translate(i/2,s/2),h.rotate(-90*Math.PI/180),h.translate(-s/2,-i/2);break;default:o.width=s,o.height=i}h.drawImage(t,0,0,s,i),h.restore(),o.toBlob(r=>{let c=URL.createObjectURL(r);URL.revokeObjectURL(this.imgs),this.imgs=c},"image/"+this.outputType,1)},checkedImg(){if(this.img===null||this.img===""){this.imgs="",this.clearCrop();return}this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop();let t=new Image;if(t.onload=()=>{if(this.img==="")return this.$emit("img-load",new Error("图片不能为空")),!1;let s=t.width,i=t.height;A.getData(t).then(o=>{this.orientation=o.orientation||1;let h=Number(this.maxImgSize);if(!this.orientation&&s<h&i<h){this.imgs=this.img;return}s>h&&(i=i/s*h,s=h),i>h&&(s=s/i*h,i=h),this.checkOrientationImage(t,this.orientation,s,i)}).catch(o=>{this.$emit("img-load","error"),this.$emit("img-load-error",o)})},t.onerror=s=>{this.$emit("img-load","error"),this.$emit("img-load-error",s)},this.img.substr(0,4)!=="data"&&(t.crossOrigin=""),this.isIE){var e=new XMLHttpRequest;e.onload=function(){var s=URL.createObjectURL(this.response);t.src=s},e.open("GET",this.img,!0),e.responseType="blob",e.send()}else t.src=this.img},startMove(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=("clientY"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),t.touches.length==2&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale(t){t.preventDefault();let e=this.scale;var s={x:this.touches[0].clientX,y:this.touches[0].clientY},i={x:t.touches[0].clientX,y:t.touches[0].clientY},o={x:this.touches[1].clientX,y:this.touches[1].clientY},h={x:t.touches[1].clientX,y:t.touches[1].clientY},r=Math.sqrt(Math.pow(s.x-o.x,2)+Math.pow(s.y-o.y,2)),c=Math.sqrt(Math.pow(i.x-h.x,2)+Math.pow(i.y-h.y,2)),l=c-r,n=1;n=n/this.trueWidth>n/this.trueHeight?n/this.trueHeight:n/this.trueWidth,n=n>.1?.1:n;var a=n*l;if(!this.touchNow){if(this.touchNow=!0,l>0?e+=Math.abs(a):l<0&&e>Math.abs(a)&&(e-=Math.abs(a)),this.touches=t.touches,setTimeout(()=>{this.touchNow=!1},8),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e}},cancelTouchScale(t){window.removeEventListener("touchmove",this.touchScale)},moveImg(t){if(t.preventDefault(),t.touches&&t.touches.length===2)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;let e="clientX"in t?t.clientX:t.touches[0].clientX,s="clientY"in t?t.clientY:t.touches[0].clientY,i,o;i=e-this.moveX,o=s-this.moveY,this.$nextTick(()=>{if(this.centerBox){let h=this.getImgAxis(i,o,this.scale),r=this.getCropAxis(),c=this.trueHeight*this.scale,l=this.trueWidth*this.scale,n,a,p,u;switch(this.rotate){case 1:case-1:case 3:case-3:n=this.cropOffsertX-this.trueWidth*(1-this.scale)/2+(c-l)/2,a=this.cropOffsertY-this.trueHeight*(1-this.scale)/2+(l-c)/2,p=n-c+this.cropW,u=a-l+this.cropH;break;default:n=this.cropOffsertX-this.trueWidth*(1-this.scale)/2,a=this.cropOffsertY-this.trueHeight*(1-this.scale)/2,p=n-l+this.cropW,u=a-c+this.cropH;break}h.x1>=r.x1&&(i=n),h.y1>=r.y1&&(o=a),h.x2<=r.x2&&(i=p),h.y2<=r.y2&&(o=u)}this.x=i,this.y=o,this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})})},leaveImg(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize(t){t.preventDefault();let e=this.scale;var s=t.deltaY||t.wheelDelta,i=navigator.userAgent.indexOf("Firefox");s=i>0?s*30:s,this.isIE&&(s=-s);var o=this.coe;o=o/this.trueWidth>o/this.trueHeight?o/this.trueHeight:o/this.trueWidth;var h=o*s;h<0?e+=Math.abs(h):e>Math.abs(h)&&(e-=Math.abs(h));let r=h<0?"add":"reduce";if(r!==this.coeStatus&&(this.coeStatus=r,this.coe=.2),this.scaling||(this.scalingSet=setTimeout(()=>{this.scaling=!1,this.coe=this.coe+=.01},50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},changeScale(t){let e=this.scale;t=t||1;var s=20;if(s=s/this.trueWidth>s/this.trueHeight?s/this.trueHeight:s/this.trueWidth,t=t*s,t>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick(()=>{var i=e-this.cropX,o=s-this.cropY;if(i>0?(this.cropW=i+this.cropChangeX>this.w?this.w-this.cropChangeX:i,this.cropOffsertX=this.cropChangeX):(this.cropW=this.w-this.cropChangeX+Math.abs(i)>this.w?this.cropChangeX:Math.abs(i),this.cropOffsertX=this.cropChangeX+i>0?this.cropChangeX+i:0),!this.fixed)o>0?(this.cropH=o+this.cropChangeY>this.h?this.h-this.cropChangeY:o,this.cropOffsertY=this.cropChangeY):(this.cropH=this.h-this.cropChangeY+Math.abs(o)>this.h?this.cropChangeY:Math.abs(o),this.cropOffsertY=this.cropChangeY+o>0?this.cropChangeY+o:0);else{var h=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];h+this.cropOffsertY>this.h?(this.cropH=this.h-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],i>0?this.cropOffsertX=this.cropChangeX:this.cropOffsertX=this.cropChangeX-this.cropW):this.cropH=h,this.cropOffsertY=this.cropOffsertY}})},changeCropSize(t,e,s,i,o){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=s,this.changeCropTypeX=i,this.changeCropTypeY=o,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow(t){t.preventDefault();var e="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,s="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;let i=this.w,o=this.h,h=0,r=0;if(this.centerBox){let n=this.getImgAxis(),a=n.x2,p=n.y2;h=n.x1>0?n.x1:0,r=n.y1>0?n.y1:0,i>a&&(i=a),o>p&&(o=p)}const[c,l]=this.checkCropLimitSize();this.$nextTick(()=>{var n=e-this.cropX,a=s-this.cropY;if(this.canChangeX&&(this.changeCropTypeX===1?this.cropOldW-n<c?(this.cropW=c,this.cropOffsertX=this.cropOldW+this.cropChangeX-h-c):this.cropOldW-n>0?(this.cropW=i-this.cropChangeX-n<=i-h?this.cropOldW-n:this.cropOldW+this.cropChangeX-h,this.cropOffsertX=i-this.cropChangeX-n<=i-h?this.cropChangeX+n:h):(this.cropW=Math.abs(n)+this.cropChangeX<=i?Math.abs(n)-this.cropOldW:i-this.cropOldW-this.cropChangeX,this.cropOffsertX=this.cropChangeX+this.cropOldW):this.changeCropTypeX===2&&(this.cropOldW+n<c?this.cropW=c:this.cropOldW+n>0?(this.cropW=this.cropOldW+n+this.cropOffsertX<=i?this.cropOldW+n:i-this.cropOffsertX,this.cropOffsertX=this.cropChangeX):(this.cropW=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-h?Math.abs(n+this.cropOldW):this.cropChangeX-h,this.cropOffsertX=i-this.cropChangeX+Math.abs(n+this.cropOldW)<=i-h?this.cropChangeX-Math.abs(n+this.cropOldW):h))),this.canChangeY&&(this.changeCropTypeY===1?this.cropOldH-a<l?(this.cropH=l,this.cropOffsertY=this.cropOldH+this.cropChangeY-r-l):this.cropOldH-a>0?(this.cropH=o-this.cropChangeY-a<=o-r?this.cropOldH-a:this.cropOldH+this.cropChangeY-r,this.cropOffsertY=o-this.cropChangeY-a<=o-r?this.cropChangeY+a:r):(this.cropH=Math.abs(a)+this.cropChangeY<=o?Math.abs(a)-this.cropOldH:o-this.cropOldH-this.cropChangeY,this.cropOffsertY=this.cropChangeY+this.cropOldH):this.changeCropTypeY===2&&(this.cropOldH+a<l?this.cropH=l:this.cropOldH+a>0?(this.cropH=this.cropOldH+a+this.cropOffsertY<=o?this.cropOldH+a:o-this.cropOffsertY,this.cropOffsertY=this.cropChangeY):(this.cropH=o-this.cropChangeY+Math.abs(a+this.cropOldH)<=o-r?Math.abs(a+this.cropOldH):this.cropChangeY-r,this.cropOffsertY=o-this.cropChangeY+Math.abs(a+this.cropOldH)<=o-r?this.cropChangeY-Math.abs(a+this.cropOldH):r))),this.canChangeX&&this.fixed){var p=this.cropW/this.fixedNumber[0]*this.fixedNumber[1];p<l?(this.cropH=l,this.cropW=this.fixedNumber[0]*l/this.fixedNumber[1],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):p+this.cropOffsertY>o?(this.cropH=o-this.cropOffsertY,this.cropW=this.cropH/this.fixedNumber[1]*this.fixedNumber[0],this.changeCropTypeX===1&&(this.cropOffsertX=this.cropChangeX+(this.cropOldW-this.cropW))):this.cropH=p}if(this.canChangeY&&this.fixed){var u=this.cropH/this.fixedNumber[1]*this.fixedNumber[0];u<c?(this.cropW=c,this.cropH=this.fixedNumber[1]*c/this.fixedNumber[0],this.cropOffsertY=this.cropOldH+this.cropChangeY-this.cropH):u+this.cropOffsertX>i?(this.cropW=i-this.cropOffsertX,this.cropH=this.cropW/this.fixedNumber[0]*this.fixedNumber[1]):this.cropW=u}})},checkCropLimitSize(){let{cropW:t,cropH:e,limitMinSize:s}=this,i=new Array;return Array.isArray(s)?i=s:i=[s,s],t=parseFloat(i[0]),e=parseFloat(i[1]),[t,e]},changeCropEnd(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize(t,e,s,i,o,h){const r=t/e;let c=o,l=h;return c<s&&(c=s,l=Math.ceil(c/r)),l<i&&(l=i,c=Math.ceil(l*r),c<s&&(c=s,l=Math.ceil(c/r))),c<o&&(c=o,l=Math.ceil(c/r)),l<h&&(l=h,c=Math.ceil(l*r)),{width:c,height:l}},endCrop(){this.cropW===0&&this.cropH===0&&(this.cropping=!1);let[t,e]=this.checkCropLimitSize();const{width:s,height:i}=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],t,e,this.cropW,this.cropH):{width:t,height:e};s>this.cropW&&(this.cropW=s,this.cropOffsertX+s>this.w&&(this.cropOffsertX=this.w-s)),i>this.cropH&&(this.cropH=i,this.cropOffsertY+i>this.h&&(this.cropOffsertY=this.h-i)),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop(){this.crop=!0},stopCrop(){this.crop=!1},clearCrop(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&t.touches.length===2)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);let e="clientX"in t?t.clientX:t.touches[0].clientX,s="clientY"in t?t.clientY:t.touches[0].clientY,i,o;i=e-this.cropOffsertX,o=s-this.cropOffsertY,this.cropX=i,this.cropY=o,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop(t,e){let s=0,i=0;t&&(t.preventDefault(),s="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY),this.$nextTick(()=>{let o,h,r=s-this.cropX,c=i-this.cropY;if(e&&(r=this.cropOffsertX,c=this.cropOffsertY),r<=0?o=0:r+this.cropW>this.w?o=this.w-this.cropW:o=r,c<=0?h=0:c+this.cropH>this.h?h=this.h-this.cropH:h=c,this.centerBox){let l=this.getImgAxis();o<=l.x1&&(o=l.x1),o+this.cropW>l.x2&&(o=l.x2-this.cropW),h<=l.y1&&(h=l.y1),h+this.cropH>l.y2&&(h=l.y2-this.cropH)}this.cropOffsertX=o,this.cropOffsertY=h,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})})},getImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i={x1:0,x2:0,y1:0,y2:0},o=this.trueWidth*s,h=this.trueHeight*s;switch(this.rotate){case 0:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s;break;case 1:case-1:case 3:case-3:i.x1=t+this.trueWidth*(1-s)/2+(o-h)/2,i.x2=i.x1+this.trueHeight*s,i.y1=e+this.trueHeight*(1-s)/2+(h-o)/2,i.y2=i.y1+this.trueWidth*s;break;default:i.x1=t+this.trueWidth*(1-s)/2,i.x2=i.x1+this.trueWidth*s,i.y1=e+this.trueHeight*(1-s)/2,i.y2=i.y1+this.trueHeight*s;break}return i},getCropAxis(){let t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked(t){let e=document.createElement("canvas"),s=new Image,i=this.rotate,o=this.trueWidth,h=this.trueHeight,r=this.cropOffsertX,c=this.cropOffsertY;s.onload=()=>{if(this.cropW!==0){let a=e.getContext("2d"),p=1;this.high&!this.full&&(p=window.devicePixelRatio),this.enlarge!==1&!this.full&&(p=Math.abs(Number(this.enlarge)));let u=this.cropW*p,O=this.cropH*p,f=o*this.scale*p,g=h*this.scale*p,v=(this.x-r+this.trueWidth*(1-this.scale)/2)*p,d=(this.y-c+this.trueHeight*(1-this.scale)/2)*p;switch(n(u,O),a.save(),this.fillColor&&(a.fillStyle=this.fillColor,a.fillRect(0,0,e.width,e.height)),i){case 0:this.full?(n(u/this.scale,O/this.scale),a.drawImage(s,v/this.scale,d/this.scale,f/this.scale,g/this.scale)):a.drawImage(s,v,d,f,g);break;case 1:case-3:this.full?(n(u/this.scale,O/this.scale),v=v/this.scale+(f/this.scale-g/this.scale)/2,d=d/this.scale+(g/this.scale-f/this.scale)/2,a.rotate(i*90*Math.PI/180),a.drawImage(s,d,-v-g/this.scale,f/this.scale,g/this.scale)):(v=v+(f-g)/2,d=d+(g-f)/2,a.rotate(i*90*Math.PI/180),a.drawImage(s,d,-v-g,f,g));break;case 2:case-2:this.full?(n(u/this.scale,O/this.scale),a.rotate(i*90*Math.PI/180),v=v/this.scale,d=d/this.scale,a.drawImage(s,-v-f/this.scale,-d-g/this.scale,f/this.scale,g/this.scale)):(a.rotate(i*90*Math.PI/180),a.drawImage(s,-v-f,-d-g,f,g));break;case 3:case-1:this.full?(n(u/this.scale,O/this.scale),v=v/this.scale+(f/this.scale-g/this.scale)/2,d=d/this.scale+(g/this.scale-f/this.scale)/2,a.rotate(i*90*Math.PI/180),a.drawImage(s,-d-f/this.scale,v,f/this.scale,g/this.scale)):(v=v+(f-g)/2,d=d+(g-f)/2,a.rotate(i*90*Math.PI/180),a.drawImage(s,-d-f,v,f,g));break;default:this.full?(n(u/this.scale,O/this.scale),a.drawImage(s,v/this.scale,d/this.scale,f/this.scale,g/this.scale)):a.drawImage(s,v,d,f,g)}a.restore()}else{let a=o*this.scale,p=h*this.scale,u=e.getContext("2d");switch(u.save(),this.fillColor&&(u.fillStyle=this.fillColor,u.fillRect(0,0,e.width,e.height)),i){case 0:n(a,p),u.drawImage(s,0,0,a,p);break;case 1:case-3:n(p,a),u.rotate(i*90*Math.PI/180),u.drawImage(s,0,-p,a,p);break;case 2:case-2:n(a,p),u.rotate(i*90*Math.PI/180),u.drawImage(s,-a,-p,a,p);break;case 3:case-1:n(p,a),u.rotate(i*90*Math.PI/180),u.drawImage(s,-a,0,a,p);break;default:n(a,p),u.drawImage(s,0,0,a,p)}u.restore()}t(e)};var l=this.img.substr(0,4);l!=="data"&&(s.crossOrigin="Anonymous"),s.src=this.imgs;function n(a,p){e.width=Math.round(a),e.height=Math.round(p)}},getCropData(t){this.getCropChecked(e=>{t(e.toDataURL("image/"+this.outputType,this.outputSize))})},getCropBlob(t){this.getCropChecked(e=>{e.toBlob(s=>t(s),"image/"+this.outputType,this.outputSize)})},showPreview(){if(this.isCanShow)this.isCanShow=!1,setTimeout(()=>{this.isCanShow=!0},16);else return!1;let t=this.cropW,e=this.cropH,s=this.scale;var i={};i.div={width:`${t}px`,height:`${e}px`};let o=(this.x-this.cropOffsertX)/s,h=(this.y-this.cropOffsertY)/s,r=0;i.w=t,i.h=e,i.url=this.imgs,i.img={width:`${this.trueWidth}px`,height:`${this.trueHeight}px`,transform:`scale(${s})translate3d(${o}px, ${h}px, ${r}px)rotateZ(${this.rotate*90}deg)`},i.html=`
      <div class="show-preview" style="width: ${i.w}px; height: ${i.h}px,; overflow: hidden">
        <div style="width: ${t}px; height: ${e}px">
          <img src=${i.url} style="width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:
          scale(${s})translate3d(${o}px, ${h}px, ${r}px)rotateZ(${this.rotate*90}deg)">
        </div>
      </div>`,this.$emit("real-time",i)},reload(){let t=new Image;t.onload=()=>{this.w=parseFloat(window.getComputedStyle(this.$refs.cropper).width),this.h=parseFloat(window.getComputedStyle(this.$refs.cropper).height),this.trueWidth=t.width,this.trueHeight=t.height,this.original?this.scale=1:this.scale=this.checkedMode(),this.$nextTick(()=>{this.x=-(this.trueWidth-this.trueWidth*this.scale)/2+(this.w-this.trueWidth*this.scale)/2,this.y=-(this.trueHeight-this.trueHeight*this.scale)/2+(this.h-this.trueHeight*this.scale)/2,this.loading=!1,this.autoCrop&&this.goAutoCrop(),this.$emit("img-load","success"),setTimeout(()=>{this.showPreview()},20)})},t.onerror=()=>{this.$emit("img-load","error")},t.src=this.imgs},checkedMode(){let t=1,e=this.trueWidth,s=this.trueHeight;const i=this.mode.split(" ");switch(i[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":e=this.w,t=e/this.trueWidth,s=s*t,s<this.h&&(s=this.h,t=s/this.trueHeight);break;default:try{let o=i[0];if(o.search("px")!==-1){o=o.replace("px",""),e=parseFloat(o);const h=e/this.trueWidth;let r=1,c=i[1];c.search("px")!==-1&&(c=c.replace("px",""),s=parseFloat(c),r=s/this.trueHeight),t=Math.min(h,r)}if(o.search("%")!==-1&&(o=o.replace("%",""),e=parseFloat(o)/100*this.w,t=e/this.trueWidth),i.length===2&&o==="auto"){let h=i[1];h.search("px")!==-1&&(h=h.replace("px",""),s=parseFloat(h),t=s/this.trueHeight),h.search("%")!==-1&&(h=h.replace("%",""),s=parseFloat(h)/100*this.h,t=s/this.trueHeight)}}catch{t=1}}return t},goAutoCrop(t,e){if(this.imgs===""||this.imgs===null)return;this.clearCrop(),this.cropping=!0;let s=this.w,i=this.h;if(this.centerBox){const r=Math.abs(this.rotate)%2>0;let c=(r?this.trueHeight:this.trueWidth)*this.scale,l=(r?this.trueWidth:this.trueHeight)*this.scale;s=c<s?c:s,i=l<i?l:i}var o=t||parseFloat(this.autoCropWidth),h=e||parseFloat(this.autoCropHeight);(o===0||h===0)&&(o=s*.8,h=i*.8),o=o>s?s:o,h=h>i?i:h,this.fixed&&(h=o/this.fixedNumber[0]*this.fixedNumber[1]),h>this.h&&(h=this.h,o=h/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(o,h)},changeCrop(t,e){if(this.centerBox){let s=this.getImgAxis();t>s.x2-s.x1&&(t=s.x2-s.x1,e=t/this.fixedNumber[0]*this.fixedNumber[1]),e>s.y2-s.y1&&(e=s.y2-s.y1,t=e/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick(()=>{this.cropOffsertX=(this.w-this.cropW)/2,this.cropOffsertY=(this.h-this.cropH)/2,this.centerBox&&this.moveCrop(null,!0)})},refresh(){this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick(()=>{this.checkedImg()})},rotateLeft(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear(){this.rotate=0},checkoutImgAxis(t,e,s){t=t||this.x,e=e||this.y,s=s||this.scale;let i=!0;if(this.centerBox){let o=this.getImgAxis(t,e,s),h=this.getCropAxis();o.x1>=h.x1&&(i=!1),o.x2<=h.x2&&(i=!1),o.y1>=h.y1&&(i=!1),o.y2<=h.y2&&(i=!1)}return i}},mounted(){this.support="onwheel"in document.createElement("div")?"wheel":document.onmousewheel!==void 0?"mousewheel":"DOMMouseScroll";let t=this;var e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(s,i,o){for(var h=atob(this.toDataURL(i,o).split(",")[1]),r=h.length,c=new Uint8Array(r),l=0;l<r;l++)c[l]=h.charCodeAt(l);s(new Blob([c],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},unmounted(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}}),ht={key:0,class:"cropper-box"},at=["src"],ct={class:"cropper-view-box"},nt=["src"],lt={key:1};function pt(t,e,s,i,o,h){return Y(),M("div",{class:"vue-cropper",ref:"cropper",onMouseover:e[28]||(e[28]=(...r)=>t.scaleImg&&t.scaleImg(...r)),onMouseout:e[29]||(e[29]=(...r)=>t.cancelScale&&t.cancelScale(...r))},[t.imgs?(Y(),M("div",ht,[E(m("div",{class:"cropper-box-canvas",style:S({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+t.rotate*90+"deg)"})},[m("img",{src:t.imgs,alt:"cropper-img",ref:"cropperImg"},null,8,at)],4),[[T,!t.loading]])])):I("",!0),m("div",{class:R(["cropper-drag-box",{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping}]),onMousedown:e[0]||(e[0]=(...r)=>t.startMove&&t.startMove(...r)),onTouchstart:e[1]||(e[1]=(...r)=>t.startMove&&t.startMove(...r))},null,34),E(m("div",{class:"cropper-crop-box",style:S({width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"})},[m("span",ct,[m("img",{style:S({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+t.rotate*90+"deg)"}),src:t.imgs,alt:"cropper-img"},null,12,nt)]),m("span",{class:"cropper-face cropper-move",onMousedown:e[2]||(e[2]=(...r)=>t.cropMove&&t.cropMove(...r)),onTouchstart:e[3]||(e[3]=(...r)=>t.cropMove&&t.cropMove(...r))},null,32),t.info?(Y(),M("span",{key:0,class:"crop-info",style:S({top:t.cropInfo.top})},$(t.cropInfo.width)+" × "+$(t.cropInfo.height),5)):I("",!0),t.fixedBox?I("",!0):(Y(),M("span",lt,[m("span",{class:"crop-line line-w",onMousedown:e[4]||(e[4]=r=>t.changeCropSize(r,!1,!0,0,1)),onTouchstart:e[5]||(e[5]=r=>t.changeCropSize(r,!1,!0,0,1))},null,32),m("span",{class:"crop-line line-a",onMousedown:e[6]||(e[6]=r=>t.changeCropSize(r,!0,!1,1,0)),onTouchstart:e[7]||(e[7]=r=>t.changeCropSize(r,!0,!1,1,0))},null,32),m("span",{class:"crop-line line-s",onMousedown:e[8]||(e[8]=r=>t.changeCropSize(r,!1,!0,0,2)),onTouchstart:e[9]||(e[9]=r=>t.changeCropSize(r,!1,!0,0,2))},null,32),m("span",{class:"crop-line line-d",onMousedown:e[10]||(e[10]=r=>t.changeCropSize(r,!0,!1,2,0)),onTouchstart:e[11]||(e[11]=r=>t.changeCropSize(r,!0,!1,2,0))},null,32),m("span",{class:"crop-point point1",onMousedown:e[12]||(e[12]=r=>t.changeCropSize(r,!0,!0,1,1)),onTouchstart:e[13]||(e[13]=r=>t.changeCropSize(r,!0,!0,1,1))},null,32),m("span",{class:"crop-point point2",onMousedown:e[14]||(e[14]=r=>t.changeCropSize(r,!1,!0,0,1)),onTouchstart:e[15]||(e[15]=r=>t.changeCropSize(r,!1,!0,0,1))},null,32),m("span",{class:"crop-point point3",onMousedown:e[16]||(e[16]=r=>t.changeCropSize(r,!0,!0,2,1)),onTouchstart:e[17]||(e[17]=r=>t.changeCropSize(r,!0,!0,2,1))},null,32),m("span",{class:"crop-point point4",onMousedown:e[18]||(e[18]=r=>t.changeCropSize(r,!0,!1,1,0)),onTouchstart:e[19]||(e[19]=r=>t.changeCropSize(r,!0,!1,1,0))},null,32),m("span",{class:"crop-point point5",onMousedown:e[20]||(e[20]=r=>t.changeCropSize(r,!0,!1,2,0)),onTouchstart:e[21]||(e[21]=r=>t.changeCropSize(r,!0,!1,2,0))},null,32),m("span",{class:"crop-point point6",onMousedown:e[22]||(e[22]=r=>t.changeCropSize(r,!0,!0,1,2)),onTouchstart:e[23]||(e[23]=r=>t.changeCropSize(r,!0,!0,1,2))},null,32),m("span",{class:"crop-point point7",onMousedown:e[24]||(e[24]=r=>t.changeCropSize(r,!1,!0,0,2)),onTouchstart:e[25]||(e[25]=r=>t.changeCropSize(r,!1,!0,0,2))},null,32),m("span",{class:"crop-point point8",onMousedown:e[26]||(e[26]=r=>t.changeCropSize(r,!0,!0,2,2)),onTouchstart:e[27]||(e[27]=r=>t.changeCropSize(r,!0,!0,2,2))},null,32)]))],4),[[T,t.cropping]])],544)}const ut=ot(rt,[["render",pt],["__scopeId","data-v-69939069"]]),dt=t=>(Z("data-v-494c65e6"),t=t(),J(),t),ft=["src"],gt={class:"avatar-upload-preview"},mt=["src"],vt=dt(()=>m("br",null,null,-1)),wt={__name:"userAvatar",setup(t){const e=F(),{proxy:s}=V(),i=L(!1),o=L(!1),h=L("修改头像"),r=j({img:e.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png",filename:"avatar",previews:{}});function c(){i.value=!0}function l(){o.value=!0}function n(){}function a(){s.$refs.cropper.rotateLeft()}function p(){s.$refs.cropper.rotateRight()}function u(d){d=d||1,s.$refs.cropper.changeScale(d)}function O(d){if(d.type.indexOf("image/")==-1)s.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");else{const w=new FileReader;w.readAsDataURL(d),w.onload=()=>{r.img=w.result,r.filename=d.name}}}function f(){s.$refs.cropper.getCropBlob(d=>{let w=new FormData;w.append("avatarfile",d,r.filename),K(w).then(b=>{i.value=!1,r.img="/sux-admin"+b.imgUrl,e.avatar=r.img,s.$modal.msgSuccess("修改成功"),o.value=!1})})}function g(d){r.previews=d}function v(){r.img=e.avatar,r.visible=!1}return(d,w)=>{const b=H("el-col"),k=H("el-row"),_=H("Upload"),z=H("el-icon"),W=H("el-button"),B=H("el-upload"),P=H("el-dialog");return Y(),M("div",{class:"user-info-head",onClick:w[6]||(w[6]=X=>c())},[m("img",{src:y(r).img,title:"点击上传头像",class:"img-circle img-lg"},null,8,ft),C(P,{title:y(h),modelValue:y(i),"onUpdate:modelValue":w[5]||(w[5]=X=>G(i)?i.value=X:null),width:"800px","append-to-body":"",onOpened:l,onClose:v},{default:x(()=>[C(k,null,{default:x(()=>[C(b,{xs:24,md:12,style:{height:"350px"}},{default:x(()=>[y(o)?(Y(),q(y(ut),{key:0,ref:"cropper",img:y(r).img,info:!0,autoCrop:y(r).autoCrop,autoCropWidth:y(r).autoCropWidth,autoCropHeight:y(r).autoCropHeight,fixedBox:y(r).fixedBox,outputType:y(r).outputType,onRealTime:g},null,8,["img","autoCrop","autoCropWidth","autoCropHeight","fixedBox","outputType"])):I("",!0)]),_:1}),C(b,{xs:24,md:12,style:{height:"350px"}},{default:x(()=>[m("div",gt,[m("img",{src:y(r).previews.url,style:S(y(r).previews.img)},null,12,mt)])]),_:1})]),_:1}),vt,C(k,null,{default:x(()=>[C(b,{lg:2,md:2},{default:x(()=>[C(B,{action:"#","http-request":n,"show-file-list":!1,"before-upload":O},{default:x(()=>[C(W,null,{default:x(()=>[N(" 选择 "),C(z,{class:"el-icon--right"},{default:x(()=>[C(_)]),_:1})]),_:1})]),_:1})]),_:1}),C(b,{lg:{span:1,offset:2},md:2},{default:x(()=>[C(W,{icon:"Plus",onClick:w[0]||(w[0]=X=>u(1))})]),_:1}),C(b,{lg:{span:1,offset:1},md:2},{default:x(()=>[C(W,{icon:"Minus",onClick:w[1]||(w[1]=X=>u(-1))})]),_:1}),C(b,{lg:{span:1,offset:1},md:2},{default:x(()=>[C(W,{icon:"RefreshLeft",onClick:w[2]||(w[2]=X=>a())})]),_:1}),C(b,{lg:{span:1,offset:1},md:2},{default:x(()=>[C(W,{icon:"RefreshRight",onClick:w[3]||(w[3]=X=>p())})]),_:1}),C(b,{lg:{span:2,offset:6},md:2},{default:x(()=>[C(W,{type:"primary",onClick:w[4]||(w[4]=X=>f())},{default:x(()=>[N("提 交")]),_:1})]),_:1})]),_:1})]),_:1},8,["title","modelValue"])])}}},yt=D(wt,[["__scopeId","data-v-494c65e6"]]);export{yt as default};
