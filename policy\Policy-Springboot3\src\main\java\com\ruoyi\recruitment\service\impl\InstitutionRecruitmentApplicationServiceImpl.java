package com.ruoyi.recruitment.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.recruitment.mapper.InstitutionRecruitmentApplicationMapper;
import com.ruoyi.recruitment.mapper.OnlineTrainingInstitutionRecruitmentMapper;
import com.ruoyi.recruitment.domain.InstitutionRecruitmentApplication;
import com.ruoyi.recruitment.service.IInstitutionRecruitmentApplicationService;

/**
 * 机构招募申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class InstitutionRecruitmentApplicationServiceImpl implements IInstitutionRecruitmentApplicationService 
{
    @Autowired
    private InstitutionRecruitmentApplicationMapper institutionRecruitmentApplicationMapper;

    @Autowired
    private OnlineTrainingInstitutionRecruitmentMapper onlineTrainingInstitutionRecruitmentMapper;

    /**
     * 查询机构招募申请
     * 
     * @param applicationId 机构招募申请主键
     * @return 机构招募申请
     */
    @Override
    public InstitutionRecruitmentApplication selectInstitutionRecruitmentApplicationByApplicationId(Long applicationId)
    {
        return institutionRecruitmentApplicationMapper.selectInstitutionRecruitmentApplicationByApplicationId(applicationId);
    }

    /**
     * 查询机构招募申请列表
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 机构招募申请
     */
    @Override
    public List<InstitutionRecruitmentApplication> selectInstitutionRecruitmentApplicationList(InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        return institutionRecruitmentApplicationMapper.selectInstitutionRecruitmentApplicationList(institutionRecruitmentApplication);
    }

    /**
     * 新增机构招募申请
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 结果
     */
    @Override
    public int insertInstitutionRecruitmentApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        institutionRecruitmentApplication.setCreateTime(DateUtils.getNowDate());
        institutionRecruitmentApplication.setCreateId(SecurityUtils.getUserId());
        return institutionRecruitmentApplicationMapper.insertInstitutionRecruitmentApplication(institutionRecruitmentApplication);
    }

    /**
     * 修改机构招募申请
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 结果
     */
    @Override
    public int updateInstitutionRecruitmentApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        institutionRecruitmentApplication.setUpdateTime(DateUtils.getNowDate());
        institutionRecruitmentApplication.setUpdateId(SecurityUtils.getUserId());
        return institutionRecruitmentApplicationMapper.updateInstitutionRecruitmentApplication(institutionRecruitmentApplication);
    }

    /**
     * 批量删除机构招募申请
     * 
     * @param applicationIds 需要删除的机构招募申请主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionRecruitmentApplicationByApplicationIds(Long[] applicationIds)
    {
        return institutionRecruitmentApplicationMapper.deleteInstitutionRecruitmentApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除机构招募申请信息
     * 
     * @param applicationId 机构招募申请主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionRecruitmentApplicationByApplicationId(Long applicationId)
    {
        return institutionRecruitmentApplicationMapper.deleteInstitutionRecruitmentApplicationByApplicationId(applicationId);
    }

    /**
     * 根据招募ID查询申请列表
     * 
     * @param recruitmentId 招募ID
     * @return 申请列表
     */
    @Override
    public List<InstitutionRecruitmentApplication> selectApplicationsByRecruitmentId(Long recruitmentId)
    {
        return institutionRecruitmentApplicationMapper.selectApplicationsByRecruitmentId(recruitmentId);
    }

    /**
     * 根据用户ID查询申请列表
     * 
     * @param userId 用户ID
     * @return 申请列表
     */
    @Override
    public List<InstitutionRecruitmentApplication> selectApplicationsByUserId(Long userId)
    {
        return institutionRecruitmentApplicationMapper.selectApplicationsByUserId(userId);
    }

    /**
     * 提交申请
     * 
     * @param institutionRecruitmentApplication 申请信息
     * @return 结果
     */
    @Override
    @Transactional
    public int submitApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        // 检查是否已经申请过
        InstitutionRecruitmentApplication existingApplication = institutionRecruitmentApplicationMapper.checkExistingApplication(
            institutionRecruitmentApplication.getRecruitmentId(), 
            SecurityUtils.getUserId()
        );
        
        if (existingApplication != null && !"2".equals(existingApplication.getApplicationStatus()) && !"3".equals(existingApplication.getApplicationStatus())) {
            throw new RuntimeException("您已经申请过该招募项目");
        }

        // 设置申请信息
        institutionRecruitmentApplication.setApplicantUserId(SecurityUtils.getUserId());
        institutionRecruitmentApplication.setApplicationStatus("0"); // 待审核
        institutionRecruitmentApplication.setApplicationTime(new Date());
        institutionRecruitmentApplication.setIsSelected("0");
        institutionRecruitmentApplication.setCreateTime(DateUtils.getNowDate());
        institutionRecruitmentApplication.setCreateId(SecurityUtils.getUserId());

        // 插入申请记录
        int result = institutionRecruitmentApplicationMapper.insertInstitutionRecruitmentApplication(institutionRecruitmentApplication);
        
        // 更新招募信息的申请次数
        if (result > 0) {
            onlineTrainingInstitutionRecruitmentMapper.incrementApplicationCount(institutionRecruitmentApplication.getRecruitmentId());
        }
        
        return result;
    }

    /**
     * 审核申请
     * 
     * @param applicationId 申请ID
     * @param status 审核状态
     * @param reviewComment 审核意见
     * @return 结果
     */
    @Override
    public int reviewApplication(Long applicationId, String status, String reviewComment)
    {
        String reviewer = SecurityUtils.getUsername();
        return institutionRecruitmentApplicationMapper.reviewApplication(applicationId, status, reviewer, reviewComment);
    }

    /**
     * 选中机构
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    @Override
    @Transactional
    public int selectInstitution(Long applicationId)
    {
        // 选中机构
        int result = institutionRecruitmentApplicationMapper.selectInstitution(applicationId);
        
        if (result > 0) {
            // 获取申请信息
            InstitutionRecruitmentApplication application = institutionRecruitmentApplicationMapper.selectInstitutionRecruitmentApplicationByApplicationId(applicationId);
            if (application != null) {
                // 更新招募信息的选中机构数量
                InstitutionRecruitmentApplication stats = institutionRecruitmentApplicationMapper.getApplicationStats(application.getRecruitmentId());
                if (stats != null) {
                    onlineTrainingInstitutionRecruitmentMapper.updateSelectedCount(application.getRecruitmentId(), Integer.parseInt(stats.getIsSelected()));
                }
            }
        }
        
        return result;
    }

    /**
     * 取消选中机构
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    @Override
    @Transactional
    public int unselectInstitution(Long applicationId)
    {
        // 取消选中机构
        int result = institutionRecruitmentApplicationMapper.unselectInstitution(applicationId);
        
        if (result > 0) {
            // 获取申请信息
            InstitutionRecruitmentApplication application = institutionRecruitmentApplicationMapper.selectInstitutionRecruitmentApplicationByApplicationId(applicationId);
            if (application != null) {
                // 更新招募信息的选中机构数量
                InstitutionRecruitmentApplication stats = institutionRecruitmentApplicationMapper.getApplicationStats(application.getRecruitmentId());
                if (stats != null) {
                    onlineTrainingInstitutionRecruitmentMapper.updateSelectedCount(application.getRecruitmentId(), Integer.parseInt(stats.getIsSelected()));
                }
            }
        }
        
        return result;
    }

    /**
     * 取消申请
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    @Override
    public int cancelApplication(Long applicationId)
    {
        return institutionRecruitmentApplicationMapper.cancelApplication(applicationId);
    }

    /**
     * 检查是否已申请
     * 
     * @param recruitmentId 招募ID
     * @param userId 用户ID
     * @return 申请记录
     */
    @Override
    public InstitutionRecruitmentApplication checkExistingApplication(Long recruitmentId, Long userId)
    {
        return institutionRecruitmentApplicationMapper.checkExistingApplication(recruitmentId, userId);
    }

    /**
     * 获取申请统计信息
     * 
     * @param recruitmentId 招募ID
     * @return 统计信息
     */
    @Override
    public InstitutionRecruitmentApplication getApplicationStats(Long recruitmentId)
    {
        return institutionRecruitmentApplicationMapper.getApplicationStats(recruitmentId);
    }

    /**
     * 批量审核申请
     * 
     * @param applicationIds 申请ID数组
     * @param status 审核状态
     * @param reviewComment 审核意见
     * @return 结果
     */
    @Override
    public int batchReviewApplications(Long[] applicationIds, String status, String reviewComment)
    {
        String reviewer = SecurityUtils.getUsername();
        return institutionRecruitmentApplicationMapper.batchReviewApplications(applicationIds, status, reviewer, reviewComment);
    }

    /**
     * 更新申请（重新申请）
     * 
     * @param institutionRecruitmentApplication 申请信息
     * @return 结果
     */
    @Override
    public int updateApplicationForReapply(InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        // 重置申请状态为待审核
        institutionRecruitmentApplication.setApplicationStatus("0");
        institutionRecruitmentApplication.setApplicationTime(new Date());
        institutionRecruitmentApplication.setReviewTime(null);
        institutionRecruitmentApplication.setReviewer(null);
        institutionRecruitmentApplication.setReviewComment(null);
        institutionRecruitmentApplication.setIsSelected("0");
        institutionRecruitmentApplication.setUpdateTime(DateUtils.getNowDate());
        institutionRecruitmentApplication.setUpdateId(SecurityUtils.getUserId());
        
        return institutionRecruitmentApplicationMapper.updateInstitutionRecruitmentApplication(institutionRecruitmentApplication);
    }
}
