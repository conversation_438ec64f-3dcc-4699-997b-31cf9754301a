import{Y as y,C as de,d as ge,r as s,D as be,I as me,e as he,R as fe,f as $,K as ye,c as x,o as p,l as h,i as f,j as g,k as n,h as ve,G as _,m as J,p as u,t as c}from"./index-DP10CBaW.js";import{g as je,e as Ce}from"./columnUtils-DYlA-XL_.js";import{T as _e}from"./index-BWWetMd6.js";import Te from"./JobPostingFormDialog-CpRy2JIh.js";function xe(o){return y({url:"/job/posting/list",method:"get",params:o})}function Se(o){return y({url:"/job/posting/"+o,method:"get"})}function ke(o){return y({url:"/job/posting",method:"post",data:o})}function Pe(o){return y({url:"/job/posting",method:"put",data:o})}function Me(o){return y({url:`/job/posting/publish/${o}`,method:"put"})}function De(o){return y({url:`/job/posting/pause/${o}`,method:"put"})}function Ie(o){return y({url:"/job/posting/"+o,method:"delete"})}const Oe=o=>({dialogWidth:"900px",dialogHeight:"70vh",labelWidth:"100px",column:[{label:"职位名称",prop:"jobTitle",search:!0,searchSpan:8,minWidth:200,rules:[{required:!0,message:"职位名称不能为空",trigger:"blur"},{min:2,max:100,message:"职位名称长度必须介于 2 和 100 之间",trigger:"blur"}],span:8},{label:"工作类型",prop:"jobType",search:!0,searchSpan:6,width:100,align:"center",type:"select",dicData:[{label:"全职",value:"全职"},{label:"兼职",value:"兼职"},{label:"临时工",value:"临时工"},{label:"小时工",value:"小时工"},{label:"实习",value:"实习"}],span:8,slot:!0,rules:[{required:!0,message:"工作类型不能为空",trigger:"change"}]},{label:"工作类别",prop:"jobCategory",search:!0,searchSpan:6,width:100,align:"center",type:"select",dicData:[{label:"服务员",value:"服务员"},{label:"保洁",value:"保洁"},{label:"搬运工",value:"搬运工"},{label:"销售",value:"销售"},{label:"客服",value:"客服"},{label:"配送员",value:"配送员"},{label:"厨师",value:"厨师"},{label:"司机",value:"司机"},{label:"保安",value:"保安"},{label:"其他",value:"其他"}],span:8,slot:!0,rules:[{required:!0,message:"工作类别不能为空",trigger:"change"}]},{label:"薪资类型",prop:"salaryType",search:!0,searchSpan:6,width:80,align:"center",type:"select",dicData:[{label:"小时",value:"hourly"},{label:"日薪",value:"daily"},{label:"月薪",value:"monthly"},{label:"计件",value:"piece"}],span:8,rules:[{required:!0,message:"薪资类型不能为空",trigger:"change"}]},{label:"学历要求",prop:"educationRequired",search:!0,searchSpan:6,width:80,align:"center",type:"select",dicData:[{label:"不限",value:"不限"},{label:"初中",value:"初中"},{label:"高中",value:"高中"},{label:"中专",value:"中专"},{label:"大专",value:"大专"},{label:"本科",value:"本科"}],span:8,rules:[{required:!0,message:"学历要求不能为空",trigger:"change"}]},{label:"工作地点",prop:"workLocation",search:!0,searchSpan:8,minWidth:120,span:8,rules:[{required:!0,message:"工作地点不能为空",trigger:"blur"},{max:100,message:"工作地点不能超过100个字符",trigger:"blur"}]},{label:"最低薪资",prop:"salaryMin",search:!1,type:"number",span:8,precision:0,min:0,rules:[{required:!0,message:"最低薪资不能为空",trigger:"blur"},{type:"number",min:0,message:"最低薪资不能小于0",trigger:"blur"}]},{label:"最高薪资",prop:"salaryMax",search:!1,type:"number",span:8,precision:0,min:0,rules:[{type:"number",min:0,message:"最高薪资不能小于0",trigger:"blur"}]},{label:"职位描述",prop:"jobDescription",search:!1,type:"textarea",span:24,minRows:3,maxRows:6,showWordLimit:!0,maxlength:500,rules:[{required:!0,message:"职位描述不能为空",trigger:"blur"}]},{label:"招聘人数",prop:"positionsAvailable",search:!1,width:80,align:"center",type:"number",span:8,min:1,value:1,rules:[{required:!0,message:"招聘人数不能为空",trigger:"blur"},{type:"number",min:1,message:"招聘人数不能小于1",trigger:"blur"}]},{label:"经验要求",prop:"experienceRequired",search:!1,span:8,placeholder:"如：1年以上",rules:[{max:50,message:"经验要求不能超过50个字符",trigger:"blur"}]},{label:"联系人",prop:"contactPerson",search:!1,span:8,rules:[{required:!0,message:"联系人不能为空",trigger:"blur"},{max:20,message:"联系人不能超过20个字符",trigger:"blur"}]},{label:"联系电话",prop:"contactPhone",search:!1,span:8,rules:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},{label:"公司名称",prop:"companyName",search:!0,searchSpan:8,span:8,rules:[{max:100,message:"公司名称不能超过100个字符",trigger:"blur"}]},{label:"状态",prop:"status",search:!0,searchSpan:6,width:80,align:"center",type:"select",dicData:[{label:"草稿",value:"draft"},{label:"已发布",value:"published"},{label:"已暂停",value:"paused"},{label:"已关闭",value:"closed"},{label:"已完成",value:"completed"}],span:8,slot:!0,addDisplay:!1,editDisplay:!1,value:"published"}]}),$e={class:"job-posting-container app-container"},qe={key:0},Ne={key:1},Re={key:2},Le={class:"operation-btns"},ze={key:1,class:"loading-placeholder"},Fe=de({name:"JobPosting"}),Ve=Object.assign(Fe,{setup(o){const{proxy:r}=ge(),S=s([]),P=s(!0),v=s([]),A=s(!0),W=s(!0),q=s(0),T=s(!0),N=s([]),R=s([]),M=s(!1),D=s(!1),I=s({dialogWidth:"800px",dialogHeight:"60vh"}),U=s(null),j=s(null),L=s([]),C=s({}),V=be({queryParams:{pageNum:1,pageSize:10,jobTitle:void 0,jobType:void 0,jobCategory:void 0,workLocation:void 0,status:void 0,companyName:void 0,publisherUserName:void 0,createTime:void 0}}),{queryParams:d}=me(V);he(async()=>{await B(),b()});const B=async()=>{try{const e=Oe(r),a=await je({baseOption:e,proxy:r}),{tableColumns:l,searchColumns:i,formFields:O,formOptions:m}=Ce(a);N.value=l,R.value=i,L.value=O,I.value={...I.value,...m},D.value=!0}catch(e){D.value=!1,console.error("初始化配置失败:",e)}};function b(){M.value=!0,P.value=!0;let e={...d.value};C.value.createTime&&Array.isArray(C.value.createTime)&&C.value.createTime.length===2&&(e=r.addDateRange(e,C.value.createTime)),xe(e).then(a=>{M.value=!1,P.value=!1,S.value=a.rows,q.value=a.total,fe(()=>{T.value=!1})})}const E=e=>{T.value=!0,C.value={...e};const{createTime:a,...l}=e||{};Object.assign(d.value,l),d.value.pageNum=1,b()},w=()=>{T.value=!0,d.value={pageNum:1,pageSize:10,jobTitle:void 0,jobType:void 0,jobCategory:void 0,workLocation:void 0,status:void 0,companyName:void 0,publisherUserName:void 0,createTime:void 0},C.value={},b()},H=e=>{T.value=!0,d.value.pageNum=e,b()},G=e=>{T.value=!0,d.value.pageSize=e,d.value.pageNum=1,b()};function K(e){v.value=e.map(a=>a.jobId),A.value=e.length!=1,W.value=!e.length}const Y=e=>{var a;(a=j.value)==null||a.openDialog("view","查看招聘信息",e)},z=e=>{const a=e.jobId;Se(a).then(l=>{var i;(i=j.value)==null||i.openDialog("edit","编辑招聘信息",l.data)})},Q=()=>{var a;const e={status:"draft"};(a=j.value)==null||a.openDialog("add","新增招聘信息",e)},X=async e=>{var a,l;try{e.type==="add"?(await ke(e.data),r.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await Pe(e.data),r.$modal.msgSuccess("修改成功")),(a=j.value)==null||a.onSubmitSuccess(),b()}catch(i){(l=j.value)==null||l.onSubmitError(),console.error("提交失败:",i)}},Z=()=>{};function ee(){Q()}function ae(e){if(e)z(e);else{const a=v.value[0],l=S.value.find(i=>i.jobId===a);l&&z(l)}}function te(e){const a=e!=null&&e.jobId?[e.jobId]:v.value;r.$modal.confirm("是否确认发布选中的招聘信息？").then(function(){return Me(a[0])}).then(()=>{b(),r.$modal.msgSuccess("发布成功")}).catch(()=>{})}function ne(e){const a=e!=null&&e.jobId?[e.jobId]:v.value;r.$modal.confirm("是否确认暂停选中的招聘信息？").then(function(){return De(a[0])}).then(()=>{b(),r.$modal.msgSuccess("暂停成功")}).catch(()=>{})}function le(e){const a=e!=null&&e.jobId?[e.jobId]:v.value;r.$modal.confirm("是否确认删除选中的招聘信息？").then(function(){return Ie(a)}).then(()=>{b(),r.$modal.msgSuccess("删除成功")}).catch(()=>{})}function se(){r.download("job/posting/export",{...d.value},`job_posting_${new Date().getTime()}.xlsx`)}function oe(e){return{全职:"success",兼职:"info",临时工:"warning",小时工:"danger",实习:""}[e]||""}function re(e){return{服务员:"success",保洁:"info",搬运工:"warning",销售:"danger",客服:"",配送员:"success",厨师:"info",司机:"warning",保安:"danger",其他:""}[e]||""}function ie(e){return{urgent:"danger",high:"warning",normal:"info",low:""}[e]||"info"}function ue(e){return{urgent:"紧急",high:"高",normal:"普通",low:"低"}[e]||"普通"}function ce(e){return{draft:"info",published:"success",paused:"warning",closed:"danger",completed:""}[e]||"info"}function pe(e){return{draft:"草稿",published:"已发布",paused:"已暂停",closed:"已关闭",completed:"已完成"}[e]||"未知"}function F(e){return{hourly:"元/小时",daily:"元/天",monthly:"元/月",piece:"元/件"}[e]||"元"}return(e,a)=>{const l=$("el-button"),i=$("el-tag"),O=$("el-empty"),m=ye("hasPermi");return p(),x("div",$e,[g(D)?(p(),h(_e,{key:0,columns:g(N),data:g(S),loading:g(M),showIndex:!0,searchColumns:g(R),showOperation:!0,operationLabel:"操作",operationWidth:"200",fixedOperation:!0,ref_key:"tableListRef",ref:U,onSearch:E,onReset:w,defaultPage:{pageSize:g(d).pageSize,currentPage:g(d).pageNum,total:g(q)},onCurrentChange:H,onSizeChange:G,onSelectionChange:K},{"menu-left":n(()=>[_((p(),h(l,{type:"primary",class:"custom-btn",onClick:ee},{default:n(()=>[u("新 增")]),_:1})),[[m,["job:posting:add"]]]),_((p(),h(l,{type:"warning",plain:"",class:"custom-btn",onClick:se},{default:n(()=>[u("导 出")]),_:1})),[[m,["job:posting:export"]]])]),status:n(({row:t})=>[f(i,{type:ce(t.status),size:"small"},{default:n(()=>[u(c(pe(t.status)),1)]),_:2},1032,["type"])]),jobType:n(({row:t})=>[f(i,{type:oe(t.jobType),size:"small"},{default:n(()=>[u(c(t.jobType),1)]),_:2},1032,["type"])]),jobCategory:n(({row:t})=>[f(i,{type:re(t.jobCategory),size:"small"},{default:n(()=>[u(c(t.jobCategory),1)]),_:2},1032,["type"])]),salaryRange:n(({row:t})=>[t.salaryMin&&t.salaryMax?(p(),x("span",qe,c(t.salaryMin)+"-"+c(t.salaryMax)+" "+c(F(t.salaryType)),1)):t.salaryMin?(p(),x("span",Ne,c(t.salaryMin)+"+ "+c(F(t.salaryType)),1)):(p(),x("span",Re,"面议"))]),positionsCount:n(({row:t})=>[u(c(t.positionsFilled||0)+"/"+c(t.positionsAvailable||0),1)]),urgencyLevel:n(({row:t})=>[f(i,{type:ie(t.urgencyLevel),size:"small"},{default:n(()=>[u(c(ue(t.urgencyLevel)),1)]),_:2},1032,["type"])]),viewApplication:n(({row:t})=>[u(c(t.viewCount||0)+"/"+c(t.applicationCount||0),1)]),menu:n(({row:t})=>[ve("div",Le,[f(l,{type:"primary",link:"",onClick:k=>Y(t)},{default:n(()=>[u("查看")]),_:2},1032,["onClick"]),_((p(),h(l,{type:"primary",link:"",onClick:k=>ae(t)},{default:n(()=>[u("编辑")]),_:2},1032,["onClick"])),[[m,["job:posting:edit"]]]),t.status==="draft"?_((p(),h(l,{key:0,type:"success",link:"",onClick:k=>te(t)},{default:n(()=>[u("发布")]),_:2},1032,["onClick"])),[[m,["job:posting:publish"]]]):J("",!0),t.status==="published"?_((p(),h(l,{key:1,type:"warning",link:"",onClick:k=>ne(t)},{default:n(()=>[u("暂停")]),_:2},1032,["onClick"])),[[m,["job:posting:pause"]]]):J("",!0),_((p(),h(l,{type:"danger",link:"",onClick:k=>le(t)},{default:n(()=>[u("删除")]),_:2},1032,["onClick"])),[[m,["job:posting:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(p(),x("div",ze,[f(O,{description:"正在加载表格配置..."})])),f(Te,{ref_key:"jobPostingFormDialogRef",ref:j,formFields:g(L),formOption:g(I),onSubmit:X,onCancel:Z},null,8,["formFields","formOption"])])}}});export{Ve as default};
