import{_ as R,a as q,r as g,e as F,J as T,f as p,K as J,c as u,G as K,i as t,k as a,p as n,L as O,M as Q,m as v,o as d,t as s,l as x,h as e,x as U,y as H,N as W,j as D,O as X,P as Y,Q as Z,E as ee}from"./index-DP10CBaW.js";import{g as te,c as ae}from"./application-CqX6Rd9f.js";const _=f=>(U("data-v-65039200"),f=f(),H(),f),se={class:"my-applications-container"},le=_(()=>e("div",{class:"page-header"},[e("h1",null,"我的培训申请"),e("p",null,"查看您的所有培训申请记录和状态")],-1)),oe={class:"applications-list"},ne={key:0,class:"empty-state"},ie={key:1,class:"application-items"},ce={class:"application-content"},de={class:"application-header"},ue={class:"training-title"},_e={class:"application-info"},re={class:"info-row"},pe={class:"info-item"},ve=_(()=>e("span",{class:"label"},"申请人：",-1)),fe={class:"value"},me={class:"info-item"},he=_(()=>e("span",{class:"label"},"联系电话：",-1)),ye={class:"value"},ge={class:"info-item"},be=_(()=>e("span",{class:"label"},"申请时间：",-1)),we={class:"value"},ke={class:"info-row"},Ce={class:"info-item"},Se=_(()=>e("span",{class:"label"},"培训类型：",-1)),Te={class:"value"},xe={class:"info-item"},De=_(()=>e("span",{class:"label"},"培训时间：",-1)),Me={class:"value"},Ne={key:0,class:"info-row"},ze={class:"info-item"},Ie=_(()=>e("span",{class:"label"},"审核时间：",-1)),Ve={class:"value"},Ae={class:"info-item"},Be=_(()=>e("span",{class:"label"},"审核人：",-1)),Ee={class:"value"},Le={key:1,class:"info-row"},Pe={class:"info-item full-width"},$e=_(()=>e("span",{class:"label"},"审核意见：",-1)),je={class:"value"},Ge={class:"application-actions"},Re={key:0,class:"application-detail"},qe={__name:"my-applications",setup(f){const M=q(),b=g(!1),w=g([]),k=g(!1),o=g(null);F(()=>{N()});const N=async()=>{b.value=!0;try{const i=await te();w.value=i.data||[]}catch(i){T.error("获取申请记录失败"),console.error(i)}finally{b.value=!1}},B=i=>{o.value=i,k.value=!0},E=async i=>{try{await ee.confirm("确认要取消申请吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ae(i.applicationId),T.success("取消申请成功"),await N()}catch(r){r!=="cancel"&&T.error(r.msg||"取消申请失败")}},L=i=>{M.push({path:"/zhaop/application/signup",query:{orderId:i.orderId}})},P=()=>{M.push("/zhaop/application/signup")},z=i=>({0:"warning",1:"success",2:"danger",3:"info"})[i]||"info",I=i=>({0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"})[i]||"未知",m=i=>i?new Date(i).toLocaleString("zh-CN"):"--",h=i=>i?new Date(i).toLocaleDateString("zh-CN"):"--";return(i,r)=>{const y=p("el-button"),$=p("el-empty"),V=p("el-tag"),C=p("el-icon"),c=p("el-descriptions-item"),S=p("el-descriptions"),j=p("el-dialog"),G=J("loading");return d(),u("div",se,[le,K((d(),u("div",oe,[w.value.length===0?(d(),u("div",ne,[t($,{description:"暂无申请记录"},{default:a(()=>[t(y,{type:"primary",onClick:P},{default:a(()=>[n("去申请培训")]),_:1})]),_:1})])):(d(),u("div",ie,[(d(!0),u(O,null,Q(w.value,l=>(d(),u("div",{key:l.applicationId,class:W(["application-item",{"status-pending":l.applicationStatus==="0","status-approved":l.applicationStatus==="1","status-rejected":l.applicationStatus==="2","status-cancelled":l.applicationStatus==="3"}])},[e("div",ce,[e("div",de,[e("h3",ue,s(l.orderTitle),1),t(V,{type:z(l.applicationStatus),size:"default"},{default:a(()=>[n(s(I(l.applicationStatus)),1)]),_:2},1032,["type"])]),e("div",_e,[e("div",re,[e("div",pe,[ve,e("span",fe,s(l.applicantName),1)]),e("div",me,[he,e("span",ye,s(l.applicantPhone),1)]),e("div",ge,[be,e("span",we,s(m(l.applicationTime)),1)])]),e("div",ke,[e("div",Ce,[Se,e("span",Te,s(l.trainingType),1)]),e("div",xe,[De,e("span",Me,s(h(l.startDate))+" 至 "+s(h(l.endDate)),1)])]),l.reviewTime?(d(),u("div",Ne,[e("div",ze,[Ie,e("span",Ve,s(m(l.reviewTime)),1)]),e("div",Ae,[Be,e("span",Ee,s(l.reviewer||"--"),1)])])):v("",!0),l.reviewComment?(d(),u("div",Le,[e("div",Pe,[$e,e("span",je,s(l.reviewComment),1)])])):v("",!0)])]),e("div",Ge,[t(y,{type:"primary",size:"default",onClick:A=>B(l)},{default:a(()=>[t(C,null,{default:a(()=>[t(D(X))]),_:1}),n(" 查看详情 ")]),_:2},1032,["onClick"]),l.applicationStatus==="0"?(d(),x(y,{key:0,type:"warning",size:"default",onClick:A=>E(l)},{default:a(()=>[t(C,null,{default:a(()=>[t(D(Y))]),_:1}),n(" 取消申请 ")]),_:2},1032,["onClick"])):v("",!0),l.applicationStatus==="2"?(d(),x(y,{key:1,type:"success",size:"default",onClick:A=>L(l)},{default:a(()=>[t(C,null,{default:a(()=>[t(D(Z))]),_:1}),n(" 重新申请 ")]),_:2},1032,["onClick"])):v("",!0)])],2))),128))]))])),[[G,b.value]]),t(j,{modelValue:k.value,"onUpdate:modelValue":r[0]||(r[0]=l=>k.value=l),title:"申请详情",width:"700px","append-to-body":""},{default:a(()=>[o.value?(d(),u("div",Re,[t(S,{title:"培训信息",column:2,border:""},{default:a(()=>[t(c,{label:"培训标题"},{default:a(()=>[n(s(o.value.orderTitle),1)]),_:1}),t(c,{label:"培训类型"},{default:a(()=>[n(s(o.value.trainingType),1)]),_:1}),t(c,{label:"培训时间"},{default:a(()=>[n(s(h(o.value.startDate))+" 至 "+s(h(o.value.endDate)),1)]),_:1})]),_:1}),t(S,{title:"申请信息",column:2,border:"",style:{"margin-top":"20px"}},{default:a(()=>[t(c,{label:"申请人姓名"},{default:a(()=>[n(s(o.value.applicantName),1)]),_:1}),t(c,{label:"联系电话"},{default:a(()=>[n(s(o.value.applicantPhone),1)]),_:1}),t(c,{label:"邮箱地址"},{default:a(()=>[n(s(o.value.applicantEmail),1)]),_:1}),t(c,{label:"性别"},{default:a(()=>[n(s(o.value.applicantGender),1)]),_:1}),t(c,{label:"年龄"},{default:a(()=>[n(s(o.value.applicantAge),1)]),_:1}),t(c,{label:"学历"},{default:a(()=>[n(s(o.value.applicantEducation),1)]),_:1}),t(c,{label:"身份证号"},{default:a(()=>[n(s(o.value.applicantIdCard),1)]),_:1}),t(c,{label:"联系地址"},{default:a(()=>[n(s(o.value.applicantAddress),1)]),_:1}),t(c,{label:"工作经验",span:2},{default:a(()=>[n(s(o.value.applicantExperience||"--"),1)]),_:1}),t(c,{label:"申请备注",span:2},{default:a(()=>[n(s(o.value.applicationNote||"--"),1)]),_:1})]),_:1}),t(S,{title:"审核信息",column:2,border:"",style:{"margin-top":"20px"}},{default:a(()=>[t(c,{label:"申请状态"},{default:a(()=>[t(V,{type:z(o.value.applicationStatus)},{default:a(()=>[n(s(I(o.value.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),t(c,{label:"申请时间"},{default:a(()=>[n(s(m(o.value.applicationTime)),1)]),_:1}),t(c,{label:"审核时间"},{default:a(()=>[n(s(m(o.value.reviewTime)),1)]),_:1}),t(c,{label:"审核人"},{default:a(()=>[n(s(o.value.reviewer||"--"),1)]),_:1}),o.value.reviewComment?(d(),x(c,{key:0,label:"审核意见",span:2},{default:a(()=>[n(s(o.value.reviewComment),1)]),_:1})):v("",!0)]),_:1})])):v("",!0)]),_:1},8,["modelValue"])])}}},Ke=R(qe,[["__scopeId","data-v-65039200"]]);export{Ke as default};
