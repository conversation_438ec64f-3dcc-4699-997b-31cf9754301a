import{_ as C,r as N,w as $,e as A,A as f,f as b,c as n,o as r,i as B,a4 as y,k as h,L as u,M as E,l as k,m as F,h as p,t as c,N as I,Z as L,p as v,j as x,a7 as S,x as j,y as z}from"./index-DP10CBaW.js";const M=t=>(j("data-v-18c5825e"),t=t(),z(),t),O={class:"view-content"},W={class:"divider-wrapper"},R={class:"divider-title"},Z=M(()=>p("div",{class:"divider-line"},null,-1)),_={class:"view-item-content"},q={class:"view-value"},G={key:3,class:"multiline-text"},H={__name:"index",props:{modelValue:{type:Object,default:()=>({})},fields:{type:Array,default:()=>[]},labelWidth:{type:String,default:"120px"},emptyText:{type:String,default:"--"}},setup(t){const i=t,d=N({});$(()=>i.modelValue,s=>{w()},{deep:!0}),A(()=>{w()});const T=f(()=>i.fields.filter(s=>s.viewDisplay!==!1)),w=()=>{const s={};i.fields.forEach(a=>{s[a.prop]={viewDisplay:!0}}),d.value={...s},i.fields.forEach(a=>{if(a.control&&typeof a.control=="function"){const o=i.modelValue[a.prop],l=a.control(o,i.modelValue);l&&Object.keys(l).forEach(e=>{d.value[e]&&(d.value[e]={...d.value[e],...l[e]})})}})},V=f(()=>T.value.filter(s=>{const a=d.value[s.prop];return!a||a.viewDisplay!==!1})),g=(s,a)=>{if(!s||a===void 0||a===null||a instanceof Array&&a.length<1)return i.emptyText;const o=s.find(l=>l.value==a);return o?o.label:a},D=s=>{let a=!1;const o=V.value;for(let l=s+1;l<o.length;l++){const e=o[l];if(e.divider)break;if(!e.divider){const m=d.value[e.prop];if(!m||m.viewDisplay!==!1){a=!0;break}}}return a};return(s,a)=>{const o=b("el-col"),l=b("el-row");return r(),n("div",O,[B(l,{gutter:24,class:"view-row"},{default:h(()=>[(r(!0),n(u,null,E(V.value,(e,m)=>(r(),n(u,{key:m},[e.divider?(r(),k(o,{key:0,span:24,class:"view-divider"},{default:h(()=>[D(m)?y(s.$slots,e.prop,{key:0,field:e},()=>[p("div",W,[p("div",R,c(e.label),1),Z])],!0):F("",!0)]),_:2},1024)):(r(),k(o,{key:1,span:e.span||12,class:I(["view-item",{"full-width-item":e.span===24}])},{default:h(()=>[p("div",_,[p("div",{class:"view-label",style:L({width:i.labelWidth})},c(e.label)+"：",5),p("div",q,[y(s.$slots,e.prop,{value:t.modelValue[e.prop],row:t.modelValue,field:e},()=>[e.dicData?(r(),n(u,{key:0},[v(c(g(e.dicData,t.modelValue[e.prop])),1)],64)):e.type==="date"?(r(),n(u,{key:1},[v(c(x(S)(t.modelValue[e.prop],"{y}-{m}-{d}")||t.emptyText),1)],64)):e.type==="datetime"?(r(),n(u,{key:2},[v(c(x(S)(t.modelValue[e.prop],"{y}-{m}-{d} {h}:{i}:{s}")||t.emptyText),1)],64)):e.type==="textarea"&&t.modelValue[e.prop]?(r(),n("div",G,c(t.modelValue[e.prop]),1)):e.viewSlot?y(s.$slots,`${e.prop}-default`,{key:4,row:t.modelValue,value:t.modelValue[e.prop]},()=>[v(c(t.modelValue[e.prop]||t.emptyText),1)],!0):(r(),n(u,{key:5},[v(c(t.modelValue[e.prop]||t.emptyText),1)],64))],!0)])])]),_:2},1032,["span","class"]))],64))),128))]),_:3}),y(s.$slots,"append",{},void 0,!0)])}}},P=C(H,[["__scopeId","data-v-18c5825e"]]);export{P as V};
