import{_ as Se,d as Ce,D as Pe,I as ke,r as G,w as V,e as _e,f as p,K as Ve,c as h,m as w,h as F,G as Fe,i as b,j as r,k as c,o as s,L as S,M as N,a4 as D,l as u,Z as W,p as m,n as De,t as x,a2 as Ae,a5 as ve,a6 as ze,F as Oe}from"./index-DP10CBaW.js";const xe={class:"table-container"},Be={key:0,class:"search-container"},Te={class:"search-form-wrapper"},Le={class:"search-button-wrapper"},$e={class:"table-header"},je={class:"left-buttons"},Ue={class:"right-buttons"},Ne={key:1,class:"pagination-container"},We={class:"dialog-footer"},Me={__name:"index",props:{columns:{type:Array,default:()=>[]},isShowSearch:{type:Boolean,default:!0},fetchColumns:{type:Boolean,default:!1},menuCode:{type:String,default:""},searchColumns:{type:Array,default:null},data:{type:Array,default:()=>[]},tableHeight:{type:String},loading:{type:Boolean,default:!1},showIndex:{type:Boolean,default:!0},showSelection:{type:Boolean,default:!1},showOperation:{type:Boolean,default:!0},operationLabel:{type:String,default:"操作"},operationWidth:{type:String,default:"220"},showPagination:{type:Boolean,default:!0},paginationLayout:{type:String,default:"total, sizes, prev, pager, next, jumper"},formComponent:{type:[Object,Function],default:null},formProps:{type:Object,default:()=>({})},dynamicForm:{type:Boolean,default:!1},dialogWidth:{type:String,default:"50%"},hideDialogFooter:{type:Boolean,default:!1},loadDataApi:{type:Function},addApi:{type:Function},updateApi:{type:Function},deleteApi:{type:Function},defaultPage:{type:Object,default:()=>({pageSize:10,currentPage:1,total:0})},defaultSortField:{type:String,default:"create_time"},defaultSortOrder:{type:String,default:"desc"},showToggleSearch:{type:Boolean,default:!1},fixedOperation:{type:Boolean,default:!1},spanMethod:{type:[Function,Object]},searchParams:{type:Object,default:()=>({})}},emits:["update:loading","selection-change","row-save","row-update","row-delete","search","reset","load","refresh","current-change","size-change","update:searchParams"],setup(n,{expose:Z,emit:Q}){const l=n,i=Q,{proxy:A}=Ce(),a=Pe({tableData:l.data||[],tableColumns:l.columns||[],searchFields:l.searchColumns||[],tableLoading:l.loading,searchParams:l.searchParams||{},page:{...l.defaultPage},dialogVisible:!1,dialogType:"add",form:{},selectedRows:[],searchCollapse:!1,spanMethod:l.spanMethod}),{tableData:M,tableColumns:X,searchFields:R,tableLoading:Y,searchParams:y,page:C,dialogVisible:v,dialogType:z,form:ee,selectedRows:ae,searchCollapse:I,spanMethod:te}=ke(a),B=G(null),T=G(null);V(()=>l.data,e=>{e&&(a.tableData=e)},{deep:!0,immediate:!0}),V(()=>l.loading,e=>{a.tableLoading=e},{immediate:!0}),V(()=>l.searchColumns,e=>{e&&e.length>0&&(a.searchFields=e)},{deep:!0,immediate:!0}),V(()=>l.searchParams,e=>{e&&Object.keys(e).length>0&&JSON.stringify(e)!==JSON.stringify(a.searchParams)&&(a.searchParams={...e})},{deep:!0,immediate:!0}),V(()=>l.defaultPage,e=>{e&&(e.total!==a.page.total||e.currentPage!==a.page.currentPage||e.pageSize!==a.page.pageSize)&&(a.page={...e})},{deep:!0,immediate:!0}),V(()=>a.searchParams,e=>{JSON.stringify(e)!==JSON.stringify(l.searchParams)&&i("update:searchParams",{...e})},{deep:!0}),_e(()=>{l.searchColumns&&l.searchColumns.length>0&&(a.searchFields=l.searchColumns),l.data&&l.data.length>0?a.tableData=l.data:l.fetchColumns&&l.menuCode?le():J(),l.loadDataApi&&a.tableData.length===0&&!l.data&&f()});const J=()=>{l.searchColumns||(a.searchFields=a.tableColumns.filter(e=>e.search===!0))},le=async()=>{try{a.tableColumns=[],J()}catch{}},f=(e={})=>{if(!l.loadDataApi)return;const o={...e,pageNum:a.page.currentPage,pageSize:a.page.pageSize,orderByColumn:l.defaultSortField,isAsc:l.defaultSortOrder};a.tableLoading=!0,i("update:loading",!0),l.loadDataApi(o).then(g=>{a.tableData=g.rows||g.data||[],a.page.total=g.total||0,a.tableLoading=!1,i("update:loading",!1),i("load",{data:a.tableData,total:a.page.total})}).catch(()=>{a.tableLoading=!1,i("update:loading",!1)})},L=()=>{a.page.currentPage=1;const e={...a.searchParams};f(e),i("search",e)},H=()=>{var e;(e=B.value)==null||e.resetFields(),a.searchParams={},a.page.currentPage=1,f(),i("update:searchParams",{}),i("reset")},oe=e=>{a.selectedRows=e,i("selection-change",e)},re=e=>{a.page.currentPage=e,f(a.searchParams),i("current-change",e)},se=e=>{a.page.pageSize=e,a.page.currentPage=1,f(a.searchParams),i("size-change",e)},ne=(e,o)=>{if(!e||o===void 0||o===null||o==="")return"";if(Array.isArray(o))return o.map(k=>{const O=e.find($=>$.value==k);return O?O.label:k}).join(", ");const g=e.find(k=>k.value==o);return g?g.label:o},K=()=>{a.searchCollapse=!a.searchCollapse},de=()=>{a.dialogType="add",a.form={},a.dialogVisible=!0},ie=e=>{a.dialogType="edit",a.form={...e},a.dialogVisible=!0},ce=e=>{a.dialogType="view",a.form={...e},a.dialogVisible=!0},ue=e=>{if(!l.deleteApi)return;const o=e.id||e[Object.keys(e).find(g=>g.toLowerCase().includes("id"))];A.$modal.confirm("是否确认删除该数据项？").then(function(){return l.deleteApi(o)}).then(()=>{f(a.searchParams),A.$modal.msgSuccess("删除成功"),i("row-delete",e)}).catch(()=>{})},pe=()=>{var e,o;(o=(e=T.value)==null?void 0:e.submit)!=null&&o.call(e)||E()},E=e=>{const o=e||a.form;a.dialogType==="add"?he(o):a.dialogType==="edit"&&fe(o)},he=e=>{if(!l.addApi){P();return}l.addApi(e).then(o=>{A.$modal.msgSuccess("添加成功"),P(),f(a.searchParams),i("row-save",o)}).catch(()=>{})},fe=e=>{if(!l.updateApi){P();return}l.updateApi(e).then(o=>{A.$modal.msgSuccess("修改成功"),P(),f(a.searchParams),i("row-update",o)}).catch(()=>{})},P=()=>{a.dialogVisible=!1,a.form={}};return Z({search:e=>{e&&Object.assign(a.searchParams,e),L()},reset:()=>{H()},getSearchParams:()=>({...a.searchParams}),setSearchParams:e=>{a.searchParams={...e}},refresh:()=>{f(a.searchParams),i("refresh")},tableData:M,selectedRows:ae,add:de,edit:ie,view:ce,delete:ue,loadData:f,searchForm:B,formRef:T,page:C}),(e,o)=>{const g=p("el-option"),k=p("el-select"),O=p("el-date-picker"),$=p("el-input"),q=p("el-form-item"),_=p("el-button"),ge=p("el-form"),j=p("el-table-column"),me=p("el-table"),ye=p("el-pagination"),be=p("el-dialog"),we=Ve("loading");return s(),h("div",xe,[r(R).length>0&&n.isShowSearch?(s(),h("div",Be,[b(ge,{model:r(y),ref_key:"searchForm",ref:B,onKeyup:De(L,["enter"])},{default:c(()=>[F("div",Te,[(s(!0),h(S,null,N(r(R),(t,U)=>(s(),h("div",{key:U,class:"search-field-item"},[b(q,{prop:t.prop,label:t.label},{default:c(()=>[t.searchSlot?D(e.$slots,"search-"+t.prop,{key:0,row:r(y)},void 0,!0):t.type==="select"?(s(),u(k,{key:1,modelValue:r(y)[t.prop],"onUpdate:modelValue":d=>r(y)[t.prop]=d,placeholder:"请选择"+t.label,clearable:"",class:"search-field-width",multiple:t.searchMultiple,style:W({width:t.searchWidth||"150px"})},{default:c(()=>[(s(!0),h(S,null,N(t.dicData,d=>(s(),u(g,{key:d.value,label:d.label,value:d.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","multiple","style"])):t.type==="daterange"||t.type==="date"||t.type==="datetime"?(s(),u(O,{key:2,modelValue:r(y)[t.prop],"onUpdate:modelValue":d=>r(y)[t.prop]=d,type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",clearable:"",class:"search-field-width",style:W({width:t.searchWidth||"260px"})},null,8,["modelValue","onUpdate:modelValue","style"])):(s(),u($,{key:3,modelValue:r(y)[t.prop],"onUpdate:modelValue":d=>r(y)[t.prop]=d,placeholder:"请输入"+t.label,clearable:"",class:"search-field-width",style:W({width:t.searchWidth||"150px"})},null,8,["modelValue","onUpdate:modelValue","placeholder","style"]))]),_:2},1032,["prop","label"])]))),128)),F("div",Le,[b(q,null,{default:c(()=>[b(_,{type:"primary",class:"custom-btn",onClick:L},{default:c(()=>[m("搜 索")]),_:1}),b(_,{class:"custom-btn",onClick:H},{default:c(()=>[m("重 置")]),_:1}),n.showToggleSearch&&!r(I)?(s(),u(_,{key:0,type:"text",size:"small",onClick:K},{default:c(()=>[m(" 收 起 ")]),_:1})):w("",!0),n.showToggleSearch&&r(I)?(s(),u(_,{key:1,type:"text",size:"small",onClick:K},{default:c(()=>[m(" 展 开 ")]),_:1})):w("",!0)]),_:1})])])]),_:3},8,["model"])])):w("",!0),F("div",$e,[F("div",je,[D(e.$slots,"menu-left",{},void 0,!0)]),F("div",Ue,[D(e.$slots,"menu-right",{},void 0,!0)])]),Fe((s(),u(me,{data:r(M),style:{width:"100%"},"header-cell-style":{background:"#f8f8f9",color:"#515a6e"},onSelectionChange:oe,"max-height":n.tableHeight,"min-height":n.tableHeight?void 0:"auto","span-method":r(te),"scrollbar-always-on":!0,flexible:!0,"empty-text":"暂无数据"},{default:c(()=>[n.showSelection?(s(),u(j,{key:0,type:"selection",width:"55",align:"center"})):w("",!0),(s(!0),h(S,null,N(r(X),(t,U)=>(s(),u(j,{key:U,prop:t.prop,label:t.label,width:t.width,"min-width":t.minWidth||"100",align:t.align||"center",sortable:t.sortable,"show-overflow-tooltip":t.showOverflowTooltip!==!1},{default:c(d=>[t.tableSlot?D(e.$slots,t.prop,{key:0,row:d.row,index:d.$index},void 0,!0):t.type==="date"?(s(),h(S,{key:1},[m(x(e.parseTime(d.row[t.prop],"{y}-{m}-{d}")),1)],64)):t.type==="datetime"?(s(),h(S,{key:2},[m(x(e.parseTime(d.row[t.prop],"{y}-{m}-{d} {h}:{i}:{s}")),1)],64)):t.dicData?(s(),h(S,{key:3},[m(x(ne(t.dicData,d.row[t.prop])),1)],64)):(s(),h(S,{key:4},[m(x(d.row[t.prop]),1)],64))]),_:2},1032,["prop","label","width","min-width","align","sortable","show-overflow-tooltip"]))),128)),n.showOperation?(s(),u(j,{key:1,label:n.operationLabel,width:n.operationWidth,align:"center",fixed:n.fixedOperation?"right":!1},{default:c(t=>[D(e.$slots,"menu",{row:t.row,index:t.$index},void 0,!0)]),_:3},8,["label","width","fixed"])):w("",!0)]),_:3},8,["data","max-height","min-height","span-method"])),[[we,r(Y)]]),n.showPagination?(s(),h("div",Ne,[b(ye,{"current-page":r(C).currentPage,"onUpdate:currentPage":o[0]||(o[0]=t=>r(C).currentPage=t),"page-size":r(C).pageSize,"onUpdate:pageSize":o[1]||(o[1]=t=>r(C).pageSize=t),"page-sizes":[10,20,50,100],layout:n.paginationLayout,total:r(C).total,onSizeChange:se,onCurrentChange:re,background:""},null,8,["current-page","page-size","layout","total"])])):w("",!0),b(be,{modelValue:r(v),"onUpdate:modelValue":o[2]||(o[2]=t=>Oe(v)?v.value=t:null),title:r(z)==="add"?"新增":r(z)==="edit"?"编辑":"查看",width:n.dialogWidth,"destroy-on-close":"","append-to-body":""},Ae({default:c(()=>[n.dynamicForm&&r(v)?(s(),u(ve(n.formComponent),ze({key:0,ref_key:"formRef",ref:T},n.formProps,{onSubmit:E,onCancel:P}),null,16)):D(e.$slots,"form",{key:1,form:r(ee),type:r(z)},void 0,!0)]),_:2},[n.hideDialogFooter?void 0:{name:"footer",fn:c(()=>[F("div",We,[b(_,{onClick:P},{default:c(()=>[m("取消")]),_:1}),r(z)!=="view"?(s(),u(_,{key:0,type:"primary",onClick:pe},{default:c(()=>[m("确认")]),_:1})):w("",!0)])]),key:"0"}]),1032,["modelValue","title","width"])])}}},Je=Se(Me,[["__scopeId","data-v-bc0165bf"]]);export{Je as T};
