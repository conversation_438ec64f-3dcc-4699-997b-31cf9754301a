import{_ as X,C as ee,d as ae,r as m,D as te,A as le,w as oe,f as o,l as r,o as l,k as s,h as E,Z as ne,i as b,c as V,L as g,M as S,m as f,p as D,t as re,R as H}from"./index-DP10CBaW.js";const se={class:"dialog-footer"},ue=ee({name:"TrainingOrderFormDialog"}),ie=Object.assign(ue,{props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(v,{expose:M,emit:R}){const{proxy:w}=ae(),T=v,k=R,_=m(!1),x=m(""),p=m(""),a=te({}),U=m({}),h=m(!1),i=m(null),O=m({}),c=le(()=>p.value==="view");oe(()=>T.formFields,n=>{const d={};n.forEach(u=>{u.rules&&u.prop&&(d[u.prop]=u.rules)}),U.value=d},{immediate:!0,deep:!0});const j=n=>p.value==="add"?n.addDisplay!==!1:p.value==="edit"?n.editDisplay!==!1:p.value==="view"?n.viewDisplay!==!1:!0,B=(n,d,u={})=>{p.value=n,x.value=d,Object.keys(a).forEach(y=>{delete a[y]}),Object.assign(a,u),O.value={...u},_.value=!0,H(()=>{i.value&&i.value.clearValidate()})},L=()=>{i.value&&i.value.validate(n=>{if(n){if(!N())return;h.value=!0,k("submit",{type:p.value,data:{...a}})}})},N=()=>a.startDate&&a.endDate&&new Date(a.startDate)>=new Date(a.endDate)?(w.$modal.msgError("结束时间必须晚于开始时间"),!1):a.registrationDeadline&&a.startDate&&new Date(a.registrationDeadline)>=new Date(a.startDate)?(w.$modal.msgError("报名截止时间必须早于培训开始时间"),!1):a.currentParticipants&&a.maxParticipants&&a.currentParticipants>a.maxParticipants?(w.$modal.msgError("当前报名人数不能超过最大参与人数"),!1):!0,P=()=>{_.value=!1,k("cancel")},W=()=>{h.value=!1,_.value=!1,F()},$=()=>{h.value=!1},F=()=>{Object.keys(a).forEach(n=>{delete a[n]}),O.value={},H(()=>{i.value&&(i.value.resetFields(),i.value.clearValidate())})};return M({openDialog:B,onSubmitSuccess:W,onSubmitError:$,resetForm:F}),(n,d)=>{const u=o("el-divider"),y=o("el-col"),C=o("el-input"),A=o("el-input-number"),I=o("el-option"),z=o("el-select"),Z=o("el-date-picker"),q=o("el-switch"),G=o("el-form-item"),J=o("el-row"),K=o("el-form"),Y=o("el-button"),Q=o("el-dialog");return l(),r(Q,{modelValue:_.value,"onUpdate:modelValue":d[0]||(d[0]=e=>_.value=e),title:x.value,width:v.formOption.dialogWidth||"1000px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:s(()=>[E("div",se,[b(Y,{onClick:P},{default:s(()=>[D("取 消")]),_:1}),c.value?f("",!0):(l(),r(Y,{key:0,type:"primary",loading:h.value,onClick:L},{default:s(()=>[D(" 确 定 ")]),_:1},8,["loading"]))])]),default:s(()=>[E("div",{class:"form-container",style:ne({maxHeight:v.formOption.dialogHeight||"70vh",overflowY:"auto"})},[b(K,{ref_key:"formRef",ref:i,model:a,rules:U.value,"label-width":v.formOption.labelWidth||"120px"},{default:s(()=>[b(J,{gutter:20},{default:s(()=>[(l(!0),V(g,null,S(v.formFields,e=>(l(),V(g,{key:e.prop},[e.divider?(l(),r(y,{key:0,span:24,class:"divider-col"},{default:s(()=>[b(u,{"content-position":"left"},{default:s(()=>[D(re(e.label),1)]),_:2},1024)]),_:2},1024)):j(e)?(l(),r(y,{key:1,span:e.span||24},{default:s(()=>[b(G,{label:e.label,prop:e.prop},{default:s(()=>[!e.type||e.type==="input"?(l(),r(C,{key:0,modelValue:a[e.prop],"onUpdate:modelValue":t=>a[e.prop]=t,placeholder:"请输入"+e.label,disabled:c.value||e.disabled,maxlength:e.maxlength,"show-word-limit":e.showWordLimit},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","maxlength","show-word-limit"])):e.type==="textarea"?(l(),r(C,{key:1,modelValue:a[e.prop],"onUpdate:modelValue":t=>a[e.prop]=t,type:"textarea",placeholder:"请输入"+e.label,disabled:c.value||e.disabled,rows:e.minRows||3,maxlength:e.maxlength,"show-word-limit":e.showWordLimit},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","rows","maxlength","show-word-limit"])):e.type==="number"?(l(),r(A,{key:2,modelValue:a[e.prop],"onUpdate:modelValue":t=>a[e.prop]=t,placeholder:"请输入"+e.label,disabled:c.value||e.disabled,min:e.min,max:e.max,precision:e.precision,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","min","max","precision"])):e.type==="select"?(l(),r(z,{key:3,modelValue:a[e.prop],"onUpdate:modelValue":t=>a[e.prop]=t,placeholder:"请选择"+e.label,disabled:c.value||e.disabled,style:{width:"100%"},clearable:""},{default:s(()=>[(l(!0),V(g,null,S(e.dicData,t=>(l(),r(I,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="datetime"?(l(),r(Z,{key:4,modelValue:a[e.prop],"onUpdate:modelValue":t=>a[e.prop]=t,type:"datetime",placeholder:"请选择"+e.label,disabled:c.value||e.disabled,format:e.format||"YYYY-MM-DD HH:mm:ss","value-format":e.valueFormat||"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","format","value-format"])):e.type==="switch"?(l(),r(q,{key:5,modelValue:a[e.prop],"onUpdate:modelValue":t=>a[e.prop]=t,disabled:c.value||e.disabled,"active-value":e.activeValue||"1","inactive-value":e.inactiveValue||"0"},null,8,["modelValue","onUpdate:modelValue","disabled","active-value","inactive-value"])):f("",!0)]),_:2},1032,["label","prop"])]),_:2},1032,["span"])):f("",!0)],64))),128))]),_:1})]),_:1},8,["model","rules","label-width"])],4)]),_:1},8,["modelValue","title","width"])}}}),de=X(ie,[["__scopeId","data-v-b9719509"]]);export{de as default};
