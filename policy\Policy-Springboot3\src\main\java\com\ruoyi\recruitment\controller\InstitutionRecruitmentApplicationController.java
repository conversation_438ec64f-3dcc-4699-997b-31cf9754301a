package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.recruitment.domain.InstitutionRecruitmentApplication;
import com.ruoyi.recruitment.service.IInstitutionRecruitmentApplicationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 机构招募申请Controller
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/recruitment/institution")
public class InstitutionRecruitmentApplicationController extends BaseController
{
    @Autowired
    private IInstitutionRecruitmentApplicationService institutionRecruitmentApplicationService;

    /**
     * 查询机构招募申请列表（管理端）
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:list')")
    @GetMapping("/applications")
    public TableDataInfo listApplications(InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        startPage();
        List<InstitutionRecruitmentApplication> list = institutionRecruitmentApplicationService.selectInstitutionRecruitmentApplicationList(institutionRecruitmentApplication);
        return getDataTable(list);
    }

    /**
     * 根据招募ID查询申请列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:list')")
    @GetMapping("/{recruitmentId}/applications")
    public TableDataInfo getApplicationsByRecruitmentId(@PathVariable("recruitmentId") Long recruitmentId)
    {
        startPage();
        List<InstitutionRecruitmentApplication> list = institutionRecruitmentApplicationService.selectApplicationsByRecruitmentId(recruitmentId);
        return getDataTable(list);
    }

    /**
     * 导出机构招募申请列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:export')")
    @Log(title = "机构招募申请", businessType = BusinessType.EXPORT)
    @PostMapping("/application/export")
    public void export(HttpServletResponse response, InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        List<InstitutionRecruitmentApplication> list = institutionRecruitmentApplicationService.selectInstitutionRecruitmentApplicationList(institutionRecruitmentApplication);
        ExcelUtil<InstitutionRecruitmentApplication> util = new ExcelUtil<InstitutionRecruitmentApplication>(InstitutionRecruitmentApplication.class);
        util.exportExcel(response, list, "机构招募申请数据");
    }

    /**
     * 获取机构招募申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:query')")
    @GetMapping(value = "/application/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(institutionRecruitmentApplicationService.selectInstitutionRecruitmentApplicationByApplicationId(applicationId));
    }

    /**
     * 新增机构招募申请（管理端）
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:add')")
    @Log(title = "机构招募申请", businessType = BusinessType.INSERT)
    @PostMapping("/application")
    public AjaxResult add(@RequestBody InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        return toAjax(institutionRecruitmentApplicationService.insertInstitutionRecruitmentApplication(institutionRecruitmentApplication));
    }

    /**
     * 修改机构招募申请（管理端）
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:edit')")
    @Log(title = "机构招募申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application")
    public AjaxResult edit(@RequestBody InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        return toAjax(institutionRecruitmentApplicationService.updateInstitutionRecruitmentApplication(institutionRecruitmentApplication));
    }

    /**
     * 删除机构招募申请
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:remove')")
    @Log(title = "机构招募申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/application/{applicationIds}")
    public AjaxResult remove(@PathVariable Long[] applicationIds)
    {
        return toAjax(institutionRecruitmentApplicationService.deleteInstitutionRecruitmentApplicationByApplicationIds(applicationIds));
    }

    /**
     * 审核申请
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:review')")
    @Log(title = "审核机构申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application/{applicationId}/review")
    public AjaxResult reviewApplication(@PathVariable Long applicationId, @RequestBody ReviewRequest request)
    {
        return toAjax(institutionRecruitmentApplicationService.reviewApplication(applicationId, request.getStatus(), request.getReviewComment()));
    }

    /**
     * 选中机构
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:select')")
    @Log(title = "选中机构", businessType = BusinessType.UPDATE)
    @PutMapping("/application/{applicationId}/select")
    public AjaxResult selectInstitution(@PathVariable Long applicationId)
    {
        return toAjax(institutionRecruitmentApplicationService.selectInstitution(applicationId));
    }

    /**
     * 取消选中机构
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:select')")
    @Log(title = "取消选中机构", businessType = BusinessType.UPDATE)
    @PutMapping("/application/{applicationId}/unselect")
    public AjaxResult unselectInstitution(@PathVariable Long applicationId)
    {
        return toAjax(institutionRecruitmentApplicationService.unselectInstitution(applicationId));
    }

    /**
     * 批量审核申请
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:batch')")
    @Log(title = "批量审核申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application/batch/review")
    public AjaxResult batchReview(@RequestBody BatchReviewRequest request)
    {
        return toAjax(institutionRecruitmentApplicationService.batchReviewApplications(request.getApplicationIds(), request.getStatus(), request.getReviewComment()));
    }

    /**
     * 获取申请统计信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:application:stats')")
    @GetMapping("/{recruitmentId}/application/stats")
    public AjaxResult getApplicationStats(@PathVariable Long recruitmentId)
    {
        InstitutionRecruitmentApplication stats = institutionRecruitmentApplicationService.getApplicationStats(recruitmentId);
        return success(stats);
    }

    // ==================== 公开接口（用户端） ====================

    /**
     * 提交机构申请（公开接口）
     */
    @Log(title = "提交机构申请", businessType = BusinessType.INSERT)
    @PostMapping("/application/submit")
    public AjaxResult submitApplication(@RequestBody InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        try {
            return toAjax(institutionRecruitmentApplicationService.submitApplication(institutionRecruitmentApplication));
        } catch (RuntimeException e) {
            return error(e.getMessage());
        }
    }

    /**
     * 获取我的申请记录（公开接口）
     */
    @GetMapping("/application/my")
    public AjaxResult getMyApplications()
    {
        Long userId = SecurityUtils.getUserId();
        List<InstitutionRecruitmentApplication> list = institutionRecruitmentApplicationService.selectApplicationsByUserId(userId);
        return success(list);
    }

    /**
     * 取消我的申请（公开接口）
     */
    @Log(title = "取消申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application/{applicationId}/cancel")
    public AjaxResult cancelMyApplication(@PathVariable Long applicationId)
    {
        // 验证申请是否属于当前用户
        InstitutionRecruitmentApplication application = institutionRecruitmentApplicationService.selectInstitutionRecruitmentApplicationByApplicationId(applicationId);
        if (application == null || !application.getApplicantUserId().equals(SecurityUtils.getUserId())) {
            return error("无权操作此申请");
        }
        
        return toAjax(institutionRecruitmentApplicationService.cancelApplication(applicationId));
    }

    /**
     * 更新申请（重新申请，公开接口）
     */
    @Log(title = "重新申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application/update")
    public AjaxResult updateApplicationForReapply(@RequestBody InstitutionRecruitmentApplication institutionRecruitmentApplication)
    {
        // 验证申请是否属于当前用户
        InstitutionRecruitmentApplication existingApplication = institutionRecruitmentApplicationService.selectInstitutionRecruitmentApplicationByApplicationId(institutionRecruitmentApplication.getApplicationId());
        if (existingApplication == null || !existingApplication.getApplicantUserId().equals(SecurityUtils.getUserId())) {
            return error("无权操作此申请");
        }
        
        return toAjax(institutionRecruitmentApplicationService.updateApplicationForReapply(institutionRecruitmentApplication));
    }

    /**
     * 检查是否已申请（公开接口）
     */
    @GetMapping("/application/check/{recruitmentId}")
    public AjaxResult checkExistingApplication(@PathVariable Long recruitmentId)
    {
        Long userId = SecurityUtils.getUserId();
        InstitutionRecruitmentApplication application = institutionRecruitmentApplicationService.checkExistingApplication(recruitmentId, userId);
        return success(application);
    }

    /**
     * 审核请求对象
     */
    public static class ReviewRequest {
        private String status;
        private String reviewComment;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getReviewComment() {
            return reviewComment;
        }

        public void setReviewComment(String reviewComment) {
            this.reviewComment = reviewComment;
        }
    }

    /**
     * 批量审核请求对象
     */
    public static class BatchReviewRequest {
        private Long[] applicationIds;
        private String status;
        private String reviewComment;

        public Long[] getApplicationIds() {
            return applicationIds;
        }

        public void setApplicationIds(Long[] applicationIds) {
            this.applicationIds = applicationIds;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getReviewComment() {
            return reviewComment;
        }

        public void setReviewComment(String reviewComment) {
            this.reviewComment = reviewComment;
        }
    }
}
