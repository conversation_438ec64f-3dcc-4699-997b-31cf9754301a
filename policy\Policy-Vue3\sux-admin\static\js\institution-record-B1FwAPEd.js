import{_ as ne,d as se,r as f,D as oe,I as re,e as ce,J as M,f as b,c as g,o as d,l as T,i,k as e,h as u,m as w,p as l,t as n,j as R,L as J,M as W,$ as ue,a1 as pe,x as de,y as fe,E as _e}from"./index-DP10CBaW.js";import{l as ge}from"./order-ZnAGpiqD.js";import{g as me,c as ve}from"./institutionApplication-DSgGW-Aq.js";import{T as he}from"./index-BWWetMd6.js";const k=N=>(de("data-v-eeb1f32a"),N=N(),fe(),N),ye={class:"institution-application-container app-container"},be=k(()=>u("div",{class:"page-header"},[u("h2",{class:"page-title"},"申请记录"),u("p",{class:"page-description"},"查看已提交的机构申请记录和审核状态")],-1)),Te={class:"price"},Se={key:1},Ce={class:"operation-btns"},we={key:1,class:"loading-placeholder"},ke={key:0,class:"application-detail"},Ne={class:"detail-section"},Ae=k(()=>u("h4",{class:"section-title"},"基本信息",-1)),Le={class:"detail-section"},Pe=k(()=>u("h4",{class:"section-title"},"培训能力",-1)),Fe={class:"detail-section"},xe=k(()=>u("h4",{class:"section-title"},"申请材料",-1)),De={class:"uploaded-materials"},Ie={class:"material-header"},ze={class:"material-info"},Me={class:"material-name"},Re={class:"material-status"},Ve={key:0,class:"material-files"},Oe={class:"file-grid"},Be={key:0,class:"detail-section"},Ee=k(()=>u("h4",{class:"section-title"},"审核信息",-1)),$e={__name:"institution-record",setup(N){const{proxy:j}=se(),V=f(!1),A=f([]),F=f(!1),s=f(null),x=f(0),O=f([]),B=f([]),D=f(!1),I=f(!1),U=f(null),S=f({}),G=oe({queryParams:{pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"}}),{queryParams:m}=re(G),E=f([{name:"机构营业执照或组织机构代码证",files:[],field:"qualificationFiles"},{name:"培训计划详细方案",files:[],field:"trainingPlanFile"},{name:"师资队伍资质证明材料",files:[],field:"teacherCertFiles"},{name:"培训场地及设施设备证明",files:[],field:"facilityFiles"},{name:"其他相关资质证明材料",files:[],field:"otherFiles"}]);ce(async()=>{await H(),h()});const H=async()=>{try{O.value=[{prop:"orderTitle",label:"培训标题",minWidth:200,showOverflowTooltip:!0},{prop:"trainingType",label:"培训类型",width:120,tableSlot:!0},{prop:"trainingLevel",label:"培训级别",width:120},{prop:"trainingDuration",label:"培训时长",width:120},{prop:"trainingFee",label:"培训费用",width:120,tableSlot:!0},{prop:"trainingTime",label:"培训时间",width:200,tableSlot:!0},{prop:"trainingAddress",label:"培训地址",minWidth:150,showOverflowTooltip:!0},{prop:"registrationDeadline",label:"申请截止",width:150,tableSlot:!0},{prop:"applicationStatus",label:"申请状态",width:120,tableSlot:!0},{prop:"applicationTime",label:"申请时间",width:150,tableSlot:!0}],B.value=[{prop:"orderTitle",label:"培训标题",type:"input"},{prop:"trainingType",label:"培训类型",type:"input"},{prop:"trainingLevel",label:"培训级别",type:"input"}],I.value=!0}catch(o){I.value=!1,console.error("初始化配置失败:",o)}},h=async()=>{D.value=!0,V.value=!0;try{const r=(await me()).data||[];if(r.length===0){A.value=[],x.value=0;return}let c={...m.value};S.value.createTime&&Array.isArray(S.value.createTime)&&S.value.createTime.length===2&&(c=j.addDateRange(c,S.value.createTime));const p=(await ge(c)).rows||[];A.value=r.map(t=>{const v=p.find(C=>C.orderId===t.orderId);return v?{...v,institutionApplication:t,applicationStatus:t.applicationStatus,canApply:t.applicationStatus==="2"||t.applicationStatus==="3"}:null}).filter(t=>t!==null).filter(t=>{var v,C,L;return!(c.orderTitle&&!((v=t.orderTitle)!=null&&v.includes(c.orderTitle))||c.trainingType&&!((C=t.trainingType)!=null&&C.includes(c.trainingType))||c.trainingLevel&&!((L=t.trainingLevel)!=null&&L.includes(c.trainingLevel)))}),x.value=A.value.length}catch(o){M.error("获取申请记录失败"),console.error(o)}finally{D.value=!1,V.value=!1}},K=o=>{S.value={...o};const{createTime:r,...c}=o||{};Object.assign(m.value,c),m.value.pageNum=1,h()},Q=()=>{m.value={pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"},S.value={},h()},X=o=>{m.value.pageNum=o,h()},Y=o=>{m.value.pageSize=o,m.value.pageNum=1,h()},Z=()=>{},ee=()=>{h()},te=o=>{s.value=o,console.log("申请数据:",o),E.value.forEach(r=>{const c=o[r.field];if(console.log(`${r.name} 文件数据:`,c),c)try{let _=JSON.parse(c)||[];r.files=_.map(p=>({name:p.name||p.fileName||p.sourceFileName,fileName:p.fileName||p.name||p.sourceFileName,sourceFileName:p.sourceFileName||p.name||p.fileName,url:p.url||p.filePath,filePath:p.filePath||p.url})),console.log(`${r.name} 解析后文件:`,r.files)}catch(_){console.error(`${r.name} JSON解析失败:`,_),r.files=[]}else r.files=[]}),F.value=!0},ae=async o=>{try{await _e.confirm("确认要取消申请吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ve(o.applicationId),M.success("取消申请成功"),await h()}catch(r){r!=="cancel"&&M.error(r.msg||"取消申请失败")}},ie=o=>({技能培训:"primary",管理培训:"success",安全培训:"warning",专业培训:"info"})[o]||"default",$=o=>({0:"warning",1:"success",2:"danger",3:"info"})[o]||"info",q=o=>({0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"})[o]||"未知",y=o=>o?new Date(o).toLocaleDateString("zh-CN"):"--";return(o,r)=>{const c=b("el-button"),_=b("el-tag"),p=b("el-empty"),t=b("el-descriptions-item"),v=b("el-descriptions"),C=b("el-icon"),L=b("el-dialog");return d(),g("div",ye,[be,I.value?(d(),T(he,{key:0,columns:O.value,data:A.value,loading:D.value,showIndex:!0,searchColumns:B.value,showOperation:!0,operationLabel:"操作",operationWidth:"300",fixedOperation:!0,ref_key:"tableListRef",ref:U,onSearch:K,onReset:Q,defaultPage:{pageSize:R(m).pageSize,currentPage:R(m).pageNum,total:x.value},onCurrentChange:X,onSizeChange:Y,onSelectionChange:Z},{"menu-left":e(()=>[i(c,{type:"primary",class:"custom-btn",onClick:ee},{default:e(()=>[l("刷新")]),_:1})]),trainingType:e(({row:a})=>[i(_,{type:ie(a.trainingType),size:"small"},{default:e(()=>[l(n(a.trainingType),1)]),_:2},1032,["type"])]),trainingFee:e(({row:a})=>[u("span",Te,n(a.trainingFee?"￥"+a.trainingFee:"面议"),1)]),trainingTime:e(({row:a})=>[l(n(y(a.startDate))+" 至 "+n(y(a.endDate)),1)]),registrationDeadline:e(({row:a})=>[l(n(y(a.registrationDeadline)),1)]),applicationStatus:e(({row:a})=>[a.institutionApplication&&a.institutionApplication.applicationStatus?(d(),T(_,{key:0,type:$(a.institutionApplication.applicationStatus),size:"small"},{default:e(()=>[l(n(q(a.institutionApplication.applicationStatus)),1)]),_:2},1032,["type"])):(d(),g("span",Se,"未申请"))]),applicationTime:e(({row:a})=>[l(n(a.institutionApplication?y(a.institutionApplication.applicationTime):"--"),1)]),menu:e(({row:a})=>[u("div",Ce,[i(c,{type:"success",link:"",onClick:z=>te(a.institutionApplication)},{default:e(()=>[l(" 查看详情 ")]),_:2},1032,["onClick"]),a.institutionApplication.applicationStatus==="0"?(d(),T(c,{key:0,type:"warning",link:"",onClick:z=>ae(a.institutionApplication)},{default:e(()=>[l(" 取消申请 ")]),_:2},1032,["onClick"])):w("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(d(),g("div",we,[i(p,{description:"正在加载表格配置..."})])),i(L,{modelValue:F.value,"onUpdate:modelValue":r[0]||(r[0]=a=>F.value=a),title:"申请详情",width:"1000px","append-to-body":"","destroy-on-close":""},{default:e(()=>[s.value?(d(),g("div",ke,[u("div",Ne,[Ae,i(v,{column:2,border:""},{default:e(()=>[i(t,{label:"机构名称"},{default:e(()=>[l(n(s.value.institutionName),1)]),_:1}),i(t,{label:"机构代码"},{default:e(()=>[l(n(s.value.institutionCode),1)]),_:1}),i(t,{label:"法定代表人"},{default:e(()=>[l(n(s.value.legalPerson),1)]),_:1}),i(t,{label:"机构类型"},{default:e(()=>[l(n(s.value.institutionType),1)]),_:1}),i(t,{label:"联系人"},{default:e(()=>[l(n(s.value.contactPerson),1)]),_:1}),i(t,{label:"联系电话"},{default:e(()=>[l(n(s.value.contactPhone),1)]),_:1}),i(t,{label:"联系邮箱"},{default:e(()=>[l(n(s.value.contactEmail),1)]),_:1}),i(t,{label:"成立时间"},{default:e(()=>[l(n(y(s.value.establishedDate)),1)]),_:1}),i(t,{label:"注册资本"},{default:e(()=>[l(n(s.value.registeredCapital)+"万元",1)]),_:1}),i(t,{label:"申请状态"},{default:e(()=>[i(_,{type:$(s.value.applicationStatus)},{default:e(()=>[l(n(q(s.value.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),i(t,{label:"申请时间"},{default:e(()=>[l(n(y(s.value.applicationTime)),1)]),_:1}),i(t,{label:"机构地址",span:2},{default:e(()=>[l(n(s.value.institutionAddress),1)]),_:1})]),_:1})]),u("div",Le,[Pe,i(v,{column:1,border:""},{default:e(()=>[i(t,{label:"经营范围"},{default:e(()=>[l(n(s.value.businessScope||"--"),1)]),_:1}),i(t,{label:"培训经验"},{default:e(()=>[l(n(s.value.trainingExperience||"--"),1)]),_:1}),i(t,{label:"培训能力"},{default:e(()=>[l(n(s.value.trainingCapacity||"--"),1)]),_:1}),i(t,{label:"培训计划"},{default:e(()=>[l(n(s.value.trainingPlan||"--"),1)]),_:1}),i(t,{label:"师资信息"},{default:e(()=>[l(n(s.value.teacherInfo||"--"),1)]),_:1}),i(t,{label:"设施设备"},{default:e(()=>[l(n(s.value.facilityInfo||"--"),1)]),_:1}),s.value.applicationNote?(d(),T(t,{key:0,label:"申请备注"},{default:e(()=>[l(n(s.value.applicationNote),1)]),_:1})):w("",!0)]),_:1})]),u("div",Fe,[xe,u("div",De,[(d(!0),g(J,null,W(E.value,(a,z)=>(d(),g("div",{class:"material-item",key:z},[u("div",Ie,[u("div",ze,[u("div",Me,[i(C,{class:"material-icon"},{default:e(()=>[i(R(ue))]),_:1}),u("span",null,n(a.name),1)]),u("div",Re,[a.files&&a.files.length>0?(d(),T(_,{key:0,type:"success",size:"small"},{default:e(()=>[l(" 已上传 "+n(a.files.length)+" 个文件 ",1)]),_:2},1024)):(d(),T(_,{key:1,type:"info",size:"small"},{default:e(()=>[l("未上传")]),_:1}))])])]),a.files&&a.files.length>0?(d(),g("div",Ve,[u("div",Oe,[(d(!0),g(J,null,W(a.files,(P,le)=>(d(),g("div",{class:"file-card",key:le},[i(pe,{file:{filePath:P.url||P.filePath,sourceFileName:P.name||P.fileName}},null,8,["file"])]))),128))])])):w("",!0)]))),128))])]),s.value.applicationStatus!=="0"?(d(),g("div",Be,[Ee,i(v,{column:2,border:""},{default:e(()=>[i(t,{label:"审核时间"},{default:e(()=>[l(n(y(s.value.reviewTime)),1)]),_:1}),i(t,{label:"审核人"},{default:e(()=>[l(n(s.value.reviewer||"--"),1)]),_:1}),s.value.reviewComment?(d(),T(t,{key:0,label:"审核意见",span:2},{default:e(()=>[l(n(s.value.reviewComment),1)]),_:1})):w("",!0)]),_:1})])):w("",!0)])):w("",!0)]),_:1},8,["modelValue"])])}}},Ue=ne($e,[["__scopeId","data-v-eeb1f32a"]]);export{Ue as default};
