import{Y as i,_ as $,d as Y,r as m,f as c,l as u,o as l,k as n,h as a,c as p,L as b,M,j as w,a7 as S,i as v,p as f,t as r,m as y,P as q,ac as G,ad as H,ae as J,x as K,y as Q}from"./index-DP10CBaW.js";function xe(e){return i({url:"/policy/application/list",method:"get",params:e})}function Te(e){return i({url:"/policy/application/"+e,method:"get"})}function be(e){return i({url:"/policy/application",method:"post",data:e})}function Me(e){return i({url:"/policy/application",method:"put",data:e})}function we(e){return i({url:"/policy/application/first-review",method:"post",data:e})}function Se(e){return i({url:"/policy/application/final-review",method:"post",data:e})}function W(e){return i({url:"/policy/application/approval-records/"+e,method:"get"})}function Ae(e){return i({url:"/policy/application/applied-policies",method:"get",params:e})}const g=e=>(K("data-v-1de46b36"),e=e(),Q(),e),X={class:"approval-records-container"},Z={class:"record-header"},ee={class:"record-level"},te={class:"record-status"},ae={class:"record-content"},oe={class:"record-info"},se={key:0,class:"info-item"},le=g(()=>a("span",{class:"label"},"审批人：",-1)),ne={class:"value"},ie={key:1,class:"info-item"},ce=g(()=>a("span",{class:"label"},"审批时间：",-1)),pe={class:"value"},re={key:0,class:"record-comment"},de=g(()=>a("div",{class:"comment-label"},"审批意见：",-1)),ue={class:"comment-content"},_e={key:1,class:"record-files"},me=g(()=>a("div",{class:"files-label"},"相关文件：",-1)),ve={class:"files-list"},fe={class:"dialog-footer"},ye={__name:"ApprovalRecordsDialog",setup(e,{expose:A}){const{proxy:C}=Y(),_=m(!1),k=m("审核记录"),d=m([]),x=m(!1),V=async o=>{_.value=!0,k.value="审核记录",x.value=!0;try{const s=await W(o);d.value=s.data||[]}catch(s){console.error("获取审核记录失败:",s),C.$modal.msgError("获取审核记录失败"),d.value=[]}finally{x.value=!1}},I=()=>{_.value=!1,d.value=[]},P=o=>({0:"warning",1:"success",2:"danger"})[o]||"info",L=o=>({0:H,1:G,2:q})[o]||J,N=o=>({1:"primary",2:"warning"})[o]||"info",R=o=>({1:"初审",2:"终审"})[o]||"未知层级",D=o=>({0:"warning",1:"success",2:"danger"})[o]||"info",z=o=>({0:"待审批",1:"审批通过",2:"审批拒绝"})[o]||"未知状态";return A({openDialog:V}),(o,s)=>{const h=c("el-tag"),B=c("el-card"),F=c("el-timeline-item"),j=c("el-timeline"),U=c("el-empty"),E=c("el-button"),O=c("el-dialog");return l(),u(O,{modelValue:_.value,"onUpdate:modelValue":s[0]||(s[0]=t=>_.value=t),title:k.value,width:"1000px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:n(()=>[a("div",fe,[v(E,{onClick:I},{default:n(()=>[f("关闭")]),_:1})])]),default:n(()=>[a("div",X,[d.value.length>0?(l(),u(j,{key:0},{default:n(()=>[(l(!0),p(b,null,M(d.value,(t,he)=>(l(),u(F,{key:t.recordId,timestamp:w(S)(t.approvalTime),placement:"top",type:P(t.approvalStatus),icon:L(t.approvalStatus)},{default:n(()=>[v(B,{class:"record-card"},{default:n(()=>[a("div",Z,[a("div",ee,[v(h,{type:N(t.approvalLevel),size:"small"},{default:n(()=>[f(r(R(t.approvalLevel)),1)]),_:2},1032,["type"])]),a("div",te,[v(h,{type:D(t.approvalStatus),size:"small"},{default:n(()=>[f(r(z(t.approvalStatus)),1)]),_:2},1032,["type"])])]),a("div",ae,[a("div",oe,[t.approverUserName?(l(),p("div",se,[le,a("span",ne,r(t.approverUserName),1)])):y("",!0),t.approvalTime?(l(),p("div",ie,[ce,a("span",pe,r(w(S)(t.approvalTime)),1)])):y("",!0)]),t.approvalComment?(l(),p("div",re,[de,a("div",ue,r(t.approvalComment),1)])):y("",!0),t.approvalFiles&&t.approvalFiles.length>0?(l(),p("div",_e,[me,a("div",ve,[(l(!0),p(b,null,M(t.approvalFiles,T=>(l(),u(h,{key:T.id,type:"info",size:"small",class:"file-tag"},{default:n(()=>[f(r(T.name),1)]),_:2},1024))),128))])])):y("",!0)])]),_:2},1024)]),_:2},1032,["timestamp","type","icon"]))),128))]),_:1})):(l(),u(U,{key:1,description:"暂无审核记录"}))])]),_:1},8,["modelValue","title"])}}},ge=$(ye,[["__scopeId","data-v-1de46b36"]]),Ce=Object.freeze(Object.defineProperty({__proto__:null,default:ge},Symbol.toStringTag,{value:"Module"}));export{ge as A,Se as a,Ae as b,be as c,Ce as d,we as f,Te as g,xe as l,Me as u};
