import{l as ue,a as pe,u as ce,r as de}from"./application-CqX6Rd9f.js";import{g as me,e as fe}from"./columnUtils-DYlA-XL_.js";import{T as ge}from"./index-BWWetMd6.js";import ve from"./TrainingApplicationFormDialog-BJraDHPs.js";import{C as he,d as be,r as i,D as _e,I as ye,e as we,R as Ce,f as c,K as Se,c as B,o as h,l as T,i as o,j as l,k as r,h as R,G as W,m as O,p as s,t as D}from"./index-DP10CBaW.js";import"./order-ZnAGpiqD.js";function Te(z){return{column:[{label:"报名ID",prop:"applicationId",width:80,align:"center",search:!1,form:!1,detail:!0},{label:"培训订单",prop:"orderTitle",minWidth:200,search:!1,form:!1,detail:!0,overHidden:!0},{label:"培训订单ID",prop:"orderId",width:120,align:"center",search:!0,form:!0,detail:!1,type:"select",dicUrl:"/training/order/list",dicMethod:"get",dicQuery:{orderStatus:"1"},props:{label:"orderTitle",value:"orderId"},rules:[{required:!0,message:"请选择培训订单",trigger:"change"}]},{label:"报名人姓名",prop:"applicantName",minWidth:120,search:!0,form:!0,detail:!0,rules:[{required:!0,message:"请输入报名人姓名",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}]},{label:"报名人手机号",prop:"applicantPhone",width:130,search:!0,form:!0,detail:!0,rules:[{required:!0,message:"请输入报名人手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},{label:"报名人邮箱",prop:"applicantEmail",minWidth:180,search:!1,form:!0,detail:!0,rules:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},{label:"身份证号",prop:"applicantIdCard",width:180,search:!1,form:!0,detail:!0,rules:[{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号",trigger:"blur"}]},{label:"性别",prop:"applicantGender",width:80,align:"center",search:!1,form:!0,detail:!0,type:"select",dicData:[{label:"男",value:"男"},{label:"女",value:"女"}]},{label:"年龄",prop:"applicantAge",width:80,align:"center",search:!1,form:!0,detail:!0,type:"number",min:16,max:100},{label:"学历",prop:"applicantEducation",width:100,align:"center",search:!1,form:!0,detail:!0,type:"select",dicData:[{label:"小学",value:"小学"},{label:"初中",value:"初中"},{label:"中专",value:"中专"},{label:"高中",value:"高中"},{label:"大专",value:"大专"},{label:"本科",value:"本科"},{label:"硕士",value:"硕士"},{label:"博士",value:"博士"}]},{label:"工作经验",prop:"applicantExperience",minWidth:200,search:!1,form:!0,detail:!0,type:"textarea",span:24,overHidden:!0},{label:"联系地址",prop:"applicantAddress",minWidth:200,search:!1,form:!0,detail:!0,overHidden:!0},{label:"报名状态",prop:"applicationStatus",width:100,align:"center",search:!0,form:!1,detail:!0,type:"select",dicData:[{label:"待审核",value:"0"},{label:"已通过",value:"1"},{label:"已拒绝",value:"2"},{label:"已取消",value:"3"}],slot:!0},{label:"报名时间",prop:"applicationTime",width:160,align:"center",search:!1,form:!1,detail:!0,type:"datetime",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",slot:!0},{label:"审核人",prop:"reviewer",width:100,align:"center",search:!0,form:!1,detail:!0},{label:"审核时间",prop:"reviewTime",width:160,align:"center",search:!1,form:!1,detail:!0,type:"datetime",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",slot:!0},{label:"审核意见",prop:"reviewComment",minWidth:200,search:!1,form:!1,detail:!0,type:"textarea",span:24,overHidden:!0},{label:"报名备注",prop:"applicationNote",minWidth:200,search:!1,form:!0,detail:!0,type:"textarea",span:24,overHidden:!0},{label:"创建时间",prop:"createTime",width:160,align:"center",search:!0,form:!1,detail:!0,type:"datetimerange",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss"}],formOption:{submitBtn:!0,emptyBtn:!0,labelWidth:120,gutter:20,column:2,menuPosition:"center",detail:{labelWidth:120,column:2,gutter:20}},searchMenuSpan:6,page:!0,selection:!0,index:!0,indexLabel:"序号",menu:!0,menuWidth:250,menuAlign:"center",height:"auto",calcHeight:350,border:!0,stripe:!0,menuBtn:!0,searchBtn:!0,searchSpan:4,addBtn:!0,editBtn:!0,delBtn:!0,viewBtn:!0}}const De={class:"training-application-container app-container"},xe={class:"operation-btns"},Ye={key:1,class:"loading-placeholder"},ke={class:"dialog-footer"},He=he({name:"TrainingApplication"}),Ae=Object.assign(He,{setup(z){const{proxy:m}=be(),x=i([]),Y=i(!0),$=i([]),L=i(!0),q=i(!0),k=i(0),v=i(!0),H=i([]),M=i([]),y=i(!1),w=i(!1),C=i({dialogWidth:"800px",dialogHeight:"70vh"}),E=i(null),b=i(null),I=i([]),g=i({}),n=i({visible:!1,title:"",form:{applicationId:null,status:"1",reviewComment:""}}),U=_e({queryParams:{pageNum:1,pageSize:10,orderId:void 0,applicantName:void 0,applicantPhone:void 0,applicationStatus:void 0,reviewer:void 0}}),{queryParams:u}=ye(U);we(async()=>{await j(),f()});const j=async()=>{try{const e=Te(m),t=await me({baseOption:e,proxy:m}),{tableColumns:d,searchColumns:p,formFields:S,formOptions:_}=fe(t);H.value=d,M.value=p,I.value=S,C.value={...C.value,..._},w.value=!0}catch(e){w.value=!1,console.error("初始化配置失败:",e)}};function f(){y.value=!0,Y.value=!0;let e={...u.value};g.value.createTime&&Array.isArray(g.value.createTime)&&g.value.createTime.length===2&&(e=m.addDateRange(e,g.value.createTime)),ue(e).then(t=>{y.value=!1,Y.value=!1,x.value=t.rows,k.value=t.total,Ce(()=>{v.value=!1})})}const G=e=>{v.value=!0,g.value={...e};const{createTime:t,...d}=e||{};Object.assign(u.value,d),u.value.pageNum=1,f()},K=()=>{v.value=!0,u.value={pageNum:1,pageSize:10,orderId:void 0,applicantName:void 0,applicantPhone:void 0,applicationStatus:void 0,reviewer:void 0},g.value={},f()},Q=e=>{v.value=!0,u.value.pageNum=e,f()},X=e=>{v.value=!0,u.value.pageSize=e,u.value.pageNum=1,f()};function J(e){$.value=e.map(t=>t.applicationId),L.value=e.length!=1,q.value=!e.length}const Z=e=>({0:"warning",1:"success",2:"danger",3:"info"})[e]||"info",ee=e=>({0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"})[e]||"未知",F=e=>e?m.parseTime(e,"{y}-{m}-{d} {h}:{i}:{s}"):"--",te=e=>{var t;(t=b.value)==null||t.openDialog("view","查看培训报名",e)},ae=async e=>{var t,d;try{e.type==="add"?(await pe(e.data),m.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await ce(e.data),m.$modal.msgSuccess("修改成功")),(t=b.value)==null||t.onSubmitSuccess(),f()}catch(p){(d=b.value)==null||d.onSubmitError(),console.error("提交失败:",p)}},le=()=>{};function N(e,t){n.value.visible=!0,n.value.title=t==="1"?"通过审核":"拒绝审核",n.value.form.applicationId=e.applicationId,n.value.form.status=t,n.value.form.reviewComment=""}function re(){const e=n.value.form;de(e.applicationId,e.status,e.reviewComment).then(()=>{n.value.visible=!1,f(),m.$modal.msgSuccess("审核成功")}).catch(()=>{})}return(e,t)=>{const d=c("el-tag"),p=c("el-button"),S=c("el-empty"),_=c("el-radio"),ie=c("el-radio-group"),V=c("el-form-item"),ne=c("el-input"),oe=c("el-form"),se=c("el-dialog"),P=Se("hasPermi");return h(),B("div",De,[l(w)?(h(),T(ge,{key:0,columns:l(H),data:l(x),loading:l(y),showIndex:!0,searchColumns:l(M),showOperation:!0,operationLabel:"操作",operationWidth:"250",fixedOperation:!0,ref_key:"tableListRef",ref:E,onSearch:G,onReset:K,defaultPage:{pageSize:l(u).pageSize,currentPage:l(u).pageNum,total:l(k)},onCurrentChange:Q,onSizeChange:X,onSelectionChange:J},{"menu-left":r(()=>[]),applicationStatus:r(({row:a})=>[o(d,{type:Z(a.applicationStatus)},{default:r(()=>[s(D(ee(a.applicationStatus)),1)]),_:2},1032,["type"])]),applicationTime:r(({row:a})=>[s(D(F(a.applicationTime)),1)]),reviewTime:r(({row:a})=>[s(D(F(a.reviewTime)),1)]),menu:r(({row:a})=>[R("div",xe,[o(p,{type:"primary",link:"",onClick:A=>te(a)},{default:r(()=>[s("查看")]),_:2},1032,["onClick"]),a.applicationStatus==="0"?W((h(),T(p,{key:0,type:"success",link:"",onClick:A=>N(a,"1")},{default:r(()=>[s("通过")]),_:2},1032,["onClick"])),[[P,["training:application:review"]]]):O("",!0),a.applicationStatus==="0"?W((h(),T(p,{key:1,type:"warning",link:"",onClick:A=>N(a,"2")},{default:r(()=>[s("拒绝")]),_:2},1032,["onClick"])),[[P,["training:application:review"]]]):O("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(h(),B("div",Ye,[o(S,{description:"正在加载表格配置..."})])),o(ve,{ref_key:"applicationFormDialogRef",ref:b,formFields:l(I),formOption:l(C),onSubmit:ae,onCancel:le},null,8,["formFields","formOption"]),o(se,{modelValue:l(n).visible,"onUpdate:modelValue":t[3]||(t[3]=a=>l(n).visible=a),title:l(n).title,width:"500px","append-to-body":""},{footer:r(()=>[R("div",ke,[o(p,{onClick:t[2]||(t[2]=a=>l(n).visible=!1)},{default:r(()=>[s("取 消")]),_:1}),o(p,{type:"primary",onClick:re},{default:r(()=>[s("确 定")]),_:1})])]),default:r(()=>[o(oe,{ref:"reviewFormRef",model:l(n).form,"label-width":"80px"},{default:r(()=>[o(V,{label:"审核状态"},{default:r(()=>[o(ie,{modelValue:l(n).form.status,"onUpdate:modelValue":t[0]||(t[0]=a=>l(n).form.status=a)},{default:r(()=>[o(_,{value:"1"},{default:r(()=>[s("通过")]),_:1}),o(_,{value:"2"},{default:r(()=>[s("拒绝")]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(V,{label:"审核意见"},{default:r(()=>[o(ne,{modelValue:l(n).form.reviewComment,"onUpdate:modelValue":t[1]||(t[1]=a=>l(n).form.reviewComment=a),type:"textarea",rows:4,placeholder:"请输入审核意见"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}});export{Ae as default};
