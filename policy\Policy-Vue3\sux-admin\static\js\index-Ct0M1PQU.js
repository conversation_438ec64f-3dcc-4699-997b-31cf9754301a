import{a7 as ne,_ as ie,C as ue,a as ce,d as de,r,e as me,f as F,K as pe,c as A,o as c,l as h,i as D,k as d,h as he,G as v,m as k,p as f}from"./index-DP10CBaW.js";import{g as ve,e as fe}from"./columnUtils-DYlA-XL_.js";import{T as ge}from"./index-BWWetMd6.js";import be from"./RoleFormDialog-CujpbQ5S.js";import ye from"./DataScopeDialog-BGt8yFRI.js";import{g as _e,f as Ce,h as Se,i as De,j as ke,l as Re,k as Ie}from"./role-FWqI-CVH.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";import"./menu-D5wKpgmc.js";const $e=[{label:"全部数据权限",value:"1"},{label:"自定数据权限",value:"2"},{label:"部门数据权限",value:"3"},{label:"部门及以下数据权限",value:"4"},{label:"仅本人数据权限",value:"5"}],Oe=P=>{const{sys_normal_disable:R}=P.useDict("sys_normal_disable");return{dialogWidth:"800px",dialogHeight:"70vh",labelWidth:"100px",column:[{label:"基础信息",prop:"divider_basic_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"角色编号",prop:"roleId",minWidth:120,editDisplay:!1,addDisplay:!1,viewDisplay:!0},{label:"角色名称",prop:"roleName",search:!0,rules:[{required:!0,message:"角色名称不能为空",trigger:"blur"}],span:12,minWidth:150},{label:"权限字符",prop:"roleKey",search:!0,rules:[{required:!0,message:"权限字符不能为空",trigger:"blur"}],span:12,minWidth:150,placeholder:"请输入权限字符",tooltip:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)"},{label:"角色顺序",prop:"roleSort",type:"number",span:12,minWidth:100,rules:[{required:!0,message:"角色顺序不能为空",trigger:"blur"}],min:0,controlsPosition:"right"},{label:"状态",prop:"status",type:"radio",span:12,minWidth:100,search:!0,dicData:R,slot:!0},{label:"创建时间",prop:"createTime",editDisplay:!1,addDisplay:!1,type:"datetime",minWidth:160,search:!0,searchRange:!0,formatter:(l,y,I)=>ne(y,"{y}-{m}-{d} {h}:{i}:{s}")},{label:"权限配置",prop:"divider_permission_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0,addDisplay:!0,editDisplay:!0,viewDisplay:!0},{label:"菜单权限",prop:"menuIds",span:24,formSlot:!0,showColumn:!1,addDisplay:!0,editDisplay:!0,viewDisplay:!0},{label:"数据权限",prop:"dataScope",type:"select",span:24,dicData:$e,showColumn:!1,addDisplay:!1,editDisplay:!1,viewDisplay:!1},{label:"数据权限范围",prop:"deptIds",span:24,formSlot:!0,showColumn:!1,addDisplay:!1,editDisplay:!1,viewDisplay:!1},{label:"详细信息",prop:"divider_detail_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"备注",prop:"remark",type:"textarea",minRows:3,maxRows:6,span:24,showColumn:!1,placeholder:"请输入内容"}]}},We={class:"role-container app-container"},Ne={class:"operation-btns"},Ee={key:1,class:"loading-placeholder"},Fe=ue({name:"Role"}),Pe=Object.assign(Fe,{setup(P){const R=ce(),{proxy:l}=de(),y=r([]),I=r([]),_=r(!1),$=r(!1),O=r({dialogWidth:"600px",dialogHeight:"70vh"}),j=r(null),g=r(null),T=r(null),b=r(1),W=r(10),N=r(0),C=r({}),u=r([]),z=r([]),V=r([]),S=r([]),q=r(!0),B=r(!0),L=async()=>{var e;try{const a=Oe(l);if(!a)throw new Error("无法获取基础配置");const t=await ve({baseOption:a,proxy:l}),{tableColumns:s,searchColumns:o,formFields:i,formOptions:n}=fe(t);y.value=s,I.value=o,z.value=i,O.value={...O.value,...n},V.value=a.dataScopeOptions||[],$.value=!0,m()}catch(a){console.error("初始化配置失败:",a),$.value=!1,(e=l==null?void 0:l.$modal)==null||e.msgError("页面初始化失败: "+a.message)}},m=()=>{_.value=!0;const e={pageNum:b.value,pageSize:W.value,...C.value};Re(e).then(a=>{a&&a.rows?(u.value=a.rows,N.value=a.total||0):(u.value=[],N.value=0),_.value=!1}).catch(a=>{var t;console.error("加载数据失败:",a),_.value=!1,(t=l==null?void 0:l.$modal)==null||t.msgError("数据加载失败")})},w=e=>{C.value=e||{},b.value=1,m()},U=()=>{C.value={},b.value=1,m()},x=e=>{b.value=e,m()},H=e=>{W.value=e,b.value=1,m()},K=e=>{S.value=e||[],q.value=(e==null?void 0:e.length)!==1,B.value=!(e!=null&&e.length)},G=()=>{var e;(e=g.value)==null||e.openDialog("add","新增角色")},M=e=>{var a;(a=g.value)==null||a.openDialog("view","查看角色",e)},J=e=>{var t;const a=e?e.roleId:(t=S.value[0])==null?void 0:t.roleId;a&&_e(a).then(s=>{var i;const o=s.data;o.roleSort=Number(o.roleSort),(i=g.value)==null||i.openDialog("edit","修改角色",o)}).catch(s=>{var o;console.error("获取角色详情失败:",s),(o=l==null?void 0:l.$modal)==null||o.msgError("获取角色详情失败")})},Q=e=>{var s;const a=e?[e.roleId]:S.value.map(o=>o.roleId),t=e?[e.roleName]:S.value.map(o=>o.roleName);if(!a.length){(s=l==null?void 0:l.$modal)==null||s.msgError("请选择要删除的角色");return}l.$modal.confirm(`是否确认删除角色"${t.join("、")}"？`).then(()=>Ce(a.join(","))).then(()=>{m(),l.$modal.msgSuccess("删除成功")}).catch(o=>{var i;o!=="cancel"&&(console.error("删除失败:",o),(i=l==null?void 0:l.$modal)==null||i.msgError("删除失败"))})},X=()=>{var e;try{l.download("system/role/export",{...C.value},`role_${new Date().getTime()}.xlsx`)}catch(a){console.error("导出失败:",a),(e=l==null?void 0:l.$modal)==null||e.msgError("导出失败")}},Y=(e,a,t,s)=>{let o=null;if(a&&a.roleId)o=a;else if(t!==void 0&&u.value[t])o=u.value[t];else if(s!==void 0&&u.value[s])o=u.value[s];else{console.error("无法获取有效的行数据");return}o.status=e,Z(o)},Z=e=>{if(!e||typeof e!="object"||!e.roleId){console.error("状态切换失败：行数据无效",e);return}let a=e.status==="0"?"启用":"停用",t=e.roleName||e.name||"该";l.$modal.confirm('确认要"'+a+'""'+t+'"角色吗?').then(()=>Ie(e.roleId,e.status)).then(()=>{l.$modal.msgSuccess(a+"成功"),m()}).catch(s=>{s!=="cancel"&&(e.status=e.status==="0"?"1":"0",console.error("状态切换失败:",s))})},ee=e=>{var a;(a=T.value)==null||a.openDialog(e)},ae=e=>{R.push("/system/role-auth/user/"+e.roleId)},le=e=>{ke(e).then(a=>{l.$modal.msgSuccess("修改成功"),m()}).catch(a=>{var t;console.error("数据权限修改失败:",a),(t=l==null?void 0:l.$modal)==null||t.msgError("数据权限修改失败")})},te=()=>{},oe=async e=>{var a,t;try{e.type==="add"?(await Se(e.data),l.$modal.msgSuccess("新增成功")):e.type==="edit"&&(await De(e.data),l.$modal.msgSuccess("修改成功")),(a=g.value)==null||a.onSubmitSuccess(),m()}catch(s){console.error("表单提交失败:",s),(t=g.value)==null||t.onSubmitError()}},se=()=>{};return me(async()=>{await L()}),(e,a)=>{const t=F("el-button"),s=F("el-switch"),o=F("el-empty"),i=pe("hasPermi");return c(),A("div",We,[$.value?(c(),h(ge,{key:0,columns:y.value,data:u.value,loading:_.value,showIndex:!0,searchColumns:I.value,showOperation:!0,operationLabel:"操作",operationWidth:"280",fixedOperation:!0,ref_key:"tableListRef",ref:j,onSearch:w,onReset:U,defaultPage:{pageSize:W.value,currentPage:b.value,total:N.value},onCurrentChange:x,onSizeChange:H,onSelectionChange:K},{"menu-left":d(()=>[v((c(),h(t,{type:"primary",class:"custom-btn",onClick:G},{default:d(()=>[f("新 增")]),_:1})),[[i,["system:role:add"]]]),v((c(),h(t,{type:"warning",class:"custom-btn",onClick:X},{default:d(()=>[f("导 出")]),_:1})),[[i,["system:role:export"]]])]),status:d(({row:n,index:p,$index:E})=>[D(s,{"model-value":(n==null?void 0:n.status)||u.value[p]&&u.value[p].status||u.value[E]&&u.value[E].status,"active-value":"0","inactive-value":"1","onUpdate:modelValue":re=>Y(re,n,p,E)},null,8,["model-value","onUpdate:modelValue"])]),menu:d(({row:n})=>[he("div",Ne,[v((c(),h(t,{type:"primary",link:"",onClick:p=>M(n)},{default:d(()=>[f(" 查看 ")]),_:2},1032,["onClick"])),[[i,["system:role:query"]]]),n.roleId!==1?v((c(),h(t,{key:0,type:"primary",link:"",onClick:p=>J(n)},{default:d(()=>[f(" 修改 ")]),_:2},1032,["onClick"])),[[i,["system:role:edit"]]]):k("",!0),n.roleId!==1?v((c(),h(t,{key:1,type:"danger",link:"",onClick:p=>Q(n)},{default:d(()=>[f(" 删除 ")]),_:2},1032,["onClick"])),[[i,["system:role:remove"]]]):k("",!0),n.roleId!==1?v((c(),h(t,{key:2,type:"warning",link:"",onClick:p=>ee(n)},{default:d(()=>[f(" 数据权限 ")]),_:2},1032,["onClick"])),[[i,["system:role:edit"]]]):k("",!0),n.roleId!==1?v((c(),h(t,{key:3,type:"success",link:"",onClick:p=>ae(n)},{default:d(()=>[f(" 分配用户 ")]),_:2},1032,["onClick"])),[[i,["system:role:edit"]]]):k("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(c(),A("div",Ee,[D(o,{description:"正在加载表格配置..."})])),D(be,{ref_key:"roleFormDialogRef",ref:g,formFields:z.value,formOption:O.value,onSubmit:oe,onCancel:se},null,8,["formFields","formOption"]),D(ye,{ref_key:"dataScopeDialogRef",ref:T,dataScopeOptions:V.value,onSubmit:le,onCancel:te},null,8,["dataScopeOptions"])])}}}),Ue=ie(Pe,[["__scopeId","data-v-161ae8cd"]]);export{Ue as default};
