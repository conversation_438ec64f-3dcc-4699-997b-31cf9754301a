import{Y as y,_ as ve,C as be,d as ke,r as u,D as ye,I as _e,e as Ce,R as we,f as T,K as xe,c as k,o as l,l as d,i as p,j as m,k as r,h as W,G as w,m as O,p as o,L as N,M as V,t as v,N as Pe,am as Se}from"./index-DP10CBaW.js";import{g as Te,e as Oe}from"./columnUtils-DYlA-XL_.js";import{T as Ie}from"./index-BWWetMd6.js";import We from"./WorkerProfileFormDialog-C98-_CuV.js";function De(s){return y({url:"/job/worker/list",method:"get",params:s})}function Le(s){return y({url:"/job/worker/"+s,method:"get"})}function je(s){return y({url:"/job/worker",method:"post",data:s})}function $e(s){return y({url:"/job/worker",method:"put",data:s})}function ze(s){return y({url:`/job/worker/activate/${s}`,method:"put"})}function Re(s){return y({url:`/job/worker/suspend/${s}`,method:"put"})}function Ne(s){return y({url:`/job/worker/verify/${s}`,method:"put"})}function Ve(s){return y({url:"/job/worker/"+s,method:"delete"})}const Fe=()=>({dialogWidth:"900px",dialogHeight:"70vh",labelWidth:"120px",column:[{label:"真实姓名",prop:"realName",search:!0,searchSpan:8,minWidth:120,rules:[{required:!0,message:"真实姓名不能为空",trigger:"blur"},{min:2,max:50,message:"真实姓名长度必须介于 2 和 50 之间",trigger:"blur"}],span:12},{label:"工作类型偏好",prop:"jobTypesPreferred",search:!0,searchSpan:6,minWidth:150,align:"center",type:"select",dicData:[{label:"全职",value:"全职"},{label:"兼职",value:"兼职"},{label:"临时工",value:"临时工"},{label:"小时工",value:"小时工"},{label:"实习",value:"实习"}],span:8,slot:!0,rules:[{required:!0,message:"工作类型偏好不能为空",trigger:"change"}]},{label:"工作类别",prop:"workCategories",search:!0,searchSpan:6,width:120,align:"center",type:"select",dicData:[{label:"服务员",value:"服务员"},{label:"保洁",value:"保洁"},{label:"搬运工",value:"搬运工"},{label:"销售",value:"销售"},{label:"客服",value:"客服"},{label:"配送员",value:"配送员"},{label:"厨师",value:"厨师"},{label:"司机",value:"司机"},{label:"保安",value:"保安"},{label:"其他",value:"其他"}],span:8,slot:!0,rules:[{required:!0,message:"工作类别不能为空",trigger:"change"}]},{label:"薪资类型偏好",prop:"salaryTypePreference",search:!0,searchSpan:6,width:100,align:"center",width:120,type:"select",dicData:[{label:"小时",value:"hourly"},{label:"日薪",value:"daily"},{label:"月薪",value:"monthly"},{label:"计件",value:"piece"}],span:8,rules:[{required:!0,message:"薪资类型偏好不能为空",trigger:"change"}]},{label:"学历水平",prop:"educationLevel",search:!0,searchSpan:6,width:80,align:"center",type:"select",dicData:[{label:"不限",value:"不限"},{label:"初中",value:"初中"},{label:"高中",value:"高中"},{label:"中专",value:"中专"},{label:"大专",value:"大专"},{label:"本科",value:"本科"}],span:8,rules:[{required:!0,message:"学历水平不能为空",trigger:"change"}]},{label:"当前所在地",prop:"currentLocation",search:!0,searchSpan:8,minWidth:120,span:8,rules:[{required:!0,message:"当前所在地不能为空",trigger:"blur"},{max:100,message:"当前所在地不能超过100个字符",trigger:"blur"}]},{label:"性别",prop:"gender",search:!0,searchSpan:6,width:60,align:"center",type:"select",dicData:[{label:"男",value:"male"},{label:"女",value:"female"}],span:8},{label:"年龄",prop:"age",search:!1,width:60,align:"center",type:"number",span:8,min:16,max:65,rules:[{type:"number",min:16,max:65,message:"年龄必须在16-65之间",trigger:"blur"}]},{label:"手机号",prop:"phone",search:!0,searchSpan:8,minWidth:120,span:8,rules:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},{label:"工作经验年数",prop:"workExperienceYears",search:!1,type:"number",width:120,span:8,min:0,max:50,rules:[{type:"number",min:0,max:50,message:"工作经验年数必须在0-50之间",trigger:"blur"}]},{label:"期望最低薪资",prop:"salaryExpectationMin",search:!1,type:"number",span:8,width:120,precision:0,min:0,rules:[{type:"number",min:0,message:"期望最低薪资不能小于0",trigger:"blur"}]},{label:"期望最高薪资",prop:"salaryExpectationMax",search:!1,type:"number",span:8,precision:0,min:0,width:120,rules:[{type:"number",min:0,message:"期望最高薪资不能小于0",trigger:"blur"}]},{label:"技能描述",prop:"skills",search:!1,slot:!0,minWidth:200,align:"left",span:24,minRows:3,maxRows:6,formslot:!0,showWordLimit:!0,maxlength:500,placeholder:"请描述您的技能和特长"},{label:"自我介绍",prop:"selfIntroduction",search:!1,type:"textarea",span:24,minRows:3,maxRows:6,showWordLimit:!0,maxlength:500,placeholder:"请简单介绍一下自己"},{label:"状态",prop:"status",search:!0,searchSpan:6,width:80,align:"center",type:"select",dicData:[{label:"活跃",value:"active"},{label:"不活跃",value:"inactive"}],span:12,slot:!0,addDisplay:!1,editDisplay:!1,value:"active"}]}),Ae={class:"worker-profile-container app-container"},Ee={class:"categories-container"},Me={key:0,class:"no-data"},qe={class:"job-types-container"},Je={key:0,class:"no-data"},Be={class:"skills-container"},Ue={key:0,class:"no-data"},Ye={class:"operation-btns"},He={key:1,class:"loading-placeholder"},Ge=be({name:"WorkerProfile"}),Ke=Object.assign(Ge,{setup(s){const{proxy:c}=ke(),D=u([]),L=u(!0),_=u([]),F=u(!0),B=u(!0),A=u(0),I=u(!0),E=u([]),M=u([]),j=u(!1),$=u(!1),z=u({dialogWidth:"800px",dialogHeight:"60vh"}),U=u(null),x=u(null),q=u([]),P=u({}),Y=ye({queryParams:{pageNum:1,pageSize:10,realName:void 0,nickname:void 0,phone:void 0,currentLocation:void 0,educationLevel:void 0,status:void 0,isVerified:void 0,createTime:void 0}}),{queryParams:f}=_e(Y);Ce(async()=>{await H(),g()});const H=async()=>{try{const e=Fe(c),t=await Te({baseOption:e,proxy:c}),{tableColumns:n,searchColumns:h,formFields:R,formOptions:b}=Oe(t);E.value=n,M.value=h,q.value=R,z.value={...z.value,...b},$.value=!0}catch(e){$.value=!1,console.error("初始化配置失败:",e)}};function g(){j.value=!0,L.value=!0;let e={...f.value};P.value.createTime&&Array.isArray(P.value.createTime)&&P.value.createTime.length===2&&(e=c.addDateRange(e,P.value.createTime)),De(e).then(t=>{j.value=!1,L.value=!1,D.value=t.rows,A.value=t.total,we(()=>{I.value=!1})})}const G=e=>{I.value=!0,P.value={...e};const{createTime:t,...n}=e||{};Object.assign(f.value,n),f.value.pageNum=1,g()},K=()=>{I.value=!0,f.value={pageNum:1,pageSize:10,realName:void 0,nickname:void 0,phone:void 0,currentLocation:void 0,educationLevel:void 0,status:void 0,isVerified:void 0,createTime:void 0},P.value={},g()},Q=e=>{I.value=!0,f.value.pageNum=e,g()},X=e=>{I.value=!0,f.value.pageSize=e,f.value.pageNum=1,g()};function Z(e){_.value=e.map(t=>t.workerId),F.value=e.length!=1,B.value=!e.length}const ee=e=>{var t;(t=x.value)==null||t.openDialog("view","查看零工信息",e)},J=e=>{const t=e.workerId;Le(t).then(n=>{var h;(h=x.value)==null||h.openDialog("edit","编辑零工信息",n.data)})},ae=()=>{var t;const e={status:"active"};(t=x.value)==null||t.openDialog("add","新增零工信息",e)},te=async e=>{var t,n;try{e.type==="add"?(await je(e.data),c.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await $e(e.data),c.$modal.msgSuccess("修改成功")),(t=x.value)==null||t.onSubmitSuccess(),g()}catch(h){(n=x.value)==null||n.onSubmitError(),console.error("提交失败:",h)}},re=()=>{};function ne(){ae()}function le(e){if(e)J(e);else{const t=_.value[0],n=D.value.find(h=>h.workerId===t);n&&J(n)}}function se(e){const t=e!=null&&e.workerId?[e.workerId]:_.value;c.$modal.confirm("是否确认验证选中的零工信息？").then(function(){return Ne(t[0])}).then(()=>{g(),c.$modal.msgSuccess("验证成功")}).catch(()=>{})}function oe(e){const t=e!=null&&e.workerId?[e.workerId]:_.value;c.$modal.confirm("是否确认激活选中的零工信息？").then(function(){return ze(t[0])}).then(()=>{g(),c.$modal.msgSuccess("激活成功")}).catch(()=>{})}function ie(e){const t=e!=null&&e.workerId?[e.workerId]:_.value;c.$modal.confirm("是否确认暂停选中的零工信息？").then(function(){return Re(t[0])}).then(()=>{g(),c.$modal.msgSuccess("暂停成功")}).catch(()=>{})}function ue(e){const t=e!=null&&e.workerId?[e.workerId]:_.value;c.$modal.confirm("是否确认删除选中的零工信息？").then(function(){return Ve(t)}).then(()=>{g(),c.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ce(){c.download("job/worker/export",{...f.value},`worker_profile_${new Date().getTime()}.xlsx`)}function de(e){return{博士:"danger",硕士:"warning",本科:"success",大专:"info",高中:"",中专:"",初中:"info",小学:"info"}[e]||""}function pe(e){return e>=90?"success-rate-high":e>=70?"success-rate-medium":e>=50?"success-rate-low":"success-rate-very-low"}function me(e){return{active:"success",inactive:"info",suspended:"warning",banned:"danger"}[e]||"info"}function fe(e){return{active:"活跃",inactive:"不活跃",suspended:"暂停",banned:"禁用"}[e]||"未知"}function S(e){if(!e)return[];try{return Array.isArray(e)?e:typeof e=="string"?JSON.parse(e):[]}catch(t){return console.warn("解析JSON数组数据失败:",t,e),[]}}return(e,t)=>{const n=T("el-button"),h=T("el-icon"),R=T("el-avatar"),b=T("el-tag"),ge=T("el-rate"),he=T("el-empty"),C=xe("hasPermi");return l(),k("div",Ae,[m($)?(l(),d(Ie,{key:0,columns:m(E),data:m(D),loading:m(j),showIndex:!0,searchColumns:m(M),showOperation:!0,operationLabel:"操作",operationWidth:"220",fixedOperation:!0,ref_key:"tableListRef",ref:U,onSearch:G,onReset:K,defaultPage:{pageSize:m(f).pageSize,currentPage:m(f).pageNum,total:m(A)},onCurrentChange:Q,onSizeChange:X,onSelectionChange:Z},{"menu-left":r(()=>[w((l(),d(n,{type:"primary",class:"custom-btn",onClick:ne},{default:r(()=>[o("新 增")]),_:1})),[[C,["job:worker:add"]]]),w((l(),d(n,{type:"warning",plain:"",class:"custom-btn",onClick:ce},{default:r(()=>[o("导 出")]),_:1})),[[C,["job:worker:export"]]])]),profilePhoto:r(({row:a})=>[p(R,{size:50,src:a.profilePhoto,alt:a.realName,fit:"cover"},{default:r(()=>[p(h,null,{default:r(()=>[p(m(Se))]),_:1})]),_:2},1032,["src","alt"])]),gender:r(({row:a})=>[p(b,{type:a.gender==="male"?"primary":"danger",size:"small"},{default:r(()=>[o(v(a.gender==="male"?"男":"女"),1)]),_:2},1032,["type"])]),educationLevel:r(({row:a})=>[p(b,{type:de(a.educationLevel),size:"small"},{default:r(()=>[o(v(a.educationLevel),1)]),_:2},1032,["type"])]),workExperience:r(({row:a})=>[o(v(a.workExperienceYears||0)+"年 ",1)]),rating:r(({row:a})=>[p(ge,{modelValue:a.ratingAverage,"onUpdate:modelValue":i=>a.ratingAverage=i,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}",max:5,size:"small"},null,8,["modelValue","onUpdate:modelValue"])]),completedJobs:r(({row:a})=>[o(v(a.completedJobs||0),1)]),successRate:r(({row:a})=>[W("span",{class:Pe(pe(a.successRate))},v((a.successRate||0).toFixed(1))+"% ",3)]),status:r(({row:a})=>[p(b,{type:me(a.status),size:"small"},{default:r(()=>[o(v(fe(a.status)),1)]),_:2},1032,["type"])]),isVerified:r(({row:a})=>[p(b,{type:a.isVerified?"success":"info",size:"small"},{default:r(()=>[o(v(a.isVerified?"已验证":"未验证"),1)]),_:2},1032,["type"])]),workCategories:r(({row:a})=>[W("div",Ee,[(l(!0),k(N,null,V(S(a.workCategories),i=>(l(),d(b,{key:i,size:"small",type:"success",class:"category-tag"},{default:r(()=>[o(v(i),1)]),_:2},1024))),128)),S(a.workCategories).length?O("",!0):(l(),k("span",Me,"暂无类别"))])]),jobTypesPreferred:r(({row:a})=>[W("div",qe,[(l(!0),k(N,null,V(S(a.jobTypesPreferred),i=>(l(),d(b,{key:i,size:"small",type:"warning",class:"job-type-tag"},{default:r(()=>[o(v(i),1)]),_:2},1024))),128)),S(a.jobTypesPreferred).length?O("",!0):(l(),k("span",Je,"暂无偏好"))])]),skills:r(({row:a})=>[W("div",Be,[(l(!0),k(N,null,V(S(a.skills),i=>(l(),d(b,{key:i,size:"small",type:"primary",class:"skill-tag"},{default:r(()=>[o(v(i),1)]),_:2},1024))),128)),S(a.skills).length?O("",!0):(l(),k("span",Ue,"暂无技能"))])]),menu:r(({row:a})=>[W("div",Ye,[p(n,{type:"primary",link:"",onClick:i=>ee(a)},{default:r(()=>[o("查看")]),_:2},1032,["onClick"]),w((l(),d(n,{type:"primary",link:"",onClick:i=>le(a)},{default:r(()=>[o("编辑")]),_:2},1032,["onClick"])),[[C,["job:worker:edit"]]]),a.isVerified?O("",!0):w((l(),d(n,{key:0,type:"success",link:"",onClick:i=>se(a)},{default:r(()=>[o("验证")]),_:2},1032,["onClick"])),[[C,["job:worker:verify"]]]),a.status==="active"?w((l(),d(n,{key:1,type:"warning",link:"",onClick:i=>ie(a)},{default:r(()=>[o("暂停")]),_:2},1032,["onClick"])),[[C,["job:worker:suspend"]]]):O("",!0),a.status!=="active"?w((l(),d(n,{key:2,type:"success",link:"",onClick:i=>oe(a)},{default:r(()=>[o("激活")]),_:2},1032,["onClick"])),[[C,["job:worker:activate"]]]):O("",!0),w((l(),d(n,{type:"danger",link:"",onClick:i=>ue(a)},{default:r(()=>[o("删除")]),_:2},1032,["onClick"])),[[C,["job:worker:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(l(),k("div",He,[p(he,{description:"正在加载表格配置..."})])),p(We,{ref_key:"workerProfileFormDialogRef",ref:x,formFields:m(q),formOption:m(z),onSubmit:te,onCancel:re},null,8,["formFields","formOption"])])}}}),aa=ve(Ke,[["__scopeId","data-v-5576d43c"]]);export{aa as default};
