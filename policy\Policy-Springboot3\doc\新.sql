
-- ----------------------------
-- Table structure for online_training_institution_recruitment
-- ----------------------------
DROP TABLE IF EXISTS `online_training_institution_recruitment`;
CREATE TABLE `online_training_institution_recruitment`  (
  `recruitment_id` bigint(0) NOT NULL AUTO_INCREMENT COMMENT '招募ID',
  `recruitment_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '招募标题',
  `recruitment_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '招募描述',
  `training_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '培训类别（IT技能/管理培训/职业技能等）',
  `training_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训级别（初级/中级/高级）',
  `training_duration` int(0) NULL DEFAULT NULL COMMENT '培训时长(小时)',
  `max_participants` int(0) NULL DEFAULT NULL COMMENT '最大参与人数',
  `training_fee_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '培训费用最低价',
  `training_fee_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '培训费用最高价',
  `training_location` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '培训地点',
  `online_support` tinyint(1) NULL DEFAULT 0 COMMENT '是否支持线上培训（0否 1是）',
  `offline_support` tinyint(1) NULL DEFAULT 0 COMMENT '是否支持线下培训（0否 1是）',
  `qualification_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '机构资质要求',
  `experience_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '经验要求',
  `teacher_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '师资要求',
  `facility_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '设施设备要求',
  `contact_person` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `application_start_date` datetime(0) NULL DEFAULT NULL COMMENT '申请开始时间',
  `application_end_date` datetime(0) NULL DEFAULT NULL COMMENT '申请截止时间',
  `expected_start_date` datetime(0) NULL DEFAULT NULL COMMENT '预期培训开始时间',
  `expected_end_date` datetime(0) NULL DEFAULT NULL COMMENT '预期培训结束时间',
  `recruitment_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '招募状态（0草稿 1发布 2进行中 3已完成 4已取消）',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `is_urgent` tinyint(1) NULL DEFAULT 0 COMMENT '是否紧急（0否 1是）',
  `view_count` int(0) NULL DEFAULT 0 COMMENT '浏览次数',
  `application_count` int(0) NULL DEFAULT 0 COMMENT '申请次数',
  `selected_count` int(0) NULL DEFAULT 0 COMMENT '已选中机构数量',
  `budget_amount` decimal(15, 2) NULL DEFAULT NULL COMMENT '预算金额',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '付款方式（一次性/分期等）',
  `evaluation_criteria` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '评选标准',
  `additional_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '其他要求',
  `attachment_files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '附件文件（JSON格式存储）',
  `publisher_user_id` bigint(0) NOT NULL COMMENT '发布者用户ID',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_id` bigint(0) NULL DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `update_id` bigint(0) NULL DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`recruitment_id`) USING BTREE,
  INDEX `idx_training_category`(`training_category`) USING BTREE,
  INDEX `idx_training_level`(`training_level`) USING BTREE,
  INDEX `idx_recruitment_status`(`recruitment_status`) USING BTREE,
  INDEX `idx_application_end_date`(`application_end_date`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_is_urgent`(`is_urgent`) USING BTREE,
  INDEX `idx_publisher_user_id`(`publisher_user_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_del_flag`(`del_flag`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '线上招募培训机构发布表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of online_training_institution_recruitment
-- ----------------------------
INSERT INTO `online_training_institution_recruitment` VALUES (1, 'Java企业级开发培训机构招募', '面向企业员工的Java高级开发培训，需要有经验的培训机构承接此项目', 'IT技能', '高级', 120, 50, 5000.00, 8000.00, '西宁市市南区或线上', 1, 1, '具备教育培训资质，有相关技术培训经验', '至少3年Java企业培训经验，成功案例不少于10个', '拥有资深Java讲师，具备实际项目开发经验', '配备现代化机房或线上教学平台', '张经理', '13800138001', '<EMAIL>', '2025-07-25 00:00:00', '2025-08-10 23:59:59', '2025-08-15 09:00:00', '2025-08-30 18:00:00', '1', 1, 0, 156, 8, 0, 300000.00, '分期付款', '综合考虑师资力量、培训经验、课程设计、价格等因素', '需要提供详细的培训计划和师资介绍', NULL, 1, '0', 1, '2025-07-25 15:30:00', NULL, '2025-07-25 15:30:00', '企业内训项目');

INSERT INTO `online_training_institution_recruitment` VALUES (2, 'Python数据分析培训机构征集', '为政府部门工作人员提供Python数据分析技能培训，寻找专业培训机构', 'IT技能', '中级', 80, 30, 3000.00, 5000.00, '西宁市崂山区', 0, 1, '具备正规教育培训资质，有数据分析培训经验', '至少2年Python数据分析培训经验', '拥有数据分析专业讲师，熟悉政府业务场景', '配备数据分析软件和高性能计算设备', '李主任', '13800138002', '<EMAIL>', '2025-07-26 00:00:00', '2025-08-05 23:59:59', '2025-08-10 09:00:00', '2025-08-20 18:00:00', '1', 0, 1, 89, 5, 0, 150000.00, '一次性付款', '重点考虑培训效果和学员满意度', '需要针对政府工作特点定制课程内容', NULL, 1, '0', 1, '2025-07-25 15:35:00', NULL, '2025-07-25 15:35:00', '政府培训项目');

INSERT INTO `online_training_institution_recruitment` VALUES (3, '企业管理培训机构招标', '为中小企业管理人员提供管理技能提升培训，公开招募优秀培训机构', '管理培训', '中级', 60, 40, 2000.00, 4000.00, '西宁市各区域', 1, 1, '具备企业管理培训资质和丰富经验', '至少5年企业管理培训经验，服务过100家以上企业', '拥有资深管理咨询师和培训师', '可提供多种培训形式和场地选择', '王总', '13800138003', '<EMAIL>', '2025-07-27 00:00:00', '2025-08-12 23:59:59', '2025-08-18 09:00:00', '2025-09-10 18:00:00', '1', 1, 0, 234, 12, 0, 500000.00, '按阶段付款', '综合评估培训方案、师资水平、成功案例、价格竞争力', '需要提供个性化的培训解决方案', NULL, 1, '0', 1, '2025-07-25 15:40:00', NULL, '2025-07-25 15:40:00', '中小企业培训项目');


