import{l as Y}from"./order-ZnAGpiqD.js";import{g as Z,c as ee}from"./application-CqX6Rd9f.js";import{T as ae}from"./index-BWWetMd6.js";import{_ as te,d as ne,r as c,D as ie,I as le,e as oe,J as R,f as _,c as C,o as d,l as w,i as o,k as a,h as y,m as z,p as i,t as l,j as B,x as re,y as se,E as pe}from"./index-DP10CBaW.js";const ce=h=>(re("data-v-29e76231"),h=h(),se(),h),ue={class:"training-application-container app-container"},de=ce(()=>y("div",{class:"page-header"},[y("h2",{class:"page-title"},"报名记录"),y("p",{class:"page-description"},"查看已提交的个人报名记录和审核状态")],-1)),ge={class:"price"},fe={key:1},ve={class:"operation-btns"},me={key:1,class:"loading-placeholder"},_e={key:0,class:"application-detail"},ye={__name:"signup-record",setup(h){const{proxy:E}=ne(),I=c(!1),b=c([]),A=c(!1),r=c(null),k=c(0),M=c([]),D=c([]),x=c(!1),L=c(!1),O=c(null),v=c({}),F=ie({queryParams:{pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"}}),{queryParams:u}=le(F);oe(async()=>{await W(),g()});const W=async()=>{try{M.value=[{prop:"orderTitle",label:"培训标题",minWidth:200,showOverflowTooltip:!0},{prop:"trainingType",label:"培训类型",width:120,tableSlot:!0},{prop:"trainingLevel",label:"培训级别",width:120},{prop:"trainingDuration",label:"培训时长",width:120},{prop:"trainingFee",label:"培训费用",width:120,tableSlot:!0},{prop:"trainingTime",label:"培训时间",width:200,tableSlot:!0},{prop:"trainingAddress",label:"培训地址",minWidth:150,showOverflowTooltip:!0},{prop:"participants",label:"报名人数",width:120,tableSlot:!0},{prop:"applicationStatus",label:"申请状态",width:120,tableSlot:!0},{prop:"applicationTime",label:"申请时间",width:150,tableSlot:!0}],D.value=[{prop:"orderTitle",label:"培训标题",type:"input"},{prop:"trainingType",label:"培训类型",type:"input"},{prop:"trainingLevel",label:"培训级别",type:"input"}],L.value=!0}catch(e){L.value=!1,console.error("初始化配置失败:",e)}},g=async()=>{x.value=!0,I.value=!0;try{const p=(await Z()).data||[];if(p.length===0){b.value=[],k.value=0;return}let s={...u.value};v.value.createTime&&Array.isArray(v.value.createTime)&&v.value.createTime.length===2&&(s=E.addDateRange(s,v.value.createTime));const N=(await Y(s)).rows||[];b.value=p.map(n=>{const f=N.find(m=>m.orderId===n.orderId);return f?{...f,userApplication:n,applicationStatus:n.applicationStatus,canApply:n.applicationStatus==="2"||n.applicationStatus==="3"}:null}).filter(n=>n!==null).filter(n=>{var f,m,t;return!(s.orderTitle&&!((f=n.orderTitle)!=null&&f.includes(s.orderTitle))||s.trainingType&&!((m=n.trainingType)!=null&&m.includes(s.trainingType))||s.trainingLevel&&!((t=n.trainingLevel)!=null&&t.includes(s.trainingLevel)))}),k.value=b.value.length}catch(e){R.error("获取申请记录失败"),console.error(e)}finally{x.value=!1,I.value=!1}},j=e=>{v.value={...e};const{createTime:p,...s}=e||{};Object.assign(u.value,s),u.value.pageNum=1,g()},q=()=>{u.value={pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"},v.value={},g()},$=e=>{u.value.pageNum=e,g()},G=e=>{u.value.pageSize=e,u.value.pageNum=1,g()},J=()=>{},U=()=>{g()},H=e=>{r.value=e,A.value=!0},K=async e=>{try{await pe.confirm("确认要取消申请吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await ee(e.applicationId),R.success("取消申请成功"),await g()}catch(p){p!=="cancel"&&R.error(p.msg||"取消申请失败")}},Q=e=>({技能培训:"primary",管理培训:"success",安全培训:"warning",专业培训:"info"})[e]||"default",P=e=>!e&&e!=="0"?"info":{0:"warning",1:"success",2:"danger",3:"info"}[String(e)]||"info",V=e=>!e&&e!=="0"?"未知":{0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"}[String(e)]||"未知",T=e=>e?new Date(e).toLocaleDateString("zh-CN"):"--";return(e,p)=>{const s=_("el-button"),S=_("el-tag"),N=_("el-empty"),n=_("el-descriptions-item"),f=_("el-descriptions"),m=_("el-dialog");return d(),C("div",ue,[de,L.value?(d(),w(ae,{key:0,columns:M.value,data:b.value,loading:x.value,showIndex:!0,searchColumns:D.value,showOperation:!0,operationLabel:"操作",operationWidth:"300",fixedOperation:!0,ref_key:"tableListRef",ref:O,onSearch:j,onReset:q,defaultPage:{pageSize:B(u).pageSize,currentPage:B(u).pageNum,total:k.value},onCurrentChange:$,onSizeChange:G,onSelectionChange:J},{"menu-left":a(()=>[o(s,{type:"primary",class:"custom-btn",onClick:U},{default:a(()=>[i("刷新")]),_:1})]),trainingType:a(({row:t})=>[o(S,{type:Q(t.trainingType),size:"small"},{default:a(()=>[i(l(t.trainingType),1)]),_:2},1032,["type"])]),trainingFee:a(({row:t})=>[y("span",ge,l(t.trainingFee?"￥"+t.trainingFee:"免费"),1)]),trainingTime:a(({row:t})=>[i(l(T(t.startDate))+" 至 "+l(T(t.endDate)),1)]),participants:a(({row:t})=>[i(l(t.currentParticipants||0)+"/"+l(t.maxParticipants||0)+"人 ",1)]),applicationStatus:a(({row:t})=>[t.userApplication&&t.userApplication.applicationStatus?(d(),w(S,{key:0,type:P(t.userApplication.applicationStatus),size:"small"},{default:a(()=>[i(l(V(t.userApplication.applicationStatus)),1)]),_:2},1032,["type"])):(d(),C("span",fe,"未申请"))]),applicationTime:a(({row:t})=>[i(l(t.userApplication?T(t.userApplication.applicationTime):"--"),1)]),menu:a(({row:t})=>[y("div",ve,[o(s,{type:"success",link:"",onClick:X=>H(t.userApplication)},{default:a(()=>[i(" 查看详情 ")]),_:2},1032,["onClick"]),t.userApplication.applicationStatus==="0"?(d(),w(s,{key:0,type:"warning",link:"",onClick:X=>K(t.userApplication)},{default:a(()=>[i(" 取消申请 ")]),_:2},1032,["onClick"])):z("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(d(),C("div",me,[o(N,{description:"正在加载表格配置..."})])),o(m,{modelValue:A.value,"onUpdate:modelValue":p[0]||(p[0]=t=>A.value=t),title:"申请详情",width:"600px","append-to-body":""},{default:a(()=>[r.value?(d(),C("div",_e,[o(f,{column:2,border:""},{default:a(()=>[o(n,{label:"申请人姓名"},{default:a(()=>[i(l(r.value.applicantName),1)]),_:1}),o(n,{label:"联系电话"},{default:a(()=>[i(l(r.value.applicantPhone),1)]),_:1}),o(n,{label:"邮箱地址"},{default:a(()=>[i(l(r.value.applicantEmail),1)]),_:1}),o(n,{label:"性别"},{default:a(()=>[i(l(r.value.applicantGender),1)]),_:1}),o(n,{label:"年龄"},{default:a(()=>[i(l(r.value.applicantAge),1)]),_:1}),o(n,{label:"学历"},{default:a(()=>[i(l(r.value.applicantEducation),1)]),_:1}),o(n,{label:"身份证号"},{default:a(()=>[i(l(r.value.applicantIdCard),1)]),_:1}),o(n,{label:"联系地址"},{default:a(()=>[i(l(r.value.applicantAddress),1)]),_:1}),o(n,{label:"申请状态"},{default:a(()=>[o(S,{type:P(r.value.applicationStatus)},{default:a(()=>[i(l(V(r.value.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),o(n,{label:"申请时间"},{default:a(()=>[i(l(T(r.value.applicationTime)),1)]),_:1}),o(n,{label:"工作经验",span:2},{default:a(()=>[i(l(r.value.applicantExperience||"--"),1)]),_:1}),o(n,{label:"申请备注",span:2},{default:a(()=>[i(l(r.value.applicationNote||"--"),1)]),_:1}),r.value.reviewComment?(d(),w(n,{key:0,label:"审核意见",span:2},{default:a(()=>[i(l(r.value.reviewComment),1)]),_:1})):z("",!0)]),_:1})])):z("",!0)]),_:1},8,["modelValue"])])}}},Ce=te(ye,[["__scopeId","data-v-29e76231"]]);export{Ce as default};
