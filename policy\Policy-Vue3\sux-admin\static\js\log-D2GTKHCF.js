import{g as Q}from"./job-DYlEC7Ga.js";import{Y as S,C as U,a as X,u as Z,d as ee,r as a,e as oe,f as I,K as ae,c as R,o as r,l as g,i as D,k as c,h as te,G as y,p as h}from"./index-DP10CBaW.js";import{g as ne,e as le}from"./columnUtils-DYlA-XL_.js";import{T as se}from"./index-BWWetMd6.js";import re from"./JobLogFormDialog-BgoMIhs3.js";import"./index-B-A7bGAb.js";function ce(u){const{sys_common_status:C,sys_job_group:n}=u.useDict("sys_common_status","sys_job_group");return{column:[{label:"日志编号",prop:"jobLogId",width:100,align:"center",sortable:!0},{label:"任务名称",prop:"jobName",minWidth:120,align:"center",showOverflowTooltip:!0,search:!0,searchType:"input",searchPlaceholder:"请输入任务名称"},{label:"任务组名",prop:"jobGroup",width:120,align:"center",showOverflowTooltip:!0,search:!0,searchType:"select",searchPlaceholder:"请选择任务组名",dicData:n,slot:!0},{label:"调用目标",prop:"invokeTarget",minWidth:150,align:"center",showOverflowTooltip:!0},{label:"日志信息",prop:"jobMessage",minWidth:150,align:"center",showOverflowTooltip:!0},{label:"执行状态",prop:"status",width:100,align:"center",search:!0,searchType:"select",searchPlaceholder:"请选择执行状态",dicData:C,slot:!0},{label:"执行时间",prop:"createTime",width:180,align:"center",type:"datetime",format:"YYYY-MM-DD HH:mm:ss",search:!0,searchType:"daterange",searchPlaceholder:"请选择执行时间"}]}}function ue(u){return S({url:"/monitor/jobLog/list",method:"get",params:u})}function ie(u){return S({url:"/monitor/jobLog/"+u,method:"delete"})}function me(){return S({url:"/monitor/jobLog/clean",method:"delete"})}const de={class:"job-log-container app-container"},pe={key:1,class:"loading-placeholder"},ge=U({name:"JobLog"}),Le=Object.assign(ge,{setup(u){X();const C=Z(),{proxy:n}=ee(),{sys_common_status:he,sys_job_group:be}=n.useDict("sys_common_status","sys_job_group"),j=a([]),x=a([]),b=a(!1),w=a(!1),L=a({dialogWidth:"800px",dialogHeight:"70vh"}),i=a(null),P=a(null),l=a(1),v=a(10),f=a(0),p=a({}),_=a([]),T=a([]),O=a(!0),F=a([]);oe(()=>{_.value=[],J()});const J=async()=>{try{const e=ce(n),t=await ne({baseOption:e,proxy:n}),{tableColumns:o,searchColumns:m,formFields:d,formOptions:k}=le(t);j.value=o,x.value=m,F.value=d,L.value={...L.value,...k},w.value=!0,await $(),s()}catch(e){w.value=!1,console.error("初始化配置失败:",e)}},$=async()=>{var t;const e=(t=C.params)==null?void 0:t.jobId;if(e&&e!=="0")try{const o=await Q(e);o.data&&(p.value={jobName:o.data.jobName,jobGroup:o.data.jobGroup})}catch(o){console.error("获取任务信息失败:",o)}},s=()=>{b.value=!0;const e={pageNum:l.value,pageSize:v.value,...p.value};ue(e).then(t=>{const o=(t.rows||[]).filter(m=>m&&m.jobLogId);_.value=o,f.value=t.total||0,z(),b.value=!1}).catch(t=>{console.error("加载任务日志列表失败:",t),_.value=[],f.value=0,b.value=!1})},z=()=>{i.value&&i.value.page&&(i.value.page.total=f.value,i.value.page.currentPage=l.value,i.value.page.pageSize=v.value)},N=e=>{p.value=e,l.value=1,s()},W=()=>{p.value={},l.value=1,s()},Y=e=>{l.value=e,s()},B=e=>{v.value=e,l.value=1,s()},G=e=>{T.value=e,O.value=!e.length},M=e=>{P.value.openDialog("view","调度日志详细",e)},V=()=>{if(!T.value.length){n.$modal.msgWarning("请选择要删除的数据");return}const e=T.value.map(t=>t.jobLogId);n.$modal.confirm('是否确认删除调度日志编号为"'+e.join(",")+'"的数据项?').then(function(){return ie(e)}).then(()=>{s(),n.$modal.msgSuccess("删除成功")}).catch(()=>{})},H=()=>{n.$modal.confirm("是否确认清空所有调度日志数据项?").then(function(){return me()}).then(()=>{s(),n.$modal.msgSuccess("清空成功")}).catch(()=>{})},q=()=>{n.download("monitor/jobLog/export",{...p.value},`job_log_${new Date().getTime()}.xlsx`)},E=()=>{const e={path:"/system/job"};n.$tab.closeOpenPage(e)},K=({type:e,data:t})=>{},A=()=>{};return(e,t)=>{const o=I("el-button"),m=I("el-empty"),d=ae("hasPermi");return r(),R("div",de,[w.value&&j.value.length>0?(r(),g(se,{key:0,columns:j.value,data:_.value,loading:b.value,showIndex:!0,searchColumns:x.value,showOperation:!0,showSelection:!0,operationLabel:"操作",operationWidth:"120",fixedOperation:!0,ref_key:"tableListRef",ref:i,onSearch:N,onReset:W,onSelectionChange:G,defaultPage:{pageSize:v.value,currentPage:l.value,total:f.value},onCurrentChange:Y,onSizeChange:B},{"menu-left":c(()=>[y((r(),g(o,{type:"danger",class:"custom-btn",disabled:O.value,onClick:V},{default:c(()=>[h(" 删 除 ")]),_:1},8,["disabled"])),[[d,["monitor:job:remove"]]]),y((r(),g(o,{type:"danger",plain:"",class:"custom-btn",onClick:H},{default:c(()=>[h(" 清 空 ")]),_:1})),[[d,["monitor:job:remove"]]]),y((r(),g(o,{type:"warning",class:"custom-btn",onClick:q},{default:c(()=>[h(" 导 出 ")]),_:1})),[[d,["monitor:job:export"]]]),D(o,{type:"info",plain:"",class:"custom-btn",onClick:E},{default:c(()=>[h(" 关 闭 ")]),_:1})]),menu:c(({row:k})=>[te("div",null,[y((r(),g(o,{type:"primary",link:"",onClick:ve=>M(k)},{default:c(()=>[h(" 详细 ")]),_:2},1032,["onClick"])),[[d,["monitor:job:query"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(r(),R("div",pe,[D(m,{description:"正在加载表格配置..."})])),D(re,{ref_key:"jobLogFormDialogRef",ref:P,formFields:F.value,formOption:L.value,onSubmit:K,onCancel:A},null,8,["formFields","formOption"])])}}});export{Le as default};
