import{_ as J,r as p,D as K,f as o,l as k,o as D,k as e,h as c,i as a,m as O,p as i,t as r,j as w,a7 as Q,ac as W,P as X,x as Y,y as Z}from"./index-DP10CBaW.js";const h=v=>(Y("data-v-f231ed0d"),v=v(),Z(),v),$={class:"review-container"},ee={class:"application-info"},ae=h(()=>c("h4",null,"申请信息",-1)),te={class:"review-form"},oe={class:"radio-content"},le=h(()=>c("span",{class:"radio-text"},"通过",-1)),se={class:"radio-content"},ne=h(()=>c("span",{class:"radio-text"},"拒绝",-1)),ie={class:"dialog-footer"},re={__name:"ReviewDialog",emits:["submit"],setup(v,{expose:N,emit:T}){const F=T,d=p(!1),S=p(""),f=p(""),t=p({}),g=p(!1),_=p(null),u=K({approvalStatus:"",approvalComment:"",approvalFiles:[]}),I={approvalStatus:[{required:!0,message:"请选择审核结果",trigger:"change"}],approvalComment:[{required:!0,message:"请填写审核意见",trigger:"blur"},{max:1e3,message:"审核意见不能超过1000个字符",trigger:"blur"}]},R=(l,s,n)=>{f.value=l,S.value=s,t.value={...n},d.value=!0,b()},b=()=>{u.approvalStatus="",u.approvalComment="",u.approvalFiles=[],_.value&&_.value.resetFields()},U=async()=>{if(_.value)try{await _.value.validate(),g.value=!0;const l={type:f.value,data:{...u},applicationData:t.value};F("submit",l)}catch(l){console.error("表单验证失败:",l)}},M=()=>{g.value=!1,d.value=!1,b()},B=()=>{g.value=!1},q=()=>{d.value=!1,b()},P=l=>({0:"warning",1:"success",2:"danger",3:"warning",4:"success",5:"danger",6:"info"})[l]||"info",j=l=>({0:"待初审",1:"初审通过",2:"初审拒绝",3:"待终审",4:"终审通过",5:"终审拒绝",6:"已完成"})[l]||"未知状态";return N({openDialog:R,onSubmitSuccess:M,onSubmitError:B}),(l,s)=>{const n=o("el-descriptions-item"),z=o("el-tag"),E=o("el-descriptions"),y=o("el-icon"),x=o("el-radio"),L=o("el-radio-group"),V=o("el-form-item"),A=o("el-input"),G=o("el-form"),C=o("el-button"),H=o("el-dialog");return D(),k(H,{modelValue:d.value,"onUpdate:modelValue":s[2]||(s[2]=m=>d.value=m),title:S.value,width:"1000px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:e(()=>[c("div",ie,[a(C,{onClick:q},{default:e(()=>[i("取消")]),_:1}),a(C,{type:"primary",loading:g.value,onClick:U},{default:e(()=>[i(" 确认"+r(f.value==="first"?"初审":"终审"),1)]),_:1},8,["loading"])])]),default:e(()=>[c("div",$,[c("div",ee,[ae,a(E,{column:2,border:""},{default:e(()=>[a(n,{label:"申请ID"},{default:e(()=>[i(r(t.value.applicationId),1)]),_:1}),a(n,{label:"政策名称"},{default:e(()=>[i(r(t.value.policyName),1)]),_:1}),a(n,{label:"申请人姓名"},{default:e(()=>[i(r(t.value.applicantName||t.value.applicantUserName),1)]),_:1}),a(n,{label:"联系电话"},{default:e(()=>[i(r(t.value.applicantPhone||"未填写"),1)]),_:1}),a(n,{label:"申请人账号"},{default:e(()=>[i(r(t.value.applicantUserName),1)]),_:1}),a(n,{label:"提交时间"},{default:e(()=>[i(r(w(Q)(t.value.submitTime)),1)]),_:1}),a(n,{label:"当前状态",span:2},{default:e(()=>[a(z,{type:P(t.value.applicationStatus),size:"small"},{default:e(()=>[i(r(j(t.value.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),t.value.remark?(D(),k(n,{key:0,label:"备注信息",span:2},{default:e(()=>[i(r(t.value.remark),1)]),_:1})):O("",!0)]),_:1})]),c("div",te,[c("h4",null,r(f.value==="first"?"初审":"终审")+"操作",1),a(G,{ref_key:"reviewFormRef",ref:_,model:u,rules:I,"label-width":"100px"},{default:e(()=>[a(V,{label:"审核结果",prop:"approvalStatus"},{default:e(()=>[a(L,{modelValue:u.approvalStatus,"onUpdate:modelValue":s[0]||(s[0]=m=>u.approvalStatus=m),class:"review-radio-group"},{default:e(()=>[a(x,{value:"1",class:"review-radio-item"},{default:e(()=>[c("span",oe,[a(y,{class:"radio-icon success-icon"},{default:e(()=>[a(w(W))]),_:1}),le])]),_:1}),a(x,{value:"2",class:"review-radio-item"},{default:e(()=>[c("span",se,[a(y,{class:"radio-icon danger-icon"},{default:e(()=>[a(w(X))]),_:1}),ne])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(V,{label:"审核意见",prop:"approvalComment"},{default:e(()=>[a(A,{modelValue:u.approvalComment,"onUpdate:modelValue":s[1]||(s[1]=m=>u.approvalComment=m),type:"textarea",rows:4,placeholder:"请填写审核意见","show-word-limit":"",maxlength:"1000"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])])]),_:1},8,["modelValue","title"])}}},ue=J(re,[["__scopeId","data-v-f231ed0d"]]);export{ue as default};
