import{_ as Y,C as j,a8 as q,A as k,d as E,r as u,f,l as g,o as h,k as d,h as C,Z,N as G,i as b,m as J,p as _,t as O,x as K,y as M}from"./index-DP10CBaW.js";import{F as P}from"./index-BylsdGrt.js";import{V as Q}from"./index-B-A7bGAb.js";const $=s=>(K("data-v-1c035338"),s=s(),M(),s),ee=$(()=>C("span",{style:{padding:"0 8px"}},"¥",-1)),le={class:"dialog-footer"},oe=j({name:"TemplateFormDialog"}),ae=Object.assign(oe,{props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({dialogWidth:"800px",dialogHeight:"70vh"})}},emits:["submit","cancel"],setup(s,{expose:T,emit:W}){q(o=>({"57bef52b":p.value.maxHeight,"9c21f52c":p.value.minHeight||"auto","60653a17":p.value.overflowY,"3423ecde":p.value.padding}));const{proxy:te}=E(),c=s,F=W,v=u(!1),i=u("add"),D=u("新增模板"),y=u(null),t=u({}),n=u(!1),r=u(!1),m=u(!1),S=k(()=>c.formFields.length?i.value==="add"?c.formFields.filter(o=>o.addDisplay!==!1):i.value==="edit"?c.formFields.filter(o=>o.editDisplay!==!1):c.formFields.filter(o=>o.viewDisplay!==!1):[]),p=k(()=>{const o={overflow:"visible",padding:"20px 10px",overflowX:"hidden"};return m.value?{...o,maxHeight:"calc(100vh - 180px)",overflowY:"auto",overflowX:"hidden"}:{...o,maxHeight:c.formOption.dialogHeight||"70vh",overflowY:"auto",overflowX:"hidden",minHeight:"auto"}}),H=()=>{m.value=!m.value},L=(o,a,e={})=>{i.value=o,D.value=a,t.value={id:e.id||void 0,name:e.name||"",code:e.code||"",category:e.category||"tech",type:e.type||"1",description:e.description||"",price:e.price||0,quantity:e.quantity||0,effectiveDate:e.effectiveDate||"",isRecommend:e.isRecommend||"0",tags:e.tags||[],status:e.status||"0",createBy:e.createBy||"",createTime:e.createTime||"",updateTime:e.updateTime||"",...e},v.value=!0},w=()=>{v.value=!1},B=(o,a)=>{},I=async()=>{if(!(n.value||r.value)&&y.value)try{n.value=!0,r.value=!0,await y.value.validate(),F("submit",{type:i.value,data:t.value})}catch{n.value=!1,r.value=!1}},N=()=>{F("cancel"),w()},U=()=>{n.value=!1,r.value=!1},z=()=>{t.value={},n.value=!1,r.value=!1};return T({openDialog:L,closeDialog:w,onSubmitSuccess:()=>{n.value=!1,r.value=!1,w()},onSubmitError:()=>{n.value=!1,r.value=!1}}),(o,a)=>{const e=f("el-input"),R=f("el-input-number"),x=f("el-button"),A=f("el-dialog");return h(),g(A,{modelValue:v.value,"onUpdate:modelValue":a[4]||(a[4]=l=>v.value=l),title:D.value,width:s.formOption.dialogWidth,"destroy-on-close":"","close-on-click-modal":!1,fullscreen:m.value,onClosed:z,onOpen:U,class:"custom-dialog"},{footer:d(()=>[C("span",le,[b(x,{class:"common-btn",onClick:H},{default:d(()=>[_(O(m.value?"退 出 全 屏":"全 屏 显 示"),1)]),_:1}),b(x,{class:"common-btn",onClick:N},{default:d(()=>[_(O(i.value==="view"?"关 闭":"取 消"),1)]),_:1}),i.value!=="view"?(h(),g(x,{key:0,type:"primary",class:"common-btn",onClick:I,loading:n.value,disabled:r.value},{default:d(()=>[_(" 确 认 ")]),_:1},8,["loading","disabled"])):J("",!0)])]),default:d(()=>[C("div",{class:G(["dialog-content",{"view-mode":i.value==="view"}]),style:Z(p.value)},[i.value!=="view"?(h(),g(P,{key:0,ref_key:"formListRef",ref:y,modelValue:t.value,"onUpdate:modelValue":a[2]||(a[2]=l=>t.value=l),fields:S.value,"is-view":i.value==="view",showActions:!1,labelWidth:s.formOption.labelWidth,inline:!1,onFieldChange:B},{description:d(({field:l,value:X})=>[b(e,{type:"textarea",modelValue:t.value.description,"onUpdate:modelValue":a[0]||(a[0]=V=>t.value.description=V),placeholder:l.placeholder||"请输入描述信息",rows:l.rows||4,maxlength:l.maxlength||500,"show-word-limit":l.showWordLimit||!0,style:{width:"100%","max-width":"100%","box-sizing":"border-box"}},null,8,["modelValue","placeholder","rows","maxlength","show-word-limit"])]),price:d(({field:l,value:X})=>[b(R,{modelValue:t.value.price,"onUpdate:modelValue":a[1]||(a[1]=V=>t.value.price=V),placeholder:l.placeholder||"请输入价格",precision:l.precision||2,min:l.min||0,max:l.max||999999.99,step:l.step||.01,"controls-position":"right",style:{width:"100%","max-width":"100%","box-sizing":"border-box"}},{prepend:d(()=>[ee]),_:2},1032,["modelValue","placeholder","precision","min","max","step"])]),_:1},8,["modelValue","fields","is-view","labelWidth"])):(h(),g(Q,{key:1,modelValue:t.value,"onUpdate:modelValue":a[3]||(a[3]=l=>t.value=l),fields:S.value,labelWidth:s.formOption.labelWidth||"120px"},null,8,["modelValue","fields","labelWidth"]))],6)]),_:1},8,["modelValue","title","width","fullscreen"])}}}),de=Y(ae,[["__scopeId","data-v-1c035338"]]);export{de as default};
