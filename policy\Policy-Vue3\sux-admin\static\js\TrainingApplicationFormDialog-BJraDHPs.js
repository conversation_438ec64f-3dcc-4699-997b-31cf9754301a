import{l as Q}from"./order-ZnAGpiqD.js";import{_ as Y,d as Z,r as m,w as ee,f as s,K as le,l as b,o as f,k as a,G as ae,j as l,i as e,m as x,c as te,L as ne,M as oe,p as v,t as V,h as ie,F as de,R as pe}from"./index-DP10CBaW.js";const ue={class:"dialog-footer"},re={__name:"TrainingApplicationFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(A,{expose:S,emit:U}){const{proxy:D}=Z(),O=A,E=U,g=m(!1),C=m(""),d=m("add"),h=m("800px"),F=m(!1),y=m(!1),w=m(null),I=m([]),t=m({applicationId:null,orderId:null,userId:null,applicantName:"",applicantPhone:"",applicantEmail:"",applicantIdCard:"",applicantGender:"",applicantAge:null,applicantEducation:"",applicantExperience:"",applicantAddress:"",applicationStatus:"0",applicationNote:"",reviewer:"",reviewComment:"",applicationTime:null,reviewTime:null}),G=m({orderId:[{required:!0,message:"请选择培训订单",trigger:"change"}],applicantName:[{required:!0,message:"请输入报名人姓名",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],applicantPhone:[{required:!0,message:"请输入报名人手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],applicantEmail:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],applicantIdCard:[{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号",trigger:"blur"}]});ee(()=>O.formOption,i=>{i.dialogWidth&&(h.value=i.dialogWidth)},{immediate:!0});const P=(i,n,r={})=>{d.value=i,C.value=n,g.value=!0,M(),(i==="edit"||i==="view")&&Object.assign(t.value,r),R()},M=()=>{t.value={applicationId:null,orderId:null,userId:null,applicantName:"",applicantPhone:"",applicantEmail:"",applicantIdCard:"",applicantGender:"",applicantAge:null,applicantEducation:"",applicantExperience:"",applicantAddress:"",applicationStatus:"0",applicationNote:"",reviewer:"",reviewComment:"",applicationTime:null,reviewTime:null},pe(()=>{w.value&&w.value.clearValidate()})},R=()=>{Q({orderStatus:"1"}).then(i=>{I.value=i.rows||[]}).catch(()=>{I.value=[]})},B=()=>{w.value&&w.value.validate(i=>{if(i){y.value=!0;const n={...t.value};E("submit",{type:d.value,data:n})}})},L=()=>{g.value=!1,E("cancel")},j=()=>{y.value=!1,g.value=!1},q=()=>{y.value=!1},W=i=>({0:"warning",1:"success",2:"danger",3:"info"})[i]||"info",$=i=>({0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"})[i]||"未知",N=i=>i?D.parseTime(i,"{y}-{m}-{d} {h}:{i}:{s}"):"--";return S({openDialog:P,onSubmitSuccess:j,onSubmitError:q}),(i,n)=>{const r=s("el-option"),T=s("el-select"),p=s("el-form-item"),u=s("el-col"),_=s("el-input"),c=s("el-row"),K=s("el-input-number"),X=s("el-tag"),z=s("el-form"),k=s("el-button"),H=s("el-dialog"),J=le("loading");return f(),b(H,{modelValue:l(g),"onUpdate:modelValue":n[11]||(n[11]=o=>de(g)?g.value=o:null),title:l(C),width:l(h),"close-on-click-modal":!1,"append-to-body":""},{footer:a(()=>[ie("div",ue,[e(k,{onClick:L},{default:a(()=>[v("取 消")]),_:1}),l(d)!=="view"?(f(),b(k,{key:0,type:"primary",onClick:B,loading:l(y)},{default:a(()=>[v("确 定")]),_:1},8,["loading"])):x("",!0)])]),default:a(()=>[ae((f(),b(z,{ref_key:"formRef",ref:w,model:l(t),rules:l(G),"label-width":"120px"},{default:a(()=>[e(c,{gutter:20},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(p,{label:"培训订单",prop:"orderId"},{default:a(()=>[e(T,{modelValue:l(t).orderId,"onUpdate:modelValue":n[0]||(n[0]=o=>l(t).orderId=o),placeholder:"请选择培训订单",style:{width:"100%"},disabled:l(d)==="view"},{default:a(()=>[(f(!0),te(ne,null,oe(l(I),o=>(f(),b(r,{key:o.orderId,label:o.orderTitle,value:o.orderId},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(p,{label:"报名人姓名",prop:"applicantName"},{default:a(()=>[e(_,{modelValue:l(t).applicantName,"onUpdate:modelValue":n[1]||(n[1]=o=>l(t).applicantName=o),placeholder:"请输入报名人姓名",disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:20},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(p,{label:"报名人手机号",prop:"applicantPhone"},{default:a(()=>[e(_,{modelValue:l(t).applicantPhone,"onUpdate:modelValue":n[2]||(n[2]=o=>l(t).applicantPhone=o),placeholder:"请输入报名人手机号",disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(p,{label:"报名人邮箱",prop:"applicantEmail"},{default:a(()=>[e(_,{modelValue:l(t).applicantEmail,"onUpdate:modelValue":n[3]||(n[3]=o=>l(t).applicantEmail=o),placeholder:"请输入报名人邮箱",disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:20},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(p,{label:"身份证号",prop:"applicantIdCard"},{default:a(()=>[e(_,{modelValue:l(t).applicantIdCard,"onUpdate:modelValue":n[4]||(n[4]=o=>l(t).applicantIdCard=o),placeholder:"请输入身份证号",disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(p,{label:"性别",prop:"applicantGender"},{default:a(()=>[e(T,{modelValue:l(t).applicantGender,"onUpdate:modelValue":n[5]||(n[5]=o=>l(t).applicantGender=o),placeholder:"请选择性别",style:{width:"100%"},disabled:l(d)==="view"},{default:a(()=>[e(r,{label:"男",value:"男"}),e(r,{label:"女",value:"女"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,{gutter:20},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(p,{label:"年龄",prop:"applicantAge"},{default:a(()=>[e(K,{modelValue:l(t).applicantAge,"onUpdate:modelValue":n[6]||(n[6]=o=>l(t).applicantAge=o),min:16,max:100,placeholder:"请输入年龄",style:{width:"100%"},disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(p,{label:"学历",prop:"applicantEducation"},{default:a(()=>[e(T,{modelValue:l(t).applicantEducation,"onUpdate:modelValue":n[7]||(n[7]=o=>l(t).applicantEducation=o),placeholder:"请选择学历",style:{width:"100%"},disabled:l(d)==="view"},{default:a(()=>[e(r,{label:"小学",value:"小学"}),e(r,{label:"初中",value:"初中"}),e(r,{label:"中专",value:"中专"}),e(r,{label:"高中",value:"高中"}),e(r,{label:"大专",value:"大专"}),e(r,{label:"本科",value:"本科"}),e(r,{label:"硕士",value:"硕士"}),e(r,{label:"博士",value:"博士"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,null,{default:a(()=>[e(u,{span:24},{default:a(()=>[e(p,{label:"联系地址",prop:"applicantAddress"},{default:a(()=>[e(_,{modelValue:l(t).applicantAddress,"onUpdate:modelValue":n[8]||(n[8]=o=>l(t).applicantAddress=o),placeholder:"请输入联系地址",disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,null,{default:a(()=>[e(u,{span:24},{default:a(()=>[e(p,{label:"工作经验",prop:"applicantExperience"},{default:a(()=>[e(_,{modelValue:l(t).applicantExperience,"onUpdate:modelValue":n[9]||(n[9]=o=>l(t).applicantExperience=o),type:"textarea",rows:3,placeholder:"请输入工作经验",disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(c,null,{default:a(()=>[e(u,{span:24},{default:a(()=>[e(p,{label:"报名备注",prop:"applicationNote"},{default:a(()=>[e(_,{modelValue:l(t).applicationNote,"onUpdate:modelValue":n[10]||(n[10]=o=>l(t).applicationNote=o),type:"textarea",rows:3,placeholder:"请输入报名备注",disabled:l(d)==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(d)==="view"?(f(),b(c,{key:0},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(p,{label:"报名状态"},{default:a(()=>[e(X,{type:W(l(t).applicationStatus)},{default:a(()=>[v(V($(l(t).applicationStatus)),1)]),_:1},8,["type"])]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(p,{label:"报名时间"},{default:a(()=>[v(V(N(l(t).applicationTime)),1)]),_:1})]),_:1})]),_:1})):x("",!0),l(d)==="view"&&l(t).reviewTime?(f(),b(c,{key:1},{default:a(()=>[e(u,{span:12},{default:a(()=>[e(p,{label:"审核人"},{default:a(()=>[v(V(l(t).reviewer||"--"),1)]),_:1})]),_:1}),e(u,{span:12},{default:a(()=>[e(p,{label:"审核时间"},{default:a(()=>[v(V(N(l(t).reviewTime)),1)]),_:1})]),_:1})]),_:1})):x("",!0),l(d)==="view"&&l(t).reviewComment?(f(),b(c,{key:2},{default:a(()=>[e(u,{span:24},{default:a(()=>[e(p,{label:"审核意见"},{default:a(()=>[v(V(l(t).reviewComment),1)]),_:1})]),_:1})]),_:1})):x("",!0)]),_:1},8,["model","rules"])),[[J,l(F)]])]),_:1},8,["modelValue","title","width"])}}},me=Y(re,[["__scopeId","data-v-f2bef001"]]);export{me as default};
