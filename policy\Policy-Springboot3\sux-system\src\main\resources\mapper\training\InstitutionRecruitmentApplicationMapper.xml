<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.InstitutionRecruitmentApplicationMapper">
    
    <resultMap type="InstitutionRecruitmentApplication" id="InstitutionRecruitmentApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="recruitmentId"    column="recruitment_id"    />
        <result property="recruitmentTitle"    column="recruitment_title"    />
        <result property="institutionName"    column="institution_name"    />
        <result property="institutionCode"    column="institution_code"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="institutionAddress"    column="institution_address"    />
        <result property="institutionType"    column="institution_type"    />
        <result property="establishedDate"    column="established_date"    />
        <result property="registeredCapital"    column="registered_capital"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="trainingExperience"    column="training_experience"    />
        <result property="trainingCapacity"    column="training_capacity"    />
        <result property="trainingPlan"    column="training_plan"    />
        <result property="teacherInfo"    column="teacher_info"    />
        <result property="facilityInfo"    column="facility_info"    />
        <result property="qualificationFiles"    column="qualification_files"    />
        <result property="trainingPlanFile"    column="training_plan_file"    />
        <result property="teacherCertFiles"    column="teacher_cert_files"    />
        <result property="facilityFiles"    column="facility_files"    />
        <result property="otherFiles"    column="other_files"    />
        <result property="applicationStatus"    column="application_status"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="reviewTime"    column="review_time"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="reviewComment"    column="review_comment"    />
        <result property="applicationNote"    column="application_note"    />
        <result property="applicantUserId"    column="applicant_user_id"    />
        <result property="isSelected"    column="is_selected"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectInstitutionRecruitmentApplicationVo">
        select a.application_id, a.recruitment_id, a.institution_name, a.institution_code, a.legal_person, a.contact_person, a.contact_phone, a.contact_email, a.institution_address, a.institution_type, a.established_date, a.registered_capital, a.business_scope, a.training_experience, a.training_capacity, a.training_plan, a.teacher_info, a.facility_info, a.qualification_files, a.training_plan_file, a.teacher_cert_files, a.facility_files, a.other_files, a.application_status, a.application_time, a.review_time, a.reviewer, a.review_comment, a.application_note, a.applicant_user_id, a.is_selected, a.del_flag, a.create_id, a.create_time, a.update_id, a.update_time, a.remark,
               r.recruitment_title
        from institution_recruitment_application a
        left join online_training_institution_recruitment r on a.recruitment_id = r.recruitment_id
    </sql>

    <select id="selectInstitutionRecruitmentApplicationList" parameterType="InstitutionRecruitmentApplication" resultMap="InstitutionRecruitmentApplicationResult">
        <include refid="selectInstitutionRecruitmentApplicationVo"/>
        <where>  
            <if test="recruitmentId != null "> and a.recruitment_id = #{recruitmentId}</if>
            <if test="recruitmentTitle != null  and recruitmentTitle != ''"> and r.recruitment_title like concat('%', #{recruitmentTitle}, '%')</if>
            <if test="institutionName != null  and institutionName != ''"> and a.institution_name like concat('%', #{institutionName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and a.contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and a.contact_phone like concat('%', #{contactPhone}, '%')</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and a.application_status = #{applicationStatus}</if>
            <if test="applicantUserId != null "> and a.applicant_user_id = #{applicantUserId}</if>
            <if test="isSelected != null  and isSelected != ''"> and a.is_selected = #{isSelected}</if>
            and a.del_flag = '0'
        </where>
        order by a.application_time desc
    </select>
    
    <select id="selectInstitutionRecruitmentApplicationByApplicationId" parameterType="Long" resultMap="InstitutionRecruitmentApplicationResult">
        <include refid="selectInstitutionRecruitmentApplicationVo"/>
        where a.application_id = #{applicationId} and a.del_flag = '0'
    </select>

    <select id="selectApplicationsByRecruitmentId" parameterType="Long" resultMap="InstitutionRecruitmentApplicationResult">
        <include refid="selectInstitutionRecruitmentApplicationVo"/>
        where a.recruitment_id = #{recruitmentId} and a.del_flag = '0'
        order by a.application_time desc
    </select>

    <select id="selectApplicationsByUserId" parameterType="Long" resultMap="InstitutionRecruitmentApplicationResult">
        <include refid="selectInstitutionRecruitmentApplicationVo"/>
        where a.applicant_user_id = #{userId} and a.del_flag = '0'
        order by a.application_time desc
    </select>

    <select id="checkExistingApplication" resultMap="InstitutionRecruitmentApplicationResult">
        <include refid="selectInstitutionRecruitmentApplicationVo"/>
        where a.recruitment_id = #{recruitmentId} and a.applicant_user_id = #{userId} and a.del_flag = '0'
        limit 1
    </select>

    <select id="getApplicationStats" parameterType="Long" resultMap="InstitutionRecruitmentApplicationResult">
        select 
            count(*) as application_id,
            count(case when application_status = '0' then 1 end) as institution_name,
            count(case when application_status = '1' then 1 end) as institution_code,
            count(case when application_status = '2' then 1 end) as legal_person,
            count(case when application_status = '3' then 1 end) as contact_person,
            count(case when is_selected = '1' then 1 end) as is_selected
        from institution_recruitment_application 
        where recruitment_id = #{recruitmentId} and del_flag = '0'
    </select>
        
    <insert id="insertInstitutionRecruitmentApplication" parameterType="InstitutionRecruitmentApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into institution_recruitment_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recruitmentId != null">recruitment_id,</if>
            <if test="institutionName != null and institutionName != ''">institution_name,</if>
            <if test="institutionCode != null">institution_code,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="institutionAddress != null">institution_address,</if>
            <if test="institutionType != null">institution_type,</if>
            <if test="establishedDate != null">established_date,</if>
            <if test="registeredCapital != null">registered_capital,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="trainingExperience != null">training_experience,</if>
            <if test="trainingCapacity != null">training_capacity,</if>
            <if test="trainingPlan != null">training_plan,</if>
            <if test="teacherInfo != null">teacher_info,</if>
            <if test="facilityInfo != null">facility_info,</if>
            <if test="qualificationFiles != null">qualification_files,</if>
            <if test="trainingPlanFile != null">training_plan_file,</if>
            <if test="teacherCertFiles != null">teacher_cert_files,</if>
            <if test="facilityFiles != null">facility_files,</if>
            <if test="otherFiles != null">other_files,</if>
            <if test="applicationStatus != null">application_status,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewComment != null">review_comment,</if>
            <if test="applicationNote != null">application_note,</if>
            <if test="applicantUserId != null">applicant_user_id,</if>
            <if test="isSelected != null">is_selected,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recruitmentId != null">#{recruitmentId},</if>
            <if test="institutionName != null and institutionName != ''">#{institutionName},</if>
            <if test="institutionCode != null">#{institutionCode},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="institutionAddress != null">#{institutionAddress},</if>
            <if test="institutionType != null">#{institutionType},</if>
            <if test="establishedDate != null">#{establishedDate},</if>
            <if test="registeredCapital != null">#{registeredCapital},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="trainingExperience != null">#{trainingExperience},</if>
            <if test="trainingCapacity != null">#{trainingCapacity},</if>
            <if test="trainingPlan != null">#{trainingPlan},</if>
            <if test="teacherInfo != null">#{teacherInfo},</if>
            <if test="facilityInfo != null">#{facilityInfo},</if>
            <if test="qualificationFiles != null">#{qualificationFiles},</if>
            <if test="trainingPlanFile != null">#{trainingPlanFile},</if>
            <if test="teacherCertFiles != null">#{teacherCertFiles},</if>
            <if test="facilityFiles != null">#{facilityFiles},</if>
            <if test="otherFiles != null">#{otherFiles},</if>
            <if test="applicationStatus != null">#{applicationStatus},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewComment != null">#{reviewComment},</if>
            <if test="applicationNote != null">#{applicationNote},</if>
            <if test="applicantUserId != null">#{applicantUserId},</if>
            <if test="isSelected != null">#{isSelected},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateInstitutionRecruitmentApplication" parameterType="InstitutionRecruitmentApplication">
        update institution_recruitment_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="recruitmentId != null">recruitment_id = #{recruitmentId},</if>
            <if test="institutionName != null and institutionName != ''">institution_name = #{institutionName},</if>
            <if test="institutionCode != null">institution_code = #{institutionCode},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="institutionAddress != null">institution_address = #{institutionAddress},</if>
            <if test="institutionType != null">institution_type = #{institutionType},</if>
            <if test="establishedDate != null">established_date = #{establishedDate},</if>
            <if test="registeredCapital != null">registered_capital = #{registeredCapital},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="trainingExperience != null">training_experience = #{trainingExperience},</if>
            <if test="trainingCapacity != null">training_capacity = #{trainingCapacity},</if>
            <if test="trainingPlan != null">training_plan = #{trainingPlan},</if>
            <if test="teacherInfo != null">teacher_info = #{teacherInfo},</if>
            <if test="facilityInfo != null">facility_info = #{facilityInfo},</if>
            <if test="qualificationFiles != null">qualification_files = #{qualificationFiles},</if>
            <if test="trainingPlanFile != null">training_plan_file = #{trainingPlanFile},</if>
            <if test="teacherCertFiles != null">teacher_cert_files = #{teacherCertFiles},</if>
            <if test="facilityFiles != null">facility_files = #{facilityFiles},</if>
            <if test="otherFiles != null">other_files = #{otherFiles},</if>
            <if test="applicationStatus != null">application_status = #{applicationStatus},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="applicationNote != null">application_note = #{applicationNote},</if>
            <if test="applicantUserId != null">applicant_user_id = #{applicantUserId},</if>
            <if test="isSelected != null">is_selected = #{isSelected},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteInstitutionRecruitmentApplicationByApplicationId" parameterType="Long">
        update institution_recruitment_application set del_flag = '2' where application_id = #{applicationId}
    </delete>

    <delete id="deleteInstitutionRecruitmentApplicationByApplicationIds" parameterType="String">
        update institution_recruitment_application set del_flag = '2' where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>

    <!-- 审核申请 -->
    <update id="reviewApplication">
        update institution_recruitment_application 
        set application_status = #{status}, 
            review_time = now(), 
            reviewer = #{reviewer}, 
            review_comment = #{reviewComment},
            update_time = now()
        where application_id = #{applicationId} and del_flag = '0'
    </update>

    <!-- 选中机构 -->
    <update id="selectInstitution" parameterType="Long">
        update institution_recruitment_application 
        set is_selected = '1', update_time = now() 
        where application_id = #{applicationId} and del_flag = '0'
    </update>

    <!-- 取消选中机构 -->
    <update id="unselectInstitution" parameterType="Long">
        update institution_recruitment_application 
        set is_selected = '0', update_time = now() 
        where application_id = #{applicationId} and del_flag = '0'
    </update>

    <!-- 取消申请 -->
    <update id="cancelApplication" parameterType="Long">
        update institution_recruitment_application 
        set application_status = '3', update_time = now() 
        where application_id = #{applicationId} and del_flag = '0'
    </update>

    <!-- 批量审核申请 -->
    <update id="batchReviewApplications">
        update institution_recruitment_application 
        set application_status = #{status}, 
            review_time = now(), 
            reviewer = #{reviewer}, 
            review_comment = #{reviewComment},
            update_time = now()
        where application_id in 
        <foreach item="applicationId" collection="applicationIds" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
        and del_flag = '0'
    </update>

</mapper>
