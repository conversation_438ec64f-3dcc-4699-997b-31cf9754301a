import{_ as K,d as P,r as d,A as Q,w as X,f as t,l as s,o as l,k as r,h as V,Z as ee,i as y,c as b,L as w,M as C,t as g,m as S,p as H}from"./index-DP10CBaW.js";const ae={class:"divider-text"},le={key:6,class:"form-text"},oe={class:"dialog-footer"},te={__name:"ApplicationFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(v,{expose:M,emit:L}){const{proxy:ne}=P(),k=L,O=v,_=d(!1),x=d(""),i=d(""),a=d({}),D=d({}),h=d(!1),c=d(null),u=Q(()=>i.value==="view");X(()=>O.formFields,n=>{const m={};n.forEach(p=>{p.rules&&p.prop&&(m[p.prop]=p.rules)}),D.value=m},{immediate:!0,deep:!0});const A=(n,m,p={})=>{i.value=n,x.value=m,a.value={...p},_.value=!0,c.value&&c.value.clearValidate()},B=n=>i.value==="add"?n.addDisplay!==!1:i.value==="edit"?n.editDisplay!==!1:i.value==="view"?n.viewDisplay!==!1:!0,N=async()=>{if(c.value)try{await c.value.validate(),h.value=!0;const n={type:i.value,data:{...a.value}};k("submit",n)}catch(n){console.error("表单验证失败:",n)}},R=()=>{h.value=!1,_.value=!1,U()},W=()=>{h.value=!1},$=()=>{_.value=!1,U(),k("cancel")},U=()=>{a.value={},c.value&&c.value.resetFields()};return M({openDialog:A,onSubmitSuccess:R,onSubmitError:W}),(n,m)=>{const p=t("el-divider"),F=t("el-col"),f=t("el-input"),E=t("el-option"),T=t("el-select"),I=t("el-date-picker"),j=t("el-input-number"),z=t("el-switch"),Z=t("el-form-item"),q=t("el-row"),G=t("el-form"),Y=t("el-button"),J=t("el-dialog");return l(),s(J,{modelValue:_.value,"onUpdate:modelValue":m[0]||(m[0]=e=>_.value=e),title:x.value,width:v.formOption.dialogWidth||"1000px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:r(()=>[V("div",oe,[y(Y,{onClick:$},{default:r(()=>[H(g(u.value?"关闭":"取消"),1)]),_:1}),u.value?S("",!0):(l(),s(Y,{key:0,type:"primary",loading:h.value,onClick:N},{default:r(()=>[H(" 确定 ")]),_:1},8,["loading"]))])]),default:r(()=>[V("div",{class:"form-container",style:ee({maxHeight:v.formOption.dialogHeight||"70vh"})},[y(G,{ref_key:"formRef",ref:c,model:a.value,rules:D.value,"label-width":v.formOption.labelWidth||"120px",class:"form-content"},{default:r(()=>[y(q,{gutter:20},{default:r(()=>[(l(!0),b(w,null,C(v.formFields,e=>(l(),b(w,{key:e.prop},[e.divider?(l(),s(F,{key:0,span:24,class:"divider-col"},{default:r(()=>[y(p,{"content-position":"left"},{default:r(()=>[V("span",ae,g(e.label),1)]),_:2},1024)]),_:2},1024)):(l(),s(F,{key:1,span:e.span||12},{default:r(()=>[B(e)?(l(),s(Z,{key:0,label:e.label,prop:e.prop,rules:e.rules},{default:r(()=>[e.type==="input"||!e.type?(l(),s(f,{key:0,modelValue:a.value[e.prop],"onUpdate:modelValue":o=>a.value[e.prop]=o,placeholder:e.placeholder||`请输入${e.label}`,disabled:u.value||e.readonly,maxlength:e.maxlength,"show-word-limit":e.showWordLimit},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","maxlength","show-word-limit"])):e.type==="textarea"?(l(),s(f,{key:1,modelValue:a.value[e.prop],"onUpdate:modelValue":o=>a.value[e.prop]=o,type:"textarea",rows:e.minRows||4,maxlength:e.maxlength,"show-word-limit":e.showWordLimit,placeholder:e.placeholder||`请输入${e.label}`,disabled:u.value||e.readonly},null,8,["modelValue","onUpdate:modelValue","rows","maxlength","show-word-limit","placeholder","disabled"])):e.type==="select"?(l(),s(T,{key:2,modelValue:a.value[e.prop],"onUpdate:modelValue":o=>a.value[e.prop]=o,placeholder:e.placeholder||`请选择${e.label}`,disabled:u.value||e.readonly,style:{width:"100%"}},{default:r(()=>[(l(!0),b(w,null,C(e.dicData,o=>(l(),s(E,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="datetime"?(l(),s(I,{key:3,modelValue:a.value[e.prop],"onUpdate:modelValue":o=>a.value[e.prop]=o,type:"datetime",placeholder:e.placeholder||`请选择${e.label}`,disabled:u.value||e.readonly,format:e.format||"YYYY-MM-DD HH:mm:ss","value-format":e.valueFormat||"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","format","value-format"])):e.type==="number"?(l(),s(j,{key:4,modelValue:a.value[e.prop],"onUpdate:modelValue":o=>a.value[e.prop]=o,min:e.min,max:e.max,step:e.step||1,disabled:u.value||e.readonly,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","min","max","step","disabled"])):e.type==="switch"?(l(),s(z,{key:5,modelValue:a.value[e.prop],"onUpdate:modelValue":o=>a.value[e.prop]=o,disabled:u.value||e.readonly,"active-value":e.activeValue||!0,"inactive-value":e.inactiveValue||!1},null,8,["modelValue","onUpdate:modelValue","disabled","active-value","inactive-value"])):(l(),b("span",le,g(a.value[e.prop]||"-"),1))]),_:2},1032,["label","prop","rules"])):S("",!0)]),_:2},1032,["span"]))],64))),128))]),_:1})]),_:1},8,["model","rules","label-width"])],4)]),_:1},8,["modelValue","title","width"])}}},re=K(te,[["__scopeId","data-v-d1ee4dc7"]]);export{re as default};
