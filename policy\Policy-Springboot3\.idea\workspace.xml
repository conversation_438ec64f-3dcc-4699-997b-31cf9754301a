<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="364a8449-4971-45b1-a42a-ecb3f693ff6a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/doc/bussine-web-policy-2025-07-23.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/doc/bussine-web-policy.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/doc/sql/sux-admin.sql" beforeDir="false" afterPath="$PROJECT_DIR$/doc/sql/sux-admin.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/doc/sql/training_order_test_data.sql" beforeDir="false" afterPath="$PROJECT_DIR$/doc/sql/training_order_test_data.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/sql/job_menu_data.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/sql/job_posting_worker_tables.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/sql/job_test_data.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/sql/training_application_menu.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/sql/training_application_table.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/sql/training_application_table_update.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/JobPostingController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/JobPostingController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/PublicJobMatchController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/PublicJobMatchController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/WorkerProfileController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/WorkerProfileController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/policy/PolicyApplicationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/policy/PolicyApplicationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/src/main/resources/config/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/sux-admin/src/main/resources/config/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-common/src/main/java/com/sux/common/core/domain/BaseEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-common/src/main/java/com/sux/common/core/domain/BaseEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/domain/JobPosting.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/domain/JobPosting.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/domain/PolicyApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/domain/PolicyApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/domain/WorkerProfile.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/domain/WorkerProfile.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/JobPostingMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/JobPostingMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/PolicyApplicationMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/PolicyApplicationMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/WorkerProfileMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/WorkerProfileMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IJobPostingService.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IJobPostingService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IPolicyApplicationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IPolicyApplicationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IWorkerProfileService.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IWorkerProfileService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/JobPostingServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/JobPostingServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/PolicyApplicationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/PolicyApplicationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/WorkerProfileServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/WorkerProfileServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/resources/application-prod.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/policy/PolicyApplicationMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/policy/PolicyApplicationMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/system/JobPostingMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/system/JobPostingMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/system/WorkerProfileMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/system/WorkerProfileMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/.env" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/api/job/posting.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/api/job/posting.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/api/job/worker.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/api/job/worker.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/api/policy/application.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/api/policy/application.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/assets/images/login-background.png" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/assets/images/login-background.png" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/components/FileUpload/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/components/FileUpload/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/components/FileView/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/components/FileView/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/const/job/posting.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/const/job/posting.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/const/job/worker.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/const/job/worker.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/layout/components/Navbar.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/layout/components/Navbar.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/router/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/router/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/store/modules/user.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/store/modules/user.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/utils/columnUtils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/utils/columnUtils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/utils/request.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/utils/request.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policy/PolicyFormDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policy/PolicyFormDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyPlan/ApprovalRecordsDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyPlan/ApprovalRecordsDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyPlan/MaterialsDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyPlan/MaterialsDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyPlan/ReviewDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyPlan/ReviewDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyS/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/policy/policyS/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/system/user/profile/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/system/user/profile/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/WorkerProfileFormDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/WorkerProfileFormDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/application/TrainingApplicationFormDialog.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/application/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/application/my-applications.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/application/signup.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/match.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/worker.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/worker.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/vite.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/vite.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../plugs/Redis-x64-3.2.100/dump.rdb" beforeDir="false" afterPath="$PROJECT_DIR$/../plugs/Redis-x64-3.2.100/dump.rdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../plugs/nginx-1.18.0/logs/access.log" beforeDir="false" afterPath="$PROJECT_DIR$/../plugs/nginx-1.18.0/logs/access.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../plugs/nginx-1.18.0/logs/error.log" beforeDir="false" afterPath="$PROJECT_DIR$/../plugs/nginx-1.18.0/logs/error.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../plugs/nginx-1.18.0/logs/nginx.pid" beforeDir="false" afterPath="$PROJECT_DIR$/../plugs/nginx-1.18.0/logs/nginx.pid" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/api_data/all_data.json" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/api_data/all_data.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/api_data/calendar_list.json" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/api_data/calendar_list.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/api_data/file_list.json" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/api_data/file_list.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/api_data/mock_data.json" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/api_data/mock_data.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/api_data/read_list.json" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/api_data/read_list.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/api_data/regions.json" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/api_data/regions.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/generate_static_data.py" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/generate_static_data.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/static_files/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/static_files/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/static_files/js/policySpecial.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/static_files/js/policySpecial.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/python/static_files/js/utils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/python/static_files/js/utils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/activityDetail.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/activityDetail.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/css/index.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/css/index.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/css/trainingOrderList.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/css/trainingOrderList.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/js/indexs.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/js/indexs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderDetail.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderDetail.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderList.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderList.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/public/html/headerBar.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/public/html/headerBar.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/public/js/utils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/public/js/utils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/addPolicy/calendarDetail.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/addPolicy/calendarDetail.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/addPolicy/css/calendarDetail.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/addPolicy/css/calendarDetail.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/addPolicy/css/commonNew.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/addPolicy/css/commonNew.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/css/common.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/css/common.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/css/policySpecial.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/css/policySpecial.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/js/mockData.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/js/mockData.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/js/policySpecial.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/js/policySpecial.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/js/utils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/js/utils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/policySpecial.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/policySpecial.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/preview.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/preview.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/policy/修改说明.md" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/policy/修改说明.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/place/css/index.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/place/css/index.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/place/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/place/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/place/js/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/place/js/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/public/css/common.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/public/css/common.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/public/css/commonNew.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/public/css/commonNew.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/public/html/footerBar.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/public/html/footerBar.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/public/html/headerBar.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/public/html/headerBar.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/public/js/utils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/public/js/utils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/README-招聘信息匹配功能.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/css/jobWorkerMatch.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/css/jobWorkerMatch.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/css/talentSpecial.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/css/talentSpecial.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/jobWorkerMatch.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/jobWorkerMatch.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/js/jobWorkerMatch.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/js/jobWorkerMatch.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/talentSpecial.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/talentSpecial.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/修复说明-参照trainingOrderList.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/更新说明-原生Ajax实现.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/问题排查指南.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/_DataURI/data.image.gif.b7ebe47919df1.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/_DataURI/data.image.png.4e3f3127be0b9.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/_DataURI/data.image.png.6a0bbe485a5bc.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/c.cnzz.com/c.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-manage/v2/front/subject/approvedCompanyList.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-manage/v2/front/subject/getHotSubjectList.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-manage/v2/front/subject/getRelationPolicyList/481a446a728b48d996ee1c391760b15a.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-map/v1/specialArea/frontend.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-match/v2/front/sysArea/sysAreaLink.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-match/v2/front/sysDictData/sysDictDataList-448989eda0fa1.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-match/v2/front/sysDictData/sysDictDataList.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-match/v2/front/viewProjectlist/viewProjectlistInfo.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-qingdao/v1/projectAddQuestion/listUploadFile-af4a4497f0a25.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-qingdao/v1/projectAddQuestion/listUploadFile-e16cf0155e914.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-qingdao/v1/projectAddQuestion/listUploadFile.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-uaa/v1/validate/code.gif" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/api-uaa/v1/validate/code.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/index/indexImg/userNameIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/place/image/closeIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/place/image/new_cycdBtn2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/place/image/new_cycdTabBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/calendarDetail.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/css/calendarDetail.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/css/dragModule.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/css/smusic.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/bianji.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/bufuheIco.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/calDEnter01.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/code.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/companyName.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/daiwanshan.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/delTitIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/fileD2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/leftBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/sqz.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/unloginTargetReadBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/wanchengIco.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/wjIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/wntj.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/images/wntjIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/policy/js/calendarDetail.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/css/commonNew.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/css/zh.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/html/footerBar.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/html/headerBar.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/icons/dq_arr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/icons/footerBg.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/icons/iconCollection.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/icons/iconShare.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/icons/new_barArr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/icons/pageTitBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/icons/pathHome2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/pics/dzjg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/pics/footerCode.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/pics/footerCodeBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/pics/jiucuo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/pics/new_logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/images/pics/noDataPic.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/js/api.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/js/common.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/js/jquery-3.5.0.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/js/knockout.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/js/mapping.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/js/qrcode.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/js/utils.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/APlayer/dist/APlayer.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/APlayer/dist/APlayer.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/datePicker/WdatePicker.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/datePicker/skin/WdatePicker.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/jbox/Skins/Blue/jbox.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/jbox/jquery.jBox.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/musicPlayer/smusic.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/pagination/jquery.pagination.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/pagination/pagination.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/share/jquery.share.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/share/share.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/videojs/video-js.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/public/plugins/videojs/video.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/union/images/detailBgBottom.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/union/images/detailBgMin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/hrss.qingdao.gov.cn/cy/union/images/detailBgTop.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/icon.cnzz.com/img/pic1.gif" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/s4.cnzz.com/z.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策申报/z3.cnzz.com/stat.htm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/.vscode/settings.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/c.cnzz.com/c.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/file.zhenghe.cn/group1/M00/BC/D4/wKge52hBf-eADAPMAAm17vc4hBM239.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/file.zhenghe.cn/group1/M00/BC/FF/wKge52hMMiGAVC3tAAlSmGpJ--g574.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/fss.ahcy.gov.cn/qingdaovideo/周周问第72期.mp4" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/fss.ahcy.gov.cn/qingdaovideo/最终版三集.mp4" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/fss.ahcy.gov.cn/qingdaovideo/最终版二集.mp4" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/subject/dSubjectInfoListPage.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/subject/findFilterDeclareTime.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/sysArea/sysAreaAndChildsByIp.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/sysDictData/sysDictDataList-5d2982f22a68b.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/sysDictData/sysDictDataList-ea84d5125ee8a.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/sysDictData/sysDictDataList.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/zctPolicy/policyListPage.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-manage/v2/front/zctRead/zctReadListPage.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-map/v1/specialArea/frontend.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-policy/v1/frontPolicyVideo/policyVideoListPage.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-policy/v1/policyAreaPunishApp/allPolicyAreaList.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/api-uaa/v1/validate/code.gif" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/index/indexImg/baseEnterIcon2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/index/indexImg/baseEnterIcon3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/index/indexImg/baseEnterIcon4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/index/indexImg/baseEnterIcon5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/index/indexImg/baseEnterIcon7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/place/image/closeIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/place/image/selectModule_arrowOff.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/css/policySpecial.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/baseEnter.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/index_dspArr1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/index_dspArr2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/index_dspTit.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/index_jdBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/index_wspBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/lb_bg.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/pic_noDetail.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policyReadNoPic.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySBanner.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySBg1_1.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySBg2_1.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySBg3_1.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySIcon1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySIcon10.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySIcon2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySImg1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySImg2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySLiBg1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/policySLiBg2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/popBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box2Icon6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box4LiIcon3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_box6IconLink.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_boxMore.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_boxTitleBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_popClose.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/ps_popTitleIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/images/tips.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/js/mockData.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/js/policySpecial.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/policySpecial.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/policy/test-mock.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/css/common.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/css/zh.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/html/footerBar.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/html/headerBar.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/icons/dq_arr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/icons/footerBg.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/icons/new_barArr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/pics/dzjg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/pics/footerCode.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/pics/footerCodeBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/pics/jiucuo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/pics/new_logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/images/pics/noDataPic.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/js/api.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/js/common.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/js/jquery-3.5.0.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/js/knockout.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/js/mapping.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/js/utils.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/plugins/jbox/Skins/Blue/jbox.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/plugins/jbox/jquery.jBox.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/plugins/pagination/pagination.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/hrss.qingdao.gov.cn/cy/public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/icon.cnzz.com/img/pic1.gif" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/s4.cnzz.com/z.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/all_data.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/banners.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/calendar_list.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/column_list.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/file_list.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/mock_data.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/policy_types.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/read_list.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/regions.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/support_modes.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/api_data/video_list.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/copy_images.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/create_placeholder_images.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/download_missing_images.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/fetch_api_data.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/fetch_website.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/generate_static_data.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/inject_data_to_html.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/css/common.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/css/jbox.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/css/pagination.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/css/policySpecial.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/css/zh.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon1on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon2on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon3on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon4on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon5.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon5on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon6.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon6on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon7.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterIcon7on.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/baseEnterOn.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/closeIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/lb_bg.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/pic_noDetail.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/policySBanner.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/policySImg2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/ps_popClose.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/ps_popTitleIcon.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/tips.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/images/tipson.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/api.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/common.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/jquery-3.5.0.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/jquery.SuperSlide.2.1.3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/jquery.jBox.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/knockout.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/mapping.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/policySpecial.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/web-com/static_files/js/utils.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/企业就业服务/政策首页/z3.cnzz.com/stat.htm" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/api-activity/v1/frontQctActivityReview/qctActivityReviewListPage.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/api-app/v1/qctActivity/findQctActivityListPage-0bb92103cff88.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/api-app/v1/qctActivity/findQctActivityListPage.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/api-app/v1/sysDictData/listDict.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/api-map/v1/specialArea/frontend.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/api-uaa/v1/validate/code.gif" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/css/index.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/new_zhdAddress.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/new_zhdArr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/new_zhdBanner.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/new_zhdJqhdBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/new_zhdJqhdTab3.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/new_zhdTime.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_bg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_bg2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_btn1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_btn2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_btn4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_enter1.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_enter2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_enter4.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_libg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_listbg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_title.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/image/zhd_titleBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/activity/js/indexs.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/css/common.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/css/zh.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/html/footerBar.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/html/headerBar.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/icons/dq_arr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/icons/footerBg.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/icons/icon_more.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/icons/new_barArr.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/icons/titleStyleLine.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/pics/dzjg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/pics/footerCode.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/pics/footerCodeBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/pics/jiucuo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/images/pics/new_logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/js/api.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/js/common.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/js/jquery-3.5.0.min.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/js/knockout.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/js/mapping.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/js/utils.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/plugins/jbox/Skins/Blue/jbox.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/plugins/jbox/jquery.jBox.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/plugins/pagination/jquery.pagination.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/plugins/pagination/pagination.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/plugins/swiper/swiper.min.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/就业培训/hrss/hrss.qingdao.gov.cn/cy/public/plugins/swiper/swiper.min5.js" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\maven\apache-maven-3.8.8" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2xXYOTJkNGZGSQOd0tvyptLjJHA" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sux [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sux [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.sux [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.RuoYiAdminApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RuoYiAppApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.SuxAdminApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.SuxAppApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;21&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/gitData/work/bussine/receiving-orders_ll/web-site/policy/Policy-Springboot3/sux-system/src/main/resources/mapper/system&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.39770114&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.fonts.default&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\gitData\work\bussine\receiving-orders_ll\web-site\policy\Policy-Springboot3\sux-system\src\main\resources\mapper\system" />
      <recent name="E:\gitData\work\git-open-source\sux-admin\Sux-Springboot3\sux-app\src\main\resources\config" />
      <recent name="E:\gitData\work\git-open-source\sux-admin\Sux-Springboot3\sux-admin\src\main\resources\config" />
      <recent name="E:\gitData\work\bussine\ruoyi-vue3-springboot3\RuoYi-Springboot3\ruoyi-app" />
      <recent name="E:\gitData\work\bussine\ruoyi-vue3-springboot3\RuoYi-Springboot3\ruoyi-admin\src\main\resources\config" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.sux.web.controller.system" />
      <recent name="com.sux.web.controller" />
      <recent name="com.ruoyi.framework.config" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SuxAppApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="sux-system" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="sux-system" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SuxAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="sux-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sux.SuxAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SuxAppApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sux-app" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sux.SuxAppApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.SuxAdminApplication" />
      <item itemvalue="Spring Boot.SuxAppApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="364a8449-4971-45b1-a42a-ecb3f693ff6a" name="Changes" comment="" />
      <created>1748085852634</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748085852634</updated>
      <workItem from="1748085853768" duration="570000" />
      <workItem from="1748086761816" duration="4974000" />
      <workItem from="1748091879745" duration="1720000" />
      <workItem from="1748792186526" duration="295000" />
      <workItem from="1748844411428" duration="40000" />
      <workItem from="1749292978125" duration="2411000" />
      <workItem from="1749295702971" duration="69000" />
      <workItem from="1749303692726" duration="24000" />
      <workItem from="1750935497518" duration="623000" />
      <workItem from="1750936904632" duration="1240000" />
      <workItem from="1750938161039" duration="3998000" />
      <workItem from="1750945713621" duration="3844000" />
      <workItem from="1751025957254" duration="630000" />
      <workItem from="1751026830220" duration="9291000" />
      <workItem from="1751077172910" duration="9933000" />
      <workItem from="1751118402759" duration="12833000" />
      <workItem from="1751139011027" duration="2737000" />
      <workItem from="1751195148852" duration="3785000" />
      <workItem from="1751283791802" duration="646000" />
      <workItem from="1751287370565" duration="5000" />
      <workItem from="1751373813616" duration="21000" />
      <workItem from="1752069209864" duration="1968000" />
      <workItem from="1752154894146" duration="2075000" />
      <workItem from="1752238707147" duration="4464000" />
      <workItem from="1752391871757" duration="17000" />
      <workItem from="1752401952721" duration="230000" />
      <workItem from="1752410206058" duration="685000" />
      <workItem from="1752923111772" duration="27000" />
      <workItem from="1753096916447" duration="10999000" />
      <workItem from="1753183134705" duration="9192000" />
      <workItem from="1753266738869" duration="22296000" />
      <workItem from="1753327050136" duration="7568000" />
      <workItem from="1753421060310" duration="8000" />
      <workItem from="1753429483920" duration="3845000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="fileName" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>