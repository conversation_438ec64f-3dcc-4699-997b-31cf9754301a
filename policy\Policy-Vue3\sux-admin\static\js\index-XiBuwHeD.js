import{l as ie,A as oe,g as se,u as ne,f as re,a as pe}from"./ApprovalRecordsDialog-DW515uqd.js";import{a7 as O,C as ce,d as ue,r as l,D as de,I as me,e as fe,f as C,K as ve,c as M,o as y,l as k,i as s,k as r,h as ye,G as A,m as $,p as f,t as ge,j as z}from"./index-DP10CBaW.js";import{g as he,e as De}from"./columnUtils-DYlA-XL_.js";import{T as be}from"./index-BWWetMd6.js";import Se from"./ApplicationFormDialog-DXkqHMoj.js";import _e from"./ReviewDialog-C7JfsrcQ.js";import we from"./MaterialsDialog-4SWjNsM-.js";const Ce=I=>({dialogWidth:"1000px",dialogHeight:"70vh",labelWidth:"120px",column:[{label:"申请基础信息",prop:"divider_basic_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"政策名称",prop:"policyName",search:!0,searchSpan:8,minWidth:200,addDisplay:!1,editDisplay:!1,viewDisplay:!0},{label:"政策类型",prop:"policyType",search:!0,searchSpan:8,width:120,align:"center",type:"select",dicData:[{label:"就业扶持",value:"就业扶持"},{label:"创业支持",value:"创业支持"},{label:"技能培训",value:"技能培训"},{label:"社会保障",value:"社会保障"},{label:"其他",value:"其他"}],addDisplay:!1,editDisplay:!1,viewDisplay:!0},{label:"姓名",prop:"applicantName",search:!0,searchSpan:8,width:120,align:"center",addDisplay:!1,editDisplay:!1,viewDisplay:!0},{label:"手机号",prop:"applicantPhone",searchSpan:8,width:120,align:"center",addDisplay:!1,editDisplay:!1,viewDisplay:!0},{label:"申请人",prop:"applicantUserName",search:!0,searchSpan:8,width:120,align:"center",addDisplay:!1,editDisplay:!1,viewDisplay:!0},{label:"申请状态",prop:"applicationStatus",search:!0,searchSpan:8,width:120,align:"center",type:"select",dicData:[{label:"待初审",value:"0",color:"warning"},{label:"初审通过",value:"1",color:"success"},{label:"初审拒绝",value:"2",color:"danger"},{label:"待终审",value:"3",color:"warning"},{label:"终审通过",value:"4",color:"success"},{label:"终审拒绝",value:"5",color:"danger"},{label:"已完成",value:"6",color:"info"}],slot:!0,addDisplay:!1,editDisplay:!1,viewDisplay:!0},{label:"提交时间",prop:"submitTime",searchSpan:8,width:160,align:"center",type:"datetime",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",addDisplay:!1,editDisplay:!1,viewDisplay:!0,formatter:u=>O(u.submitTime)},{label:"完成时间",prop:"completeTime",search:!1,width:160,align:"center",addDisplay:!1,editDisplay:!1,viewDisplay:!0,formatter:u=>u.completeTime?O(u.completeTime):"-"},{label:"申请材料信息",prop:"divider_materials_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0,viewDisplay:!0,addDisplay:!1,editDisplay:!1},{label:"系统信息",prop:"divider_system_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0,viewDisplay:!0,addDisplay:!1,editDisplay:!1},{label:"备注",prop:"remark",search:!1,type:"textarea",span:24,minRows:2,maxRows:4,readonly:!0,addDisplay:!1,editDisplay:!1,viewDisplay:!0}]}),ke={class:"policy-plan-container app-container"},Re={class:"operation-btns"},Te={key:1,class:"loading-placeholder"},xe=ce({name:"PolicyPlan"}),ze=Object.assign(xe,{setup(I){const{proxy:p}=ue(),g=l([]),u=l([]),W=l(!0),Y=l(!0),S=l(0),R=l([]),T=l([]),h=l(!1),x=l(!1),_=l({dialogWidth:"1000px",dialogHeight:"70vh"}),L=l(null),D=l(null),v=l(null),N=l(null),P=l(null),F=l([]),w=l({}),V=de({queryParams:{pageNum:1,pageSize:10,policyName:void 0,applicationStatus:void 0,applicantUserName:void 0}}),{queryParams:n}=me(V);fe(async()=>{await H(),c()});const H=async()=>{try{const e=Ce(p),a=await he({baseOption:e,proxy:p}),{tableColumns:o,searchColumns:t,formFields:d,formOptions:m}=De(a);R.value=o,T.value=t,F.value=d,_.value={..._.value,...m},x.value=!0}catch(e){console.error("初始化配置失败:",e),p.$modal.msgError("初始化配置失败")}};function c(){h.value=!0,ie({...n.value,...w.value}).then(e=>{g.value=e.rows||[],S.value=e.total||0,h.value=!1}).catch(e=>{console.error("查询申请列表失败:",e),g.value=[],S.value=0,h.value=!1})}const B=e=>{w.value=e,n.value.pageNum=1,c()},E=()=>{w.value={},n.value.pageNum=1,c()},j=e=>{n.value.pageNum=e,c()},q=e=>{n.value.pageSize=e,n.value.pageNum=1,c()};function U(e){u.value=e.map(a=>a.applicationId),W.value=e.length!=1,Y.value=!e.length}const G=e=>({0:"warning",1:"success",2:"danger",3:"warning",4:"success",5:"danger",6:"info"})[e]||"info",K=e=>({0:"待初审",1:"初审通过",2:"初审拒绝",3:"待终审",4:"终审通过",5:"终审拒绝",6:"已完成"})[e]||"未知状态",J=e=>{se(e.applicationId).then(a=>{var o;(o=D.value)==null||o.openDialog("view","查看申请详情",a.data)})},Q=e=>{var a;(a=v.value)==null||a.openDialog("first","初始审核",e)},X=e=>{var a;(a=v.value)==null||a.openDialog("final","终审核",e)},Z=e=>{var a;(a=N.value)==null||a.openDialog(e.applicationId)},ee=e=>{var a;(a=P.value)==null||a.openDialog(e)},ae=async e=>{var a,o;try{const{type:t,data:d,applicationData:m}=e,i={applicationId:m.applicationId,approvalStatus:d.approvalStatus,approvalComment:d.approvalComment};t==="first"?(await re(i),p.$modal.msgSuccess("初审完成")):t==="final"&&(await pe(i),p.$modal.msgSuccess("终审完成")),(a=v.value)==null||a.onSubmitSuccess(),c()}catch(t){(o=v.value)==null||o.onSubmitError(),console.error("审核失败:",t)}},le=async e=>{var a,o;try{e.type==="edit"&&(await ne(e.data),p.$modal.msgSuccess("修改成功")),(a=D.value)==null||a.onSubmitSuccess(),c()}catch(t){(o=D.value)==null||o.onSubmitError(),console.error("提交失败:",t)}},te=()=>{};return(e,a)=>{const o=C("el-tag"),t=C("el-button"),d=C("el-empty"),m=ve("hasPermi");return y(),M("div",ke,[x.value?(y(),k(be,{key:0,columns:R.value,data:g.value,loading:h.value,showIndex:!0,searchColumns:T.value,showOperation:!0,operationLabel:"操作",operationWidth:"280",fixedOperation:!0,ref_key:"tableListRef",ref:L,onSearch:B,onReset:E,defaultPage:{pageSize:z(n).pageSize,currentPage:z(n).pageNum,total:S.value},onCurrentChange:j,onSizeChange:q,onSelectionChange:U},{applicationStatus:r(({row:i})=>[s(o,{type:G(i.applicationStatus),size:"small"},{default:r(()=>[f(ge(K(i.applicationStatus)),1)]),_:2},1032,["type"])]),menu:r(({row:i})=>[ye("div",Re,[s(t,{type:"primary",link:"",onClick:b=>J(i)},{default:r(()=>[f("查看")]),_:2},1032,["onClick"]),s(t,{type:"info",link:"",onClick:b=>ee(i)},{default:r(()=>[f("查看材料")]),_:2},1032,["onClick"]),s(t,{type:"success",link:"",onClick:b=>Z(i)},{default:r(()=>[f("审核记录")]),_:2},1032,["onClick"]),i.applicationStatus==="0"?A((y(),k(t,{key:0,type:"warning",link:"",onClick:b=>Q(i)},{default:r(()=>[f("初审")]),_:2},1032,["onClick"])),[[m,["policy:application:first-review"]]]):$("",!0),i.applicationStatus==="3"?A((y(),k(t,{key:1,type:"danger",link:"",onClick:b=>X(i)},{default:r(()=>[f("终审")]),_:2},1032,["onClick"])),[[m,["policy:application:final-review"]]]):$("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(y(),M("div",Te,[s(d,{description:"正在加载表格配置..."})])),s(Se,{ref_key:"applicationFormDialogRef",ref:D,formFields:F.value,formOption:_.value,onSubmit:le,onCancel:te},null,8,["formFields","formOption"]),s(_e,{ref_key:"reviewDialogRef",ref:v,onSubmit:ae},null,512),s(oe,{ref_key:"approvalRecordsDialogRef",ref:N},null,512),s(we,{ref_key:"materialsDialogRef",ref:P},null,512)])}}});export{ze as default};
