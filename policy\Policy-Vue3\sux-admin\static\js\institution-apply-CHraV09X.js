import{_ as Fe,d as we,r as v,D as le,I as Ie,e as xe,J as x,f as h,c as b,o as m,l as V,i as e,k as t,h as r,m as C,p as s,t as u,j as L,L as B,M as W,N as De,$ as ie,a0 as qe,a1 as Ee,x as Ue,y as Le}from"./index-DP10CBaW.js";import{l as ze}from"./order-ZnAGpiqD.js";import{g as Me,u as Re,s as Oe}from"./institutionApplication-DSgGW-Aq.js";import{T as $e}from"./index-BWWetMd6.js";const P=D=>(Ue("data-v-35256650"),D=D(),Le(),D),je={class:"institution-application-container app-container"},Je=P(()=>r("div",{class:"page-header"},[r("h2",{class:"page-title"},"机构申请"),r("p",{class:"page-description"},"查看所有培训项目，提交机构申请。已申请的项目不能重复申请")],-1)),Be={class:"price"},We={key:1},Ge={class:"operation-btns"},He={key:1,class:"loading-placeholder"},Ke={class:"apply-form"},Qe={class:"form-section"},Xe=P(()=>r("h4",{class:"section-title"},"机构基本信息",-1)),Ye={class:"form-section"},Ze=P(()=>r("h4",{class:"section-title"},"联系信息",-1)),et={class:"form-section"},tt=P(()=>r("h4",{class:"section-title"},"培训能力",-1)),at={class:"form-section"},lt=P(()=>r("h4",{class:"section-title"},"申请材料上传",-1)),it={class:"required-materials"},nt={class:"material-header"},ot={class:"material-info"},st={class:"material-name"},rt={class:"material-status"},ut={class:"material-upload"},dt={class:"dialog-footer"},pt={key:0,class:"application-detail"},ct={class:"detail-section"},mt=P(()=>r("h4",{class:"section-title"},"基本信息",-1)),ft={class:"detail-section"},gt=P(()=>r("h4",{class:"section-title"},"培训能力",-1)),_t={class:"detail-section"},vt=P(()=>r("h4",{class:"section-title"},"申请材料",-1)),ht={class:"uploaded-materials"},yt={class:"material-header"},bt={class:"material-info"},Vt={class:"material-name"},Nt={class:"material-status"},Pt={key:0,class:"material-files"},At={class:"file-grid"},Ct={key:0,class:"detail-section"},St=P(()=>r("h4",{class:"section-title"},"审核信息",-1)),Tt={__name:"institution-apply",setup(D){const{proxy:ne}=we(),G=v(!1),z=v([]),I=v(!1),M=v(!1),R=v(!1),q=v(null),c=v(null),O=v(null),H=v(0),K=v([]),Q=v([]),$=v(!1),j=v(!1),oe=v(null),F=v({}),se=le({queryParams:{pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"}}),{queryParams:N}=Ie(se),o=le({orderId:null,institutionName:"",institutionCode:"",legalPerson:"",contactPerson:"",contactPhone:"",contactEmail:"",institutionAddress:"",institutionType:"",establishedDate:null,registeredCapital:null,businessScope:"",trainingExperience:"",trainingCapacity:"",trainingPlan:"",teacherInfo:"",facilityInfo:"",applicationNote:""}),re={institutionName:[{required:!0,message:"请输入机构名称",trigger:"blur"},{min:2,max:200,message:"长度在 2 到 200 个字符",trigger:"blur"}],legalPerson:[{required:!0,message:"请输入法定代表人",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],contactPerson:[{required:!0,message:"请输入联系人",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],contactEmail:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],institutionAddress:[{required:!0,message:"请输入机构地址",trigger:"blur"},{min:5,max:500,message:"长度在 5 到 500 个字符",trigger:"blur"}],trainingExperience:[{required:!0,message:"请输入培训经验",trigger:"blur"}],trainingCapacity:[{required:!0,message:"请输入培训能力",trigger:"blur"}],trainingPlan:[{required:!0,message:"请输入培训计划",trigger:"blur"}],teacherInfo:[{required:!0,message:"请输入师资信息",trigger:"blur"}]},S=v([{name:"机构营业执照或组织机构代码证",required:!0,files:[],field:"qualificationFiles"},{name:"培训计划详细方案",required:!1,files:[],field:"trainingPlanFile"},{name:"师资队伍资质证明材料",required:!1,files:[],field:"teacherCertFiles"},{name:"培训场地及设施设备证明",required:!1,files:[],field:"facilityFiles"},{name:"其他相关资质证明材料",required:!1,files:[],field:"otherFiles"}]),X=v([{name:"机构营业执照或组织机构代码证",files:[],field:"qualificationFiles"},{name:"培训计划详细方案",files:[],field:"trainingPlanFile"},{name:"师资队伍资质证明材料",files:[],field:"teacherCertFiles"},{name:"培训场地及设施设备证明",files:[],field:"facilityFiles"},{name:"其他相关资质证明材料",files:[],field:"otherFiles"}]);xe(async()=>{await ue(),T()});const ue=async()=>{try{K.value=[{prop:"orderTitle",label:"培训标题",minWidth:200,showOverflowTooltip:!0},{prop:"trainingType",label:"培训类型",width:120,tableSlot:!0},{prop:"trainingLevel",label:"培训级别",width:120},{prop:"trainingDuration",label:"培训时长",width:120},{prop:"trainingFee",label:"培训费用",width:120,tableSlot:!0},{prop:"trainingTime",label:"培训时间",width:200,tableSlot:!0},{prop:"trainingAddress",label:"培训地址",minWidth:150,showOverflowTooltip:!0},{prop:"registrationDeadline",label:"申请截止",width:150,tableSlot:!0},{prop:"applicationStatus",label:"申请状态",width:120,tableSlot:!0},{prop:"applicationTime",label:"申请时间",width:150,tableSlot:!0}],Q.value=[{prop:"orderTitle",label:"培训标题",type:"input"},{prop:"trainingType",label:"培训类型",type:"input"},{prop:"trainingLevel",label:"培训级别",type:"input"}],j.value=!0}catch(i){j.value=!1,console.error("初始化配置失败:",i)}},T=async()=>{$.value=!0,G.value=!0;try{let i={...N.value};F.value.createTime&&Array.isArray(F.value.createTime)&&F.value.createTime.length===2&&(i=ne.addDateRange(i,F.value.createTime));const[l,d]=await Promise.all([ze(i),Me()]),_=l.rows||[],n=d.data||[];H.value=l.total||0,console.log("所有培训订单数据:",_),console.log("已申请机构申请数据:",n);const p=new Map;n.forEach(f=>{p.set(f.orderId,f)}),z.value=_.map(f=>{const y=p.get(f.orderId);return{...f,institutionApplication:y||null,applicationStatus:y?y.applicationStatus:null,canApply:!y||["2","3"].includes(y.applicationStatus)}}),console.log("最终培训列表:",z.value)}catch(i){x.error("获取培训列表失败"),console.error(i)}finally{$.value=!1,G.value=!1}},de=i=>{F.value={...i};const{createTime:l,...d}=i||{};Object.assign(N.value,d),N.value.pageNum=1,T()},pe=()=>{N.value={pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"},F.value={},T()},ce=i=>{N.value.pageNum=i,T()},me=i=>{N.value.pageSize=i,N.value.pageNum=1,T()},fe=()=>{},ge=()=>{T()},_e=i=>{q.value=i,o.orderId=i.orderId,i.institutionApplication?(Object.assign(o,{institutionName:i.institutionApplication.institutionName,institutionCode:i.institutionApplication.institutionCode,legalPerson:i.institutionApplication.legalPerson,contactPerson:i.institutionApplication.contactPerson,contactPhone:i.institutionApplication.contactPhone,contactEmail:i.institutionApplication.contactEmail,institutionAddress:i.institutionApplication.institutionAddress,institutionType:i.institutionApplication.institutionType,establishedDate:i.institutionApplication.establishedDate,registeredCapital:i.institutionApplication.registeredCapital,businessScope:i.institutionApplication.businessScope,trainingExperience:i.institutionApplication.trainingExperience,trainingCapacity:i.institutionApplication.trainingCapacity,trainingPlan:i.institutionApplication.trainingPlan,teacherInfo:i.institutionApplication.teacherInfo,facilityInfo:i.institutionApplication.facilityInfo,applicationNote:""}),S.value.forEach(l=>{const d=i.institutionApplication[l.field];if(d)try{let _=JSON.parse(d)||[];l.files=_.map(n=>({name:n.name||n.fileName||n.sourceFileName,fileName:n.fileName||n.name||n.sourceFileName,sourceFileName:n.sourceFileName||n.name||n.fileName,url:n.url||n.filePath,filePath:n.filePath||n.url,uid:n.uid||new Date().getTime()+Math.random()}))}catch{l.files=[]}else l.files=[]})):(Object.assign(o,{orderId:i.orderId,institutionName:"",institutionCode:"",legalPerson:"",contactPerson:"",contactPhone:"",contactEmail:"",institutionAddress:"",institutionType:"",establishedDate:null,registeredCapital:null,businessScope:"",trainingExperience:"",trainingCapacity:"",trainingPlan:"",teacherInfo:"",facilityInfo:"",applicationNote:""}),Ne()),Pe(),I.value=!0},ve=()=>{O.value&&O.value.validate(async i=>{if(i){if(S.value.filter(d=>d.required&&(!d.files||d.files.length===0)).length>0){x.error("请上传所有必需的材料文件");return}R.value=!0;try{const d={...o};S.value.forEach(n=>{n.files&&n.files.length>0&&(d[n.field]=JSON.stringify(n.files.map(p=>({name:p.name||p.fileName||p.sourceFileName,fileName:p.fileName||p.name||p.sourceFileName,sourceFileName:p.sourceFileName||p.name||p.fileName,url:p.url||p.filePath,filePath:p.filePath||p.url}))))}),q.value.institutionApplication?(d.applicationId=q.value.institutionApplication.applicationId,d.applicationStatus="0",await Re(d),x.success("重新申请提交成功，请等待审核")):(await Oe(d),x.success("申请提交成功，请等待审核")),I.value=!1,await T()}catch(d){x.error(d.msg||"申请提交失败，请稍后重试")}finally{R.value=!1}}})},he=i=>{c.value=i,console.log("申请数据:",i),X.value.forEach(l=>{const d=i[l.field];if(console.log(`${l.name} 文件数据:`,d),d)try{let _=JSON.parse(d)||[];l.files=_.map(n=>({name:n.name||n.fileName||n.sourceFileName,fileName:n.fileName||n.name||n.sourceFileName,sourceFileName:n.sourceFileName||n.name||n.fileName,url:n.url||n.filePath,filePath:n.filePath||n.url})),console.log(`${l.name} 解析后文件:`,l.files)}catch(_){console.error(`${l.name} JSON解析失败:`,_),l.files=[]}else l.files=[]}),M.value=!0},ye=i=>{const l=new Date,d=i.registrationDeadline?new Date(i.registrationDeadline):null;return!(d&&l>d)},be=i=>({技能培训:"primary",管理培训:"success",安全培训:"warning",专业培训:"info"})[i]||"default",Y=i=>({0:"warning",1:"success",2:"danger",3:"info"})[i]||"info",Z=i=>({0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"})[i]||"未知",k=i=>i?new Date(i).toLocaleDateString("zh-CN"):"--",Ve=(i,l)=>{S.value[l].files=i.fileList||[]},Ne=()=>{S.value.forEach(i=>{i.files=[]})},Pe=()=>{S.value.forEach(i=>{i.files||(i.files=[])})};return(i,l)=>{var ae;const d=h("el-button"),_=h("el-tag"),n=h("el-empty"),p=h("el-input"),f=h("el-form-item"),y=h("el-col"),E=h("el-row"),U=h("el-option"),Ae=h("el-select"),Ce=h("el-date-picker"),Se=h("el-input-number"),ee=h("el-icon"),Te=h("el-form"),te=h("el-dialog"),g=h("el-descriptions-item"),J=h("el-descriptions");return m(),b("div",je,[Je,j.value?(m(),V($e,{key:0,columns:K.value,data:z.value,loading:$.value,showIndex:!0,searchColumns:Q.value,showOperation:!0,operationLabel:"操作",operationWidth:"300",fixedOperation:!0,ref_key:"tableListRef",ref:oe,onSearch:de,onReset:pe,defaultPage:{pageSize:L(N).pageSize,currentPage:L(N).pageNum,total:H.value},onCurrentChange:ce,onSizeChange:me,onSelectionChange:fe},{"menu-left":t(()=>[e(d,{type:"primary",class:"custom-btn",onClick:ge},{default:t(()=>[s("刷新")]),_:1})]),trainingType:t(({row:a})=>[e(_,{type:be(a.trainingType),size:"small"},{default:t(()=>[s(u(a.trainingType),1)]),_:2},1032,["type"])]),trainingFee:t(({row:a})=>[r("span",Be,u(a.trainingFee?"￥"+a.trainingFee:"面议"),1)]),trainingTime:t(({row:a})=>[s(u(k(a.startDate))+" 至 "+u(k(a.endDate)),1)]),registrationDeadline:t(({row:a})=>[s(u(k(a.registrationDeadline)),1)]),applicationStatus:t(({row:a})=>[a.institutionApplication&&a.institutionApplication.applicationStatus?(m(),V(_,{key:0,type:Y(a.institutionApplication.applicationStatus),size:"small"},{default:t(()=>[s(u(Z(a.institutionApplication.applicationStatus)),1)]),_:2},1032,["type"])):(m(),b("span",We,"未申请"))]),applicationTime:t(({row:a})=>[s(u(a.institutionApplication?k(a.institutionApplication.applicationTime):"--"),1)]),menu:t(({row:a})=>[r("div",Ge,[!a.institutionApplication||["2","3"].includes(a.applicationStatus)?(m(),V(d,{key:0,type:"primary",link:"",onClick:w=>_e(a),disabled:!ye(a)},{default:t(()=>[s(u(a.institutionApplication?"重新申请":"立即申请"),1)]),_:2},1032,["onClick","disabled"])):C("",!0),a.institutionApplication&&!["2","3"].includes(a.applicationStatus)?(m(),V(d,{key:1,type:"primary",link:"",disabled:""},{default:t(()=>[s(" 已申请 ")]),_:1})):C("",!0),a.institutionApplication?(m(),V(d,{key:2,type:"success",link:"",onClick:w=>he(a.institutionApplication)},{default:t(()=>[s(" 查看申请 ")]),_:2},1032,["onClick"])):C("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(m(),b("div",He,[e(n,{description:"正在加载表格配置..."})])),e(te,{modelValue:I.value,"onUpdate:modelValue":l[17]||(l[17]=a=>I.value=a),title:`机构申请 - ${(ae=q.value)==null?void 0:ae.orderTitle}`,width:"1000px","close-on-click-modal":!1,"append-to-body":""},{footer:t(()=>[r("div",dt,[e(d,{onClick:l[16]||(l[16]=a=>I.value=!1)},{default:t(()=>[s("取消")]),_:1}),e(d,{type:"primary",loading:R.value,onClick:ve},{default:t(()=>[s(" 提交申请 ")]),_:1},8,["loading"])])]),default:t(()=>[r("div",Ke,[e(Te,{ref_key:"applyFormRef",ref:O,model:o,rules:re,"label-width":"120px"},{default:t(()=>[r("div",Qe,[Xe,e(E,{gutter:20},{default:t(()=>[e(y,{span:12},{default:t(()=>[e(f,{label:"机构名称",prop:"institutionName"},{default:t(()=>[e(p,{modelValue:o.institutionName,"onUpdate:modelValue":l[0]||(l[0]=a=>o.institutionName=a),placeholder:"请输入机构名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:t(()=>[e(f,{label:"机构代码",prop:"institutionCode"},{default:t(()=>[e(p,{modelValue:o.institutionCode,"onUpdate:modelValue":l[1]||(l[1]=a=>o.institutionCode=a),placeholder:"统一社会信用代码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(E,{gutter:20},{default:t(()=>[e(y,{span:12},{default:t(()=>[e(f,{label:"法定代表人",prop:"legalPerson"},{default:t(()=>[e(p,{modelValue:o.legalPerson,"onUpdate:modelValue":l[2]||(l[2]=a=>o.legalPerson=a),placeholder:"请输入法定代表人"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:t(()=>[e(f,{label:"机构类型",prop:"institutionType"},{default:t(()=>[e(Ae,{modelValue:o.institutionType,"onUpdate:modelValue":l[3]||(l[3]=a=>o.institutionType=a),placeholder:"请选择机构类型",style:{width:"100%"}},{default:t(()=>[e(U,{label:"企业",value:"企业"}),e(U,{label:"事业单位",value:"事业单位"}),e(U,{label:"社会组织",value:"社会组织"}),e(U,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(E,{gutter:20},{default:t(()=>[e(y,{span:12},{default:t(()=>[e(f,{label:"成立时间",prop:"establishedDate"},{default:t(()=>[e(Ce,{modelValue:o.establishedDate,"onUpdate:modelValue":l[4]||(l[4]=a=>o.establishedDate=a),type:"date",placeholder:"选择成立时间",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:t(()=>[e(f,{label:"注册资本",prop:"registeredCapital"},{default:t(()=>[e(Se,{modelValue:o.registeredCapital,"onUpdate:modelValue":l[5]||(l[5]=a=>o.registeredCapital=a),min:0,precision:2,placeholder:"万元",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(f,{label:"机构地址",prop:"institutionAddress"},{default:t(()=>[e(p,{modelValue:o.institutionAddress,"onUpdate:modelValue":l[6]||(l[6]=a=>o.institutionAddress=a),placeholder:"请输入机构详细地址"},null,8,["modelValue"])]),_:1}),e(f,{label:"经营范围",prop:"businessScope"},{default:t(()=>[e(p,{modelValue:o.businessScope,"onUpdate:modelValue":l[7]||(l[7]=a=>o.businessScope=a),type:"textarea",rows:3,placeholder:"请输入经营范围"},null,8,["modelValue"])]),_:1})]),r("div",Ye,[Ze,e(E,{gutter:20},{default:t(()=>[e(y,{span:8},{default:t(()=>[e(f,{label:"联系人",prop:"contactPerson"},{default:t(()=>[e(p,{modelValue:o.contactPerson,"onUpdate:modelValue":l[8]||(l[8]=a=>o.contactPerson=a),placeholder:"请输入联系人"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:8},{default:t(()=>[e(f,{label:"联系电话",prop:"contactPhone"},{default:t(()=>[e(p,{modelValue:o.contactPhone,"onUpdate:modelValue":l[9]||(l[9]=a=>o.contactPhone=a),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:8},{default:t(()=>[e(f,{label:"联系邮箱",prop:"contactEmail"},{default:t(()=>[e(p,{modelValue:o.contactEmail,"onUpdate:modelValue":l[10]||(l[10]=a=>o.contactEmail=a),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),r("div",et,[tt,e(f,{label:"培训经验",prop:"trainingExperience"},{default:t(()=>[e(p,{modelValue:o.trainingExperience,"onUpdate:modelValue":l[11]||(l[11]=a=>o.trainingExperience=a),type:"textarea",rows:4,placeholder:"请详细描述您的培训经验，包括培训年限、培训领域、培训规模等"},null,8,["modelValue"])]),_:1}),e(f,{label:"培训能力",prop:"trainingCapacity"},{default:t(()=>[e(p,{modelValue:o.trainingCapacity,"onUpdate:modelValue":l[12]||(l[12]=a=>o.trainingCapacity=a),type:"textarea",rows:4,placeholder:"请详细描述您的培训能力，包括培训体系、培训方法、培训效果等"},null,8,["modelValue"])]),_:1}),e(f,{label:"培训计划",prop:"trainingPlan"},{default:t(()=>[e(p,{modelValue:o.trainingPlan,"onUpdate:modelValue":l[13]||(l[13]=a=>o.trainingPlan=a),type:"textarea",rows:4,placeholder:"请详细描述针对此培训项目的具体培训计划"},null,8,["modelValue"])]),_:1}),e(f,{label:"师资信息",prop:"teacherInfo"},{default:t(()=>[e(p,{modelValue:o.teacherInfo,"onUpdate:modelValue":l[14]||(l[14]=a=>o.teacherInfo=a),type:"textarea",rows:4,placeholder:"请详细描述师资队伍情况，包括讲师数量、资质、经验等"},null,8,["modelValue"])]),_:1}),e(f,{label:"设施设备",prop:"facilityInfo"},{default:t(()=>[e(p,{modelValue:o.facilityInfo,"onUpdate:modelValue":l[15]||(l[15]=a=>o.facilityInfo=a),type:"textarea",rows:3,placeholder:"请描述培训场地、设备等硬件设施情况"},null,8,["modelValue"])]),_:1})]),r("div",at,[lt,r("div",it,[(m(!0),b(B,null,W(S.value,(a,w)=>(m(),b("div",{class:De(["material-item",{"required-material":a.required,"optional-material":!a.required}]),key:w},[r("div",nt,[r("div",ot,[r("div",st,[e(ee,{class:"material-icon"},{default:t(()=>[e(L(ie))]),_:1}),r("span",null,u(a.name),1)]),r("div",rt,[a.required?(m(),V(_,{key:0,type:"danger",size:"small"},{default:t(()=>[s("必需")]),_:1})):(m(),V(_,{key:1,type:"success",size:"small"},{default:t(()=>[s("可选")]),_:1}))])])]),r("div",ut,[e(qe,{value:a.files,"onUpdate:value":A=>a.files=A,limit:5,"file-size":10,"file-type":["pdf","doc","docx","jpg","jpeg","png"],"is-show-tip":!0,onFileLoad:A=>Ve(A,w)},null,8,["value","onUpdate:value","onFileLoad"])])],2))),128))])])]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),e(te,{modelValue:M.value,"onUpdate:modelValue":l[18]||(l[18]=a=>M.value=a),title:"申请详情",width:"1000px","append-to-body":"","destroy-on-close":""},{default:t(()=>[c.value?(m(),b("div",pt,[r("div",ct,[mt,e(J,{column:2,border:""},{default:t(()=>[e(g,{label:"机构名称"},{default:t(()=>[s(u(c.value.institutionName),1)]),_:1}),e(g,{label:"机构代码"},{default:t(()=>[s(u(c.value.institutionCode),1)]),_:1}),e(g,{label:"法定代表人"},{default:t(()=>[s(u(c.value.legalPerson),1)]),_:1}),e(g,{label:"机构类型"},{default:t(()=>[s(u(c.value.institutionType),1)]),_:1}),e(g,{label:"联系人"},{default:t(()=>[s(u(c.value.contactPerson),1)]),_:1}),e(g,{label:"联系电话"},{default:t(()=>[s(u(c.value.contactPhone),1)]),_:1}),e(g,{label:"联系邮箱"},{default:t(()=>[s(u(c.value.contactEmail),1)]),_:1}),e(g,{label:"成立时间"},{default:t(()=>[s(u(k(c.value.establishedDate)),1)]),_:1}),e(g,{label:"注册资本"},{default:t(()=>[s(u(c.value.registeredCapital)+"万元",1)]),_:1}),e(g,{label:"申请状态"},{default:t(()=>[e(_,{type:Y(c.value.applicationStatus)},{default:t(()=>[s(u(Z(c.value.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),e(g,{label:"申请时间"},{default:t(()=>[s(u(k(c.value.applicationTime)),1)]),_:1}),e(g,{label:"机构地址",span:2},{default:t(()=>[s(u(c.value.institutionAddress),1)]),_:1})]),_:1})]),r("div",ft,[gt,e(J,{column:1,border:""},{default:t(()=>[e(g,{label:"经营范围"},{default:t(()=>[s(u(c.value.businessScope||"--"),1)]),_:1}),e(g,{label:"培训经验"},{default:t(()=>[s(u(c.value.trainingExperience||"--"),1)]),_:1}),e(g,{label:"培训能力"},{default:t(()=>[s(u(c.value.trainingCapacity||"--"),1)]),_:1}),e(g,{label:"培训计划"},{default:t(()=>[s(u(c.value.trainingPlan||"--"),1)]),_:1}),e(g,{label:"师资信息"},{default:t(()=>[s(u(c.value.teacherInfo||"--"),1)]),_:1}),e(g,{label:"设施设备"},{default:t(()=>[s(u(c.value.facilityInfo||"--"),1)]),_:1}),c.value.applicationNote?(m(),V(g,{key:0,label:"申请备注"},{default:t(()=>[s(u(c.value.applicationNote),1)]),_:1})):C("",!0)]),_:1})]),r("div",_t,[vt,r("div",ht,[(m(!0),b(B,null,W(X.value,(a,w)=>(m(),b("div",{class:"material-item",key:w},[r("div",yt,[r("div",bt,[r("div",Vt,[e(ee,{class:"material-icon"},{default:t(()=>[e(L(ie))]),_:1}),r("span",null,u(a.name),1)]),r("div",Nt,[a.files&&a.files.length>0?(m(),V(_,{key:0,type:"success",size:"small"},{default:t(()=>[s(" 已上传 "+u(a.files.length)+" 个文件 ",1)]),_:2},1024)):(m(),V(_,{key:1,type:"info",size:"small"},{default:t(()=>[s("未上传")]),_:1}))])])]),a.files&&a.files.length>0?(m(),b("div",Pt,[r("div",At,[(m(!0),b(B,null,W(a.files,(A,ke)=>(m(),b("div",{class:"file-card",key:ke},[e(Ee,{file:{filePath:A.url||A.filePath,sourceFileName:A.name||A.fileName}},null,8,["file"])]))),128))])])):C("",!0)]))),128))])]),c.value.applicationStatus!=="0"?(m(),b("div",Ct,[St,e(J,{column:2,border:""},{default:t(()=>[e(g,{label:"审核时间"},{default:t(()=>[s(u(k(c.value.reviewTime)),1)]),_:1}),e(g,{label:"审核人"},{default:t(()=>[s(u(c.value.reviewer||"--"),1)]),_:1}),c.value.reviewComment?(m(),V(g,{key:0,label:"审核意见",span:2},{default:t(()=>[s(u(c.value.reviewComment),1)]),_:1})):C("",!0)]),_:1})])):C("",!0)])):C("",!0)]),_:1},8,["modelValue"])])}}},xt=Fe(Tt,[["__scopeId","data-v-35256650"]]);export{xt as default};
