import{Y as x,_ as re,C as ce,d as ue,r as n,D as ie,I as pe,e as me,f as $,K as de,c as b,o as s,l as m,i as g,k as l,h as ye,G as h,m as I,p as u,t as v,j as P}from"./index-DP10CBaW.js";import{e as ge}from"./columnUtils-DYlA-XL_.js";import{T as he}from"./index-BWWetMd6.js";import ve from"./EmploymentFormDialog---Mcwsnl.js";function fe(r){return x({url:"/place/employment/list",method:"get",params:r})}function be(r){return x({url:"/place/employment",method:"post",data:r})}function _e(r){return x({url:"/place/employment",method:"put",data:r})}function Ce(r){return x({url:"/place/employment/"+r,method:"delete"})}function we(){return Promise.resolve({column:[{prop:"employmentId",label:"用工ID",width:80,align:"center",showColumn:!0},{prop:"title",label:"用工标题",minWidth:200,align:"left",showOverflowTooltip:!0,showColumn:!0,search:!0,type:"input",placeholder:"请输入用工标题"},{prop:"employmentType",label:"用工类型",width:100,align:"center",slot:"employmentType",showColumn:!0,search:!0,type:"select",placeholder:"请选择用工类型",dicData:[{label:"日结",value:"日结"},{label:"周结",value:"周结"},{label:"月结",value:"月结"},{label:"计件",value:"计件"}]},{prop:"workCategory",label:"工作类别",width:120,align:"center",showColumn:!0,search:!0,type:"select",placeholder:"请选择工作类别",dicData:[{label:"服务员",value:"服务员"},{label:"保洁",value:"保洁"},{label:"搬运工",value:"搬运工"},{label:"销售",value:"销售"},{label:"厨师助手",value:"厨师助手"},{label:"快递员",value:"快递员"},{label:"保安",value:"保安"},{label:"临时工",value:"临时工"}]},{prop:"salaryRange",label:"薪资范围",width:150,align:"center",slot:"salaryRange",showColumn:!0},{prop:"workLocation",label:"工作地点",width:150,align:"center",showOverflowTooltip:!0,showColumn:!0},{prop:"recruitCount",label:"招聘人数",width:100,align:"center",showColumn:!0},{prop:"urgencyLevel",label:"紧急程度",width:100,align:"center",slot:"urgencyLevel",showColumn:!0,search:!0,type:"select",placeholder:"请选择紧急程度",dicData:[{label:"紧急",value:"urgent"},{label:"高",value:"high"},{label:"普通",value:"normal"},{label:"低",value:"low"}]},{prop:"status",label:"状态",width:100,align:"center",slot:"status",showColumn:!0,search:!0,type:"select",placeholder:"请选择状态",dicData:[{label:"草稿",value:"draft"},{label:"已发布",value:"published"},{label:"已暂停",value:"paused"},{label:"已关闭",value:"closed"},{label:"已完成",value:"completed"}]},{prop:"createTime",label:"创建时间",width:180,align:"center",showColumn:!0,formatter:r=>r.createTime?r.createTime.substring(0,16):""}]})}const ke={class:"employment-container app-container"},Te={key:0,class:"salary-range"},xe={key:1,class:"salary-range"},Se={key:2,class:"salary-range"},$e={class:"operation-btns"},De={key:1,class:"loading-placeholder"},Me=ce({name:"EmploymentInfo"}),Le=Object.assign(Me,{setup(r){const{proxy:o}=ue(),_=n([]),S=n(!0),R=n([]),N=n(!0),O=n(!0),C=n(0),D=n([]),M=n([]),w=n(!1),L=n(!1),F=n({dialogWidth:"1200px",dialogHeight:"80vh"}),z=n(null),k=n(null),E=n([]),d=n({}),V=ie({queryParams:{pageNum:1,pageSize:10,title:void 0,employmentType:void 0,workCategory:void 0,salaryType:void 0,regionCode:void 0,urgencyLevel:void 0,status:void 0}}),{queryParams:i}=pe(V);me(async()=>{await B(),p()});async function B(){try{const e=await we(),{tableColumns:a,searchColumns:c,formFields:f}=ge(e);D.value=a,M.value=c,E.value=f,L.value=!0}catch(e){console.error("初始化表格配置失败:",e),o.$modal.msgError("表格配置加载失败")}}function p(){w.value=!0,S.value=!0;let e={...i.value};d.value.createTime&&Array.isArray(d.value.createTime)&&d.value.createTime.length===2&&(e=o.addDateRange(e,d.value.createTime)),fe(e).then(a=>{w.value=!1,S.value=!1,a.code===200?(_.value=a.rows||[],C.value=a.total||0):(_.value=[],C.value=0,o.$modal.msgError(a.msg||"获取用工信息列表失败"))}).catch(a=>{w.value=!1,S.value=!1,_.value=[],C.value=0,console.error("获取用工信息列表失败:",a),o.$modal.msgError("获取用工信息列表失败")})}const A=e=>{d.value=e,i.value.pageNum=1,p()},U=()=>{d.value={},i.value.pageNum=1,p()},W=e=>{i.value.pageNum=e,p()},j=e=>{i.value.pageSize=e,i.value.pageNum=1,p()},q=e=>{R.value=e.map(a=>a.employmentId),N.value=e.length!==1,O.value=!e.length},G=()=>{k.value.openDialog("add",{})},H=e=>{k.value.openDialog("view",e)},K=e=>{k.value.openDialog("edit",e)},Y=async e=>{try{await o.$modal.confirm("确认要发布该用工信息吗？"),o.$modal.msgSuccess("发布成功"),p()}catch(a){console.error("发布失败:",a)}},J=async e=>{try{await o.$modal.confirm("确认要暂停该用工信息吗？"),o.$modal.msgSuccess("暂停成功"),p()}catch(a){console.error("暂停失败:",a)}},Q=async e=>{try{await o.$modal.confirm("确认要删除该用工信息吗？"),await Ce(e.employmentId),o.$modal.msgSuccess("删除成功"),p()}catch(a){console.error("删除失败:",a)}},X=()=>{o.download("place/employment/export",{...i.value},`employment_${new Date().getTime()}.xlsx`)},Z=async(e,a)=>{try{a==="add"?(await be(e),o.$modal.msgSuccess("新增成功")):a==="edit"&&(await _e(e),o.$modal.msgSuccess("修改成功")),p()}catch(c){console.error("操作失败:",c),o.$modal.msgError("操作失败")}},ee=()=>{},ae=e=>({日结:"success",周结:"primary",月结:"warning",计件:"info"})[e]||"default",te=e=>({urgent:"danger",high:"warning",normal:"info",low:"success"})[e]||"info",le=e=>({urgent:"紧急",high:"高",normal:"普通",low:"低"})[e]||"普通",ne=e=>({draft:"info",published:"success",paused:"warning",closed:"danger",completed:"primary"})[e]||"info",oe=e=>({draft:"草稿",published:"已发布",paused:"已暂停",closed:"已关闭",completed:"已完成"})[e]||"未知";return(e,a)=>{const c=$("el-button"),f=$("el-tag"),se=$("el-empty"),y=de("hasPermi");return s(),b("div",ke,[L.value?(s(),m(he,{key:0,columns:D.value,data:_.value,loading:w.value,showIndex:!0,searchColumns:M.value,showOperation:!0,operationLabel:"操作",operationWidth:"280",fixedOperation:!0,ref_key:"tableListRef",ref:z,onSearch:A,onReset:U,defaultPage:{pageSize:P(i).pageSize,currentPage:P(i).pageNum,total:C.value},onCurrentChange:W,onSizeChange:j,onSelectionChange:q},{"menu-left":l(()=>[h((s(),m(c,{type:"primary",class:"custom-btn",onClick:G},{default:l(()=>[u("新 增")]),_:1})),[[y,["place:employment:add"]]]),h((s(),m(c,{type:"warning",plain:"",class:"custom-btn",onClick:X},{default:l(()=>[u("导 出")]),_:1})),[[y,["place:employment:export"]]])]),employmentType:l(({row:t})=>[g(f,{type:ae(t.employmentType)},{default:l(()=>[u(v(t.employmentType),1)]),_:2},1032,["type"])]),salaryRange:l(({row:t})=>[t.salaryMin&&t.salaryMax?(s(),b("span",Te," ¥"+v(t.salaryMin)+"-"+v(t.salaryMax),1)):t.salaryMin?(s(),b("span",xe," ¥"+v(t.salaryMin)+"起 ",1)):(s(),b("span",Se,"面议"))]),urgencyLevel:l(({row:t})=>[g(f,{type:te(t.urgencyLevel)},{default:l(()=>[u(v(le(t.urgencyLevel)),1)]),_:2},1032,["type"])]),status:l(({row:t})=>[g(f,{type:ne(t.status)},{default:l(()=>[u(v(oe(t.status)),1)]),_:2},1032,["type"])]),menu:l(({row:t})=>[ye("div",$e,[g(c,{type:"primary",link:"",onClick:T=>H(t)},{default:l(()=>[u("查看")]),_:2},1032,["onClick"]),h((s(),m(c,{type:"primary",link:"",onClick:T=>K(t)},{default:l(()=>[u("编辑")]),_:2},1032,["onClick"])),[[y,["place:employment:edit"]]]),t.status==="draft"?h((s(),m(c,{key:0,type:"success",link:"",onClick:T=>Y(t)},{default:l(()=>[u("发布")]),_:2},1032,["onClick"])),[[y,["place:employment:publish"]]]):I("",!0),["draft","published"].includes(t.status)?h((s(),m(c,{key:1,type:"warning",link:"",onClick:T=>J(t)},{default:l(()=>[u("暂停")]),_:2},1032,["onClick"])),[[y,["place:employment:pause"]]]):I("",!0),h((s(),m(c,{type:"danger",link:"",onClick:T=>Q(t)},{default:l(()=>[u("删除")]),_:2},1032,["onClick"])),[[y,["place:employment:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(s(),b("div",De,[g(se,{description:"Loading table configuration..."})])),g(ve,{ref_key:"employmentFormDialogRef",ref:k,formFields:E.value,formOption:F.value,onSubmit:Z,onCancel:ee},null,8,["formFields","formOption"])])}}}),Ne=re(Le,[["__scopeId","data-v-f264b78c"]]);export{Ne as default};
