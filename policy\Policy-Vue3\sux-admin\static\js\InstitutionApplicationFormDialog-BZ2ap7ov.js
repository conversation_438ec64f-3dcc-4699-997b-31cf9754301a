import{_ as se,r as h,D as ne,w as de,f,K as re,l as g,o as u,k as a,h as s,Z as ue,G as pe,c as _,m as y,i as e,L as S,M as k,t as v,p as V,N as ce,j as z,$ as A,a0 as me,a1 as fe,x as ge,y as _e,J as ve}from"./index-DP10CBaW.js";import{l as be}from"./order-ZnAGpiqD.js";const q=w=>(ge("data-v-2de2179b"),w=w(),_e(),w),he={class:"form-section"},ye=q(()=>s("h4",{class:"section-title"},"机构基本信息",-1)),Ve={class:"form-section"},we=q(()=>s("h4",{class:"section-title"},"联系信息",-1)),Ne={class:"form-section"},Pe=q(()=>s("h4",{class:"section-title"},"培训能力",-1)),xe={class:"form-section"},qe=q(()=>s("h4",{class:"section-title"},"申请信息",-1)),Ce={key:0,class:"form-section"},Fe=q(()=>s("h4",{class:"section-title"},"申请材料上传",-1)),Se={class:"required-materials"},Ue={class:"material-header"},Ie={class:"material-info"},ke={class:"material-name"},De={class:"material-status"},Ee={class:"material-upload"},Te={key:1,class:"form-section"},Oe=q(()=>s("h4",{class:"section-title"},"已上传材料",-1)),Le={class:"uploaded-materials"},ze={class:"material-header"},Ae={class:"material-info"},Me={class:"material-name"},je={class:"material-status"},Be={key:0,class:"material-files"},Je={class:"file-grid"},Re={class:"dialog-footer"},$e={__name:"InstitutionApplicationFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(w,{expose:M,emit:j}){const D=j,N=h(!1),E=h(""),d=h("add"),B=h(!1),U=h(!1),C=h(null),T=h([]),t=ne({applicationId:null,orderId:null,institutionName:"",institutionCode:"",legalPerson:"",contactPerson:"",contactPhone:"",contactEmail:"",institutionAddress:"",institutionType:"",establishedDate:null,registeredCapital:null,businessScope:"",trainingExperience:"",trainingCapacity:"",trainingPlan:"",teacherInfo:"",facilityInfo:"",applicationNote:"",applicationStatus:"0"}),J={orderId:[{required:!0,message:"请选择培训订单",trigger:"change"}],institutionName:[{required:!0,message:"请输入机构名称",trigger:"blur"},{min:2,max:200,message:"长度在 2 到 200 个字符",trigger:"blur"}],legalPerson:[{required:!0,message:"请输入法定代表人",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],contactPerson:[{required:!0,message:"请输入联系人",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],contactPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],contactEmail:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],institutionAddress:[{required:!0,message:"请输入机构地址",trigger:"blur"},{min:5,max:500,message:"长度在 5 到 500 个字符",trigger:"blur"}],trainingExperience:[{required:!0,message:"请输入培训经验",trigger:"blur"}],trainingCapacity:[{required:!0,message:"请输入培训能力",trigger:"blur"}],trainingPlan:[{required:!0,message:"请输入培训计划",trigger:"blur"}],teacherInfo:[{required:!0,message:"请输入师资信息",trigger:"blur"}]},P=h([{name:"机构营业执照或组织机构代码证",required:!0,files:[],field:"qualificationFiles"},{name:"培训计划详细方案",required:!1,files:[],field:"trainingPlanFile"},{name:"师资队伍资质证明材料",required:!1,files:[],field:"teacherCertFiles"},{name:"培训场地及设施设备证明",required:!1,files:[],field:"facilityFiles"},{name:"其他相关资质证明材料",required:!1,files:[],field:"otherFiles"}]);de(N,p=>{p&&R()});const R=async()=>{try{const p=await be({orderStatus:"1"});T.value=p.rows||[]}catch(p){console.error("加载培训订单失败:",p)}},$=(p,i,m={})=>{d.value=p,E.value=i,Object.keys(t).forEach(o=>{m[o]!==void 0?t[o]=m[o]:o==="applicationStatus"?t[o]="0":typeof t[o]=="string"?t[o]="":(t[o],t[o]=null)}),P.value.forEach(o=>{const r=m[o.field];if(r)try{let c=JSON.parse(r)||[];o.files=c.map(n=>({name:n.name||n.fileName||n.sourceFileName,fileName:n.fileName||n.name||n.sourceFileName,sourceFileName:n.sourceFileName||n.name||n.fileName,url:n.url||n.filePath,filePath:n.filePath||n.url,uid:n.uid||new Date().getTime()+Math.random()}))}catch{o.files=[]}else o.files=[]}),N.value=!0,C.value&&C.value.clearValidate()},W=()=>{C.value&&C.value.validate(p=>{if(p){if(d.value!=="view"&&P.value.filter(o=>o.required&&(!o.files||o.files.length===0)).length>0){ve.error("请上传所有必需的材料文件");return}U.value=!0;const i={...t};P.value.forEach(m=>{m.files&&m.files.length>0&&(i[m.field]=JSON.stringify(m.files.map(o=>({name:o.name||o.fileName||o.sourceFileName,fileName:o.fileName||o.name||o.sourceFileName,sourceFileName:o.sourceFileName||o.name||o.fileName,url:o.url||o.filePath,filePath:o.filePath||o.url}))))}),D("submit",{type:d.value,data:i})}})},G=()=>{N.value=!1,D("cancel")},H=()=>{U.value=!1,N.value=!1},K=()=>{U.value=!1},Z=p=>({0:"warning",1:"success",2:"danger",3:"info"})[p]||"info",Q=p=>({0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"})[p]||"未知",X=p=>p?new Date(p).toLocaleString("zh-CN"):"--",Y=(p,i)=>{P.value[i].files=p.fileList||[]};return M({openDialog:$,onSubmitSuccess:H,onSubmitError:K}),(p,i)=>{const m=f("el-option"),o=f("el-select"),r=f("el-form-item"),c=f("el-col"),n=f("el-input"),x=f("el-row"),ee=f("el-date-picker"),le=f("el-input-number"),F=f("el-tag"),O=f("el-icon"),ae=f("el-form"),L=f("el-button"),te=f("el-dialog"),ie=re("loading");return u(),g(te,{modelValue:N.value,"onUpdate:modelValue":i[18]||(i[18]=l=>N.value=l),title:E.value,width:w.formOption.dialogWidth||"800px","close-on-click-modal":!1,"append-to-body":"","destroy-on-close":""},{footer:a(()=>[s("div",Re,[e(L,{onClick:G},{default:a(()=>[V(v(d.value==="view"?"关闭":"取消"),1)]),_:1}),d.value!=="view"?(u(),g(L,{key:0,type:"primary",loading:U.value,onClick:W},{default:a(()=>[V(" 确定 ")]),_:1},8,["loading"])):y("",!0)])]),default:a(()=>[s("div",{class:"form-container",style:ue({height:w.formOption.dialogHeight||"auto"})},[pe((u(),g(ae,{ref_key:"formRef",ref:C,model:t,rules:J,"label-width":"120px"},{default:a(()=>[s("div",he,[ye,e(x,{gutter:20},{default:a(()=>[e(c,{span:12},{default:a(()=>[d.value!=="view"?(u(),g(r,{key:0,label:"培训订单",prop:"orderId"},{default:a(()=>[e(o,{modelValue:t.orderId,"onUpdate:modelValue":i[0]||(i[0]=l=>t.orderId=l),placeholder:"请选择培训订单",style:{width:"100%"},disabled:d.value==="edit"},{default:a(()=>[(u(!0),_(S,null,k(T.value,l=>(u(),g(m,{key:l.orderId,label:l.orderTitle,value:l.orderId},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})):(u(),g(r,{key:1,label:"培训订单"},{default:a(()=>[s("span",null,v(t.orderTitle||"--"),1)]),_:1}))]),_:1}),e(c,{span:12},{default:a(()=>[e(r,{label:"机构名称",prop:"institutionName"},{default:a(()=>[e(n,{modelValue:t.institutionName,"onUpdate:modelValue":i[1]||(i[1]=l=>t.institutionName=l),placeholder:"请输入机构名称",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(x,{gutter:20},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(r,{label:"机构代码",prop:"institutionCode"},{default:a(()=>[e(n,{modelValue:t.institutionCode,"onUpdate:modelValue":i[2]||(i[2]=l=>t.institutionCode=l),placeholder:"统一社会信用代码",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(c,{span:12},{default:a(()=>[e(r,{label:"法定代表人",prop:"legalPerson"},{default:a(()=>[e(n,{modelValue:t.legalPerson,"onUpdate:modelValue":i[3]||(i[3]=l=>t.legalPerson=l),placeholder:"请输入法定代表人",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(x,{gutter:20},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(r,{label:"机构类型",prop:"institutionType"},{default:a(()=>[e(o,{modelValue:t.institutionType,"onUpdate:modelValue":i[4]||(i[4]=l=>t.institutionType=l),placeholder:"请选择机构类型",style:{width:"100%"},disabled:d.value==="view"},{default:a(()=>[e(m,{label:"企业",value:"企业"}),e(m,{label:"事业单位",value:"事业单位"}),e(m,{label:"社会组织",value:"社会组织"}),e(m,{label:"其他",value:"其他"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(c,{span:12},{default:a(()=>[e(r,{label:"成立时间",prop:"establishedDate"},{default:a(()=>[e(ee,{modelValue:t.establishedDate,"onUpdate:modelValue":i[5]||(i[5]=l=>t.establishedDate=l),type:"date",placeholder:"选择成立时间",style:{width:"100%"},disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(x,{gutter:20},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(r,{label:"注册资本",prop:"registeredCapital"},{default:a(()=>[e(le,{modelValue:t.registeredCapital,"onUpdate:modelValue":i[6]||(i[6]=l=>t.registeredCapital=l),min:0,precision:2,placeholder:"万元",style:{width:"100%"},disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(c,{span:12},{default:a(()=>[d.value==="view"?(u(),g(r,{key:0,label:"申请状态"},{default:a(()=>[e(F,{type:Z(t.applicationStatus)},{default:a(()=>[V(v(Q(t.applicationStatus)),1)]),_:1},8,["type"])]),_:1})):y("",!0)]),_:1})]),_:1}),e(r,{label:"机构地址",prop:"institutionAddress"},{default:a(()=>[e(n,{modelValue:t.institutionAddress,"onUpdate:modelValue":i[7]||(i[7]=l=>t.institutionAddress=l),placeholder:"请输入机构详细地址",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1}),e(r,{label:"经营范围",prop:"businessScope"},{default:a(()=>[e(n,{modelValue:t.businessScope,"onUpdate:modelValue":i[8]||(i[8]=l=>t.businessScope=l),type:"textarea",rows:3,placeholder:"请输入经营范围",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),s("div",Ve,[we,e(x,{gutter:20},{default:a(()=>[e(c,{span:8},{default:a(()=>[e(r,{label:"联系人",prop:"contactPerson"},{default:a(()=>[e(n,{modelValue:t.contactPerson,"onUpdate:modelValue":i[9]||(i[9]=l=>t.contactPerson=l),placeholder:"请输入联系人",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(c,{span:8},{default:a(()=>[e(r,{label:"联系电话",prop:"contactPhone"},{default:a(()=>[e(n,{modelValue:t.contactPhone,"onUpdate:modelValue":i[10]||(i[10]=l=>t.contactPhone=l),placeholder:"请输入联系电话",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(c,{span:8},{default:a(()=>[e(r,{label:"联系邮箱",prop:"contactEmail"},{default:a(()=>[e(n,{modelValue:t.contactEmail,"onUpdate:modelValue":i[11]||(i[11]=l=>t.contactEmail=l),placeholder:"请输入联系邮箱",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1})]),s("div",Ne,[Pe,e(r,{label:"培训经验",prop:"trainingExperience"},{default:a(()=>[e(n,{modelValue:t.trainingExperience,"onUpdate:modelValue":i[12]||(i[12]=l=>t.trainingExperience=l),type:"textarea",rows:4,placeholder:"请详细描述您的培训经验，包括培训年限、培训领域、培训规模等",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1}),e(r,{label:"培训能力",prop:"trainingCapacity"},{default:a(()=>[e(n,{modelValue:t.trainingCapacity,"onUpdate:modelValue":i[13]||(i[13]=l=>t.trainingCapacity=l),type:"textarea",rows:4,placeholder:"请详细描述您的培训能力，包括培训体系、培训方法、培训效果等",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1}),e(r,{label:"培训计划",prop:"trainingPlan"},{default:a(()=>[e(n,{modelValue:t.trainingPlan,"onUpdate:modelValue":i[14]||(i[14]=l=>t.trainingPlan=l),type:"textarea",rows:4,placeholder:"请详细描述针对此培训项目的具体培训计划",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1}),e(r,{label:"师资信息",prop:"teacherInfo"},{default:a(()=>[e(n,{modelValue:t.teacherInfo,"onUpdate:modelValue":i[15]||(i[15]=l=>t.teacherInfo=l),type:"textarea",rows:4,placeholder:"请详细描述师资队伍情况，包括讲师数量、资质、经验等",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1}),e(r,{label:"设施设备",prop:"facilityInfo"},{default:a(()=>[e(n,{modelValue:t.facilityInfo,"onUpdate:modelValue":i[16]||(i[16]=l=>t.facilityInfo=l),type:"textarea",rows:3,placeholder:"请描述培训场地、设备等硬件设施情况",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),s("div",xe,[qe,e(r,{label:"申请备注",prop:"applicationNote"},{default:a(()=>[e(n,{modelValue:t.applicationNote,"onUpdate:modelValue":i[17]||(i[17]=l=>t.applicationNote=l),type:"textarea",rows:3,placeholder:"请输入申请备注（选填）",disabled:d.value==="view"},null,8,["modelValue","disabled"])]),_:1})]),d.value!=="view"?(u(),_("div",Ce,[Fe,s("div",Se,[(u(!0),_(S,null,k(P.value,(l,I)=>(u(),_("div",{class:ce(["material-item",{"required-material":l.required,"optional-material":!l.required}]),key:I},[s("div",Ue,[s("div",Ie,[s("div",ke,[e(O,{class:"material-icon"},{default:a(()=>[e(z(A))]),_:1}),s("span",null,v(l.name),1)]),s("div",De,[l.required?(u(),g(F,{key:0,type:"danger",size:"small"},{default:a(()=>[V("必需")]),_:1})):(u(),g(F,{key:1,type:"success",size:"small"},{default:a(()=>[V("可选")]),_:1}))])])]),s("div",Ee,[e(me,{value:l.files,"onUpdate:value":b=>l.files=b,limit:5,"file-size":10,"file-type":["pdf","doc","docx","jpg","jpeg","png"],"is-show-tip":!0,onFileLoad:b=>Y(b,I)},null,8,["value","onUpdate:value","onFileLoad"])])],2))),128))])])):y("",!0),d.value==="view"?(u(),_("div",Te,[Oe,s("div",Le,[(u(!0),_(S,null,k(P.value,(l,I)=>(u(),_("div",{class:"material-item",key:I},[s("div",ze,[s("div",Ae,[s("div",Me,[e(O,{class:"material-icon"},{default:a(()=>[e(z(A))]),_:1}),s("span",null,v(l.name),1)]),s("div",je,[l.files&&l.files.length>0?(u(),g(F,{key:0,type:"success",size:"small"},{default:a(()=>[V(" 已上传 "+v(l.files.length)+" 个文件 ",1)]),_:2},1024)):(u(),g(F,{key:1,type:"info",size:"small"},{default:a(()=>[V("未上传")]),_:1}))])])]),l.files&&l.files.length>0?(u(),_("div",Be,[s("div",Je,[(u(!0),_(S,null,k(l.files,(b,oe)=>(u(),_("div",{class:"file-card",key:oe},[e(fe,{file:{filePath:b.url||b.filePath,sourceFileName:b.name||b.fileName}},null,8,["file"])]))),128))])])):y("",!0)]))),128))]),d.value==="view"&&t.applicationStatus!=="0"?(u(),_(S,{key:0},[e(x,{gutter:20},{default:a(()=>[e(c,{span:12},{default:a(()=>[e(r,{label:"审核时间"},{default:a(()=>[s("span",null,v(X(t.reviewTime)),1)]),_:1})]),_:1}),e(c,{span:12},{default:a(()=>[e(r,{label:"审核人"},{default:a(()=>[s("span",null,v(t.reviewer||"--"),1)]),_:1})]),_:1})]),_:1}),t.reviewComment?(u(),g(r,{key:0,label:"审核意见"},{default:a(()=>[s("span",null,v(t.reviewComment),1)]),_:1})):y("",!0)],64)):y("",!0)])):y("",!0)]),_:1},8,["model"])),[[ie,B.value]])],4)]),_:1},8,["modelValue","title","width"])}}},He=se($e,[["__scopeId","data-v-2de2179b"]]);export{He as default};
