import request from '@/utils/request'

// 查询机构招募申请列表
export function listInstitutionRecruitmentApplications(query) {
  return request({
    url: '/recruitment/institution/applications',
    method: 'get',
    params: query
  })
}

// 查询机构招募申请详细
export function getInstitutionRecruitmentApplication(applicationId) {
  return request({
    url: '/recruitment/institution/application/' + applicationId,
    method: 'get'
  })
}

// 新增机构招募申请
export function addInstitutionRecruitmentApplication(data) {
  return request({
    url: '/recruitment/institution/application',
    method: 'post',
    data: data
  })
}

// 修改机构招募申请
export function updateInstitutionRecruitmentApplication(data) {
  return request({
    url: '/recruitment/institution/application',
    method: 'put',
    data: data
  })
}

// 删除机构招募申请
export function delInstitutionRecruitmentApplication(applicationId) {
  return request({
    url: '/recruitment/institution/application/' + applicationId,
    method: 'delete'
  })
}

// 审核机构招募申请
export function reviewInstitutionRecruitmentApplication(applicationId, status, reviewComment) {
  return request({
    url: '/recruitment/institution/application/' + applicationId + '/review',
    method: 'put',
    data: {
      status: status,
      reviewComment: reviewComment
    }
  })
}

// 提交机构招募申请（公开接口）
export function submitInstitutionRecruitmentApplication(data) {
  return request({
    url: '/recruitment/institution/application/submit',
    method: 'post',
    data: data
  })
}

// 获取我的机构招募申请记录
export function getMyInstitutionRecruitmentApplications(query) {
  return request({
    url: '/recruitment/institution/application/my',
    method: 'get',
    params: query
  })
}

// 取消我的机构招募申请
export function cancelMyInstitutionRecruitmentApplication(applicationId) {
  return request({
    url: '/recruitment/institution/application/' + applicationId + '/cancel',
    method: 'put'
  })
}

// 更新机构招募申请（公开接口，用于重新申请）
export function updateInstitutionRecruitmentApplicationPublic(data) {
  return request({
    url: '/recruitment/institution/application/update',
    method: 'put',
    data: data
  })
}

// 根据招募ID获取申请列表
export function getApplicationsByRecruitmentId(recruitmentId, query) {
  return request({
    url: '/recruitment/institution/' + recruitmentId + '/applications',
    method: 'get',
    params: query
  })
}

// 批量审核申请
export function batchReviewApplications(data) {
  return request({
    url: '/recruitment/institution/application/batch/review',
    method: 'put',
    data: data
  })
}

// 选中机构
export function selectInstitutionApplication(applicationId) {
  return request({
    url: '/recruitment/institution/application/' + applicationId + '/select',
    method: 'put'
  })
}

// 取消选中机构
export function unselectInstitutionApplication(applicationId) {
  return request({
    url: '/recruitment/institution/application/' + applicationId + '/unselect',
    method: 'put'
  })
}

// 获取申请统计信息
export function getApplicationStats(recruitmentId) {
  return request({
    url: '/recruitment/institution/' + recruitmentId + '/application/stats',
    method: 'get'
  })
}

// 导出申请列表
export function exportApplications(query) {
  return request({
    url: '/recruitment/institution/application/export',
    method: 'get',
    params: query
  })
}
