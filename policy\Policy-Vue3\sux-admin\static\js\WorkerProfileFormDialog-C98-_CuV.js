import{_ as te,r as d,D as se,A as B,f as r,l as n,o as a,k as u,h as f,Z as re,i as _,c as i,L as g,M as O,p as w,t as F,m as x,n as ne,j as ue,al as pe,R as ce}from"./index-DP10CBaW.js";const de={key:0,class:"skills-input-container"},ie={class:"skills-tags"},me={key:1,class:"no-skills"},_e=["src"],ve={class:"dialog-footer"},be={__name:"WorkerProfileFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(v,{expose:L,emit:M}){const P=v,C=M,b=d(!1),D=d(""),S=d("add"),k=d(!1),y=d(null),l=se({}),W=d({}),h=d(""),p=B(()=>S.value==="view"),j=B(()=>{const s={};return P.formFields.forEach(t=>{t.rules&&t.prop&&(s[t.prop]=t.rules)}),s}),z=s=>{const t=s.type==="image/jpeg"||s.type==="image/png",c=s.size/1024/1024<2;return t||ElMessage.error("头像图片只能是 JPG/PNG 格式!"),c||ElMessage.error("头像图片大小不能超过 2MB!"),t&&c},V=()=>{if(!l.skills)return[];try{return Array.isArray(l.skills)?l.skills:typeof l.skills=="string"?JSON.parse(l.skills):[]}catch(s){return console.warn("解析技能数组失败:",s),[]}},N=()=>{var c;const s=(c=h.value)==null?void 0:c.trim();if(!s)return;const t=V();if(!t.includes(s)){const m=[...t,s];l.skills=JSON.stringify(m)}h.value=""},J=s=>{const t=V();t.splice(s,1),l.skills=JSON.stringify(t)},R=(s,t,c={})=>{S.value=s,D.value=t,Object.keys(l).forEach(m=>{delete l[m]}),Object.assign(l,c),W.value={...c},h.value="",b.value=!0,ce(()=>{y.value&&y.value.clearValidate()})},T=()=>{y.value&&y.value.validate(s=>{s&&(k.value=!0,C("submit",{type:S.value,data:{...l}}))})},G=()=>{b.value=!1,C("cancel")};return L({openDialog:R,onSubmitSuccess:()=>{k.value=!1,b.value=!1},onSubmitError:()=>{k.value=!1}}),(s,t)=>{const c=r("el-divider"),m=r("el-col"),H=r("el-tag"),U=r("el-input"),K=r("el-input-number"),I=r("el-option"),Y=r("el-select"),Z=r("el-switch"),E=r("el-date-picker"),q=r("el-rate"),Q=r("el-icon"),X=r("el-upload"),ee=r("el-form-item"),le=r("el-row"),ae=r("el-form"),$=r("el-button"),oe=r("el-dialog");return a(),n(oe,{modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=e=>b.value=e),title:D.value,width:v.formOption.dialogWidth||"800px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:u(()=>[f("div",ve,[_($,{onClick:G},{default:u(()=>[w("取 消")]),_:1}),p.value?x("",!0):(a(),n($,{key:0,type:"primary",loading:k.value,onClick:T},{default:u(()=>[w(" 确 定 ")]),_:1},8,["loading"]))])]),default:u(()=>[f("div",{class:"form-container",style:re({maxHeight:v.formOption.dialogHeight||"60vh",overflowY:"auto"})},[_(ae,{ref_key:"formRef",ref:y,model:l,rules:j.value,"label-width":v.formOption.labelWidth||"100px"},{default:u(()=>[_(le,{gutter:20},{default:u(()=>[(a(!0),i(g,null,O(v.formFields,e=>(a(),i(g,{key:e.prop},[e.divider?(a(),n(m,{key:0,span:24,class:"divider-col"},{default:u(()=>[_(c,{"content-position":"left"},{default:u(()=>[w(F(e.label),1)]),_:2},1024)]),_:2},1024)):(a(),n(m,{key:1,span:e.span||12,class:"form-col"},{default:u(()=>[_(ee,{label:e.label,prop:e.prop,rules:e.rules},{default:u(()=>[e.prop==="skills"&&e.formslot?(a(),i("div",de,[f("div",ie,[(a(!0),i(g,null,O(V(),(o,A)=>(a(),n(H,{key:A,closable:"","disable-transitions":!1,onClose:ke=>J(A),type:"primary",size:"small",class:"skill-tag"},{default:u(()=>[w(F(o),1)]),_:2},1032,["onClose"]))),128))]),p.value?x("",!0):(a(),n(U,{key:0,modelValue:h.value,"onUpdate:modelValue":t[0]||(t[0]=o=>h.value=o),size:"small",placeholder:"输入技能后按回车添加",class:"skill-input",onKeyup:ne(N,["enter"]),onBlur:N,clearable:""},null,8,["modelValue"])),p.value&&V().length===0?(a(),i("div",me," 暂无技能 ")):x("",!0)])):!e.type||e.type==="input"?(a(),n(U,{key:1,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,placeholder:e.placeholder||`请输入${e.label}`,disabled:p.value||e.disabled,maxlength:e.maxlength,"show-word-limit":e.showWordLimit,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","maxlength","show-word-limit"])):e.type==="textarea"?(a(),n(U,{key:2,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,type:"textarea",placeholder:e.placeholder||`请输入${e.label}`,disabled:p.value||e.disabled,rows:e.minRows||4,maxlength:e.maxlength,"show-word-limit":e.showWordLimit,resize:"vertical"},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","rows","maxlength","show-word-limit"])):e.type==="number"?(a(),n(K,{key:3,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,placeholder:e.placeholder||`请输入${e.label}`,disabled:p.value||e.disabled,min:e.min,max:e.max,precision:e.precision,step:e.step||1,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","min","max","precision","step"])):e.type==="select"?(a(),n(Y,{key:4,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,placeholder:e.placeholder||`请选择${e.label}`,disabled:p.value||e.disabled,clearable:"",style:{width:"100%"}},{default:u(()=>[(a(!0),i(g,null,O(e.dicData,o=>(a(),n(I,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="switch"?(a(),n(Z,{key:5,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,disabled:p.value||e.disabled,"active-value":"0","inactive-value":"1"},null,8,["modelValue","onUpdate:modelValue","disabled"])):e.type==="date"?(a(),n(E,{key:6,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,type:"date",placeholder:e.placeholder||`请选择${e.label}`,disabled:p.value||e.disabled,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="datetime"?(a(),n(E,{key:7,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,type:"datetime",placeholder:e.placeholder||`请选择${e.label}`,disabled:p.value||e.disabled,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="rate"?(a(),n(q,{key:8,modelValue:l[e.prop],"onUpdate:modelValue":o=>l[e.prop]=o,disabled:!0,"show-score":"","text-color":"#ff9900","score-template":"{value}",max:5},null,8,["modelValue","onUpdate:modelValue"])):e.prop==="profilePhoto"?(a(),n(X,{key:9,class:"avatar-uploader",action:"#","show-file-list":!1,"before-upload":z,disabled:p.value},{default:u(()=>[l[e.prop]?(a(),i("img",{key:0,src:l[e.prop],class:"avatar"},null,8,_e)):(a(),n(Q,{key:1,class:"avatar-uploader-icon"},{default:u(()=>[_(ue(pe))]),_:1}))]),_:2},1032,["disabled"])):x("",!0)]),_:2},1032,["label","prop","rules"])]),_:2},1032,["span"]))],64))),128))]),_:1})]),_:1},8,["model","rules","label-width"])],4)]),_:1},8,["modelValue","title","width"])}}},ge=te(be,[["__scopeId","data-v-4be33985"]]);export{ge as default};
