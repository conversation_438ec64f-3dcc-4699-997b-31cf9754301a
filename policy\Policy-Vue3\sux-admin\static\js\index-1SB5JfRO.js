import{g as ce,e as ue}from"./columnUtils-DYlA-XL_.js";import{T as pe}from"./index-BWWetMd6.js";import me from"./TemplateFormDialog-Dl0pFCRv.js";import{_ as he,C as ge,a as fe,d as be,r as o,e as ve,f as W,K as xe,c as D,o as n,l as h,a4 as ye,i as $,k as c,h as V,G as v,p as x,t as _e}from"./index-DP10CBaW.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";const De=[{label:"普通",value:"1"},{label:"高级",value:"2"},{label:"专业",value:"3"}],Te=[{value:"beijing",label:"北京",children:[{value:"chaoyang",label:"朝阳区"},{value:"haidian",label:"海淀区"},{value:"dongcheng",label:"东城区"}]},{value:"shanghai",label:"上海",children:[{value:"huangpu",label:"黄浦区"},{value:"xuhui",label:"徐汇区"},{value:"changning",label:"长宁区"}]}],ke=[{id:"1",value:"1",label:"一级 1",children:[{id:"1-1",value:"1-1",label:"二级 1-1",children:[{id:"1-1-1",value:"1-1-1",label:"三级 1-1-1"}]}]},{id:"2",value:"2",label:"一级 2",children:[{id:"2-1",value:"2-1",label:"二级 2-1"},{id:"2-2",value:"2-2",label:"二级 2-2"}]}],Ce=F=>{const{sys_normal_disable:r,sys_yes_no:M,sys_user_sex:z}=F.useDict("sys_normal_disable","sys_yes_no","sys_user_sex");return{dialogWidth:"1000px",dialogHeight:"70vh",labelWidth:"120px",column:[{label:"用户名称",prop:"userName",type:"input",span:12,search:!0,searchWidth:"200px",placeholder:"请输入用户名称",maxlength:30,showWordLimit:!0,clearable:!0,minWidth:120,sortable:!0,showOverflowTooltip:!0,rules:[{required:!0,message:"请输入用户名称",trigger:"blur"},{min:2,max:20,message:"用户名长度在 2 到 20 个字符",trigger:"blur"}],tip:"用户名用于登录，不可重复"},{label:"密码",prop:"password",type:"password",span:12,addDisplay:!0,editDisplay:!1,viewDisplay:!1,rules:[{required:!0,message:"请输入密码",trigger:"blur"}]},{label:"备注",prop:"remark",type:"textarea",span:24,rows:4,autosize:{minRows:2,maxRows:6},maxlength:500,showWordLimit:!0,placeholder:"请输入备注信息",minWidth:200},{label:"年龄",prop:"age",type:"number",span:12,min:0,max:150,step:1,precision:0,controls:!0,defaultValue:18,minWidth:100},{label:"价格",prop:"price",type:"number-suffix",span:12,min:0,precision:2,suffix:"元",minWidth:120},{label:"状态",prop:"status",type:"select",span:12,search:!0,dicData:r,clearable:!0,filterable:!0,multiple:!1,searchMultiple:!0,collapseTags:!0,minWidth:100,rules:[{required:!0,message:"请选择状态",trigger:"change"}]},{label:"组织架构",prop:"orgId",type:"tree-select",span:12,dicData:ke,checkStrictly:!1,multiple:!1,minWidth:150},{label:"性别",prop:"sex",type:"radio",span:12,search:!0,dicData:z,button:!1,size:"default",minWidth:100},{label:"类型",prop:"type",type:"radio",span:12,dicData:De,button:!0,minWidth:120},{label:"兴趣爱好",prop:"hobbies",type:"checkbox",span:12,dicData:[{label:"阅读",value:"reading"},{label:"运动",value:"sports"},{label:"音乐",value:"music"},{label:"旅行",value:"travel"}],button:!1,minWidth:150},{label:"是否启用",prop:"isEnabled",type:"switch",span:12,activeValue:!0,inactiveValue:!1,activeText:"启用",inactiveText:"禁用",minWidth:100},{label:"出生日期",prop:"birthday",type:"date",span:12,search:!0,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",clearable:!0,minWidth:150},{label:"创建时间",prop:"createTime",type:"datetime",span:12,search:!0,format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",addDisplay:!1,editDisplay:!1,viewDisplay:!0,minWidth:180},{label:"工作时间",prop:"workTime",type:"time",span:12,format:"HH:mm:ss",valueFormat:"HH:mm:ss",minWidth:150},{label:"有效期",prop:"validPeriod",type:"daterange",span:12,search:!0,format:"YYYY-MM-DD",valueFormat:"YYYY-MM-DD",minWidth:250},{label:"主题色",prop:"themeColor",type:"color",span:12,showAlpha:!0,minWidth:120},{label:"评分",prop:"rating",type:"rate",span:12,max:5,allowHalf:!0,defaultValue:0,minWidth:150},{label:"地区",prop:"region",type:"cascader",span:12,dicData:Te,props:{checkStrictly:!1,expandTrigger:"click"},filterable:!0,clearable:!0,minWidth:200},{label:"自定义内容",prop:"customField",formSlot:!0,tableSlot:!0,span:24,minWidth:200},{divider:!0,label:"基础信息",prop:"divider1"},{label:"是否展示扩展信息",prop:"showExtendInfo",type:"select",span:12,search:!0,dicData:M,rules:[{required:!0,message:"请选择是否展示",trigger:"blur"}],minWidth:150,control:(_,I)=>_!=="Y"?{extendInfo:{viewDisplay:!1,addDisplay:!1,editDisplay:!1}}:{extendInfo:{viewDisplay:!0,addDisplay:!0,editDisplay:!0}}},{label:"扩展信息",prop:"extendInfo",type:"textarea",span:24,placeholder:"请输入扩展信息",rows:3,minWidth:200}]}},we={class:"template-container app-container"},Ie={key:1},Se={class:"price-cell"},Ne={key:0,class:"price-text"},Ye={key:1,class:"no-price"},Ee={key:1,class:"loading-placeholder"},We=ge({name:"Template"}),Fe=Object.assign(We,{setup(F){fe();const{proxy:r}=be(),{sys_normal_disable:M,sys_yes_no:z}=r.useDict("sys_normal_disable","sys_yes_no"),_=o([]),I=o([]),T=o(!1),S=o(!1),N=o({dialogWidth:"800px",dialogHeight:"70vh"}),y=o(null),p=o(null),b=o(1),k=o(10),C=o(0),Y=o({}),w=o([]),L=o([]),u=o([{id:1,userName:"张三",password:"123456",remark:"这是一个用于演示的用户信息记录，包含完整的个人资料和偏好设置",age:28,price:299.99,status:"0",orgId:"1-1",sex:"1",type:"2",hobbies:["reading","sports"],isEnabled:!0,birthday:"1995-03-15",createTime:"2024-01-01 09:00:00",workTime:"09:00:00",validPeriod:["2024-01-01","2024-12-31"],themeColor:"#409eff",rating:4.5,region:["beijing","chaoyang"],customField:"自定义内容示例",showExtendInfo:"Y",extendInfo:"这是扩展信息内容，用于展示更多详细信息"},{id:2,userName:"李四",password:"123456",remark:"资深开发工程师，负责系统架构设计和核心功能开发",age:32,price:599,status:"0",orgId:"1-1-1",sex:"1",type:"3",hobbies:["music","travel"],isEnabled:!0,birthday:"1991-07-22",createTime:"2024-01-05 10:15:00",workTime:"08:30:00",validPeriod:["2024-01-05","2024-12-31"],themeColor:"#67c23a",rating:5,region:["shanghai","huangpu"],customField:"高级用户配置",showExtendInfo:"Y",extendInfo:"具有丰富的项目经验，擅长前后端开发"},{id:3,userName:"王五",password:"123456",remark:"产品经理，负责产品规划和需求分析，有着敏锐的市场洞察力",age:30,price:899.5,status:"0",orgId:"2-1",sex:"0",type:"3",hobbies:["reading","music","travel"],isEnabled:!1,birthday:"1993-11-08",createTime:"2024-01-10 11:20:00",workTime:"09:30:00",validPeriod:["2024-01-10","2024-06-30"],themeColor:"#e6a23c",rating:4.2,region:["beijing","haidian"],customField:"产品专家",showExtendInfo:"N",extendInfo:""},{id:4,userName:"赵六",password:"123456",remark:"UI设计师，专注于用户体验设计和界面优化",age:26,price:399,status:"1",orgId:"2-2",sex:"0",type:"2",hobbies:["sports"],isEnabled:!0,birthday:"1997-05-18",createTime:"2024-01-12 08:30:00",workTime:"10:00:00",validPeriod:["2024-01-12","2024-11-30"],themeColor:"#f56c6c",rating:3.8,region:["shanghai","xuhui"],customField:"设计师配置",showExtendInfo:"Y",extendInfo:"熟练掌握各种设计工具，有着独特的审美观念"},{id:5,userName:"孙七",password:"123456",remark:"测试工程师，负责系统测试和质量保证工作",age:25,price:199.99,status:"0",orgId:"1-1",sex:"1",type:"1",hobbies:["reading"],isEnabled:!0,birthday:"1998-09-25",createTime:"2024-01-15 14:45:00",workTime:"09:00:00",validPeriod:["2024-01-15","2024-07-31"],themeColor:"#909399",rating:4,region:["beijing","dongcheng"],customField:"测试专员",showExtendInfo:"N",extendInfo:""},{id:6,userName:"周八",password:"123456",remark:"运维工程师，负责服务器维护和系统部署，保障系统稳定运行",age:29,price:159,status:"0",orgId:"2-1",sex:"1",type:"1",hobbies:["sports","music"],isEnabled:!0,birthday:"1994-12-03",createTime:"2024-01-18 09:10:00",workTime:"24:00:00",validPeriod:["2024-01-18","2025-01-18"],themeColor:"#606266",rating:4.7,region:["shanghai","changning"],customField:"运维专家",showExtendInfo:"Y",extendInfo:"具备丰富的运维经验，能够处理各种突发情况"},{id:7,userName:"吴九",password:"123456",remark:"项目经理，统筹项目进度和资源分配，确保项目按时交付",age:35,price:1299,status:"0",orgId:"1",sex:"0",type:"3",hobbies:["travel","reading"],isEnabled:!0,birthday:"1988-04-12",createTime:"2024-01-20 16:00:00",workTime:"08:00:00",validPeriod:["2024-01-20","2024-12-31"],themeColor:"#409eff",rating:4.9,region:["beijing","chaoyang"],customField:"高级管理",showExtendInfo:"Y",extendInfo:"拥有多年的项目管理经验，善于团队协作和沟通"},{id:8,userName:"郑十",password:"123456",remark:"数据分析师，负责业务数据分析和报表制作",age:27,price:459.99,status:"1",orgId:"2-2",sex:"0",type:"2",hobbies:["reading","music"],isEnabled:!1,birthday:"1996-08-30",createTime:"2024-01-22 13:25:00",workTime:"09:30:00",validPeriod:["2024-01-22","2024-08-31"],themeColor:"#67c23a",rating:3.5,region:["shanghai","huangpu"],customField:"数据专家",showExtendInfo:"N",extendInfo:""}]);let H=9;const R=e=>new Promise(a=>{setTimeout(()=>{let t=[...u.value];e.userName&&(t=t.filter(l=>l.userName.toLowerCase().includes(e.userName.toLowerCase()))),e.sex&&(t=t.filter(l=>l.sex===e.sex)),e.type&&(t=t.filter(l=>l.type===e.type)),e.status&&(t=t.filter(l=>l.status===e.status)),e.showExtendInfo&&(t=t.filter(l=>l.showExtendInfo===e.showExtendInfo)),e.ageMin&&(t=t.filter(l=>l.age>=e.ageMin)),e.ageMax&&(t=t.filter(l=>l.age<=e.ageMax)),e.priceMin&&(t=t.filter(l=>l.price>=e.priceMin)),e.priceMax&&(t=t.filter(l=>l.price<=e.priceMax)),e.createTimeStart&&e.createTimeEnd&&(t=t.filter(l=>{const O=new Date(l.createTime),re=new Date(e.createTimeStart),de=new Date(e.createTimeEnd);return O>=re&&O<=de}));const i=e.pageNum||1,f=e.pageSize||10,d=(i-1)*f,s=d+f,m=t.slice(d,s);a({rows:m,total:t.length})},300)}),E=e=>new Promise(a=>{setTimeout(()=>{const t=u.value.find(i=>i.id==e);a({data:t||{}})},200)}),j=e=>new Promise(a=>{setTimeout(()=>{const t={...e,id:H++,createTime:new Date().toLocaleString("zh-CN")};u.value.unshift(t),a({data:t})},500)}),B=e=>new Promise(a=>{setTimeout(()=>{const t=u.value.findIndex(i=>i.id==e.id);t>-1&&(u.value[t]={...u.value[t],...e,updateTime:new Date().toLocaleString("zh-CN")}),a({data:u.value[t]})},500)}),U=e=>new Promise(a=>{setTimeout(()=>{const t=u.value.findIndex(i=>i.id==e);t>-1&&u.value.splice(t,1),a({success:!0})},300)}),A=(e,a)=>new Promise(t=>{setTimeout(()=>{const i=u.value.find(f=>f.id==e);i&&(i.status=a,i.updateTime=new Date().toLocaleString("zh-CN")),t({success:!0})},300)});ve(()=>{w.value=[],G()});const G=async()=>{try{const e=Ce(r),a=await ce({baseOption:e,proxy:r}),{tableColumns:t,searchColumns:i,formFields:f,formOptions:d}=ue(a);_.value=t,I.value=i,L.value=f,N.value={...N.value,...d},S.value=!0,g()}catch(e){S.value=!1,console.error("初始化配置失败:",e)}},g=()=>{T.value=!0;const e={pageNum:b.value,pageSize:k.value,...Y.value};R(e).then(a=>{const t=(a.rows||[]).filter(i=>i&&i.id&&i.userName);w.value=t,C.value=a.total||0,P(),T.value=!1}).catch(a=>{console.error("加载用户列表失败:",a),w.value=[],C.value=0,T.value=!1})},P=()=>{y.value&&y.value.page&&(y.value.page.total=C.value,y.value.page.currentPage=b.value,y.value.page.pageSize=k.value)},K=e=>{Y.value=e,b.value=1,g()},J=()=>{Y.value={},b.value=1,g()},Q=e=>{b.value=e,g()},X=e=>{k.value=e,b.value=1,g()},Z=(e,a)=>{if(!e||!e.id||!e.userName||e.status===a)return;let t=a==="0"?"启用":"禁用";r.$modal.confirm('确认要"'+t+'""'+e.userName+'"吗?').then(function(){return A(e.id,a)}).then(()=>{r.$modal.msgSuccess(t+"成功"),e.status=a}).catch(function(){e.status=e.status==="0"?"1":"0"})},q=()=>{p.value.openDialog("add","新增用户")},ee=e=>{E(e.id).then(a=>{p.value.openDialog("edit","修改用户",a.data)})},te=e=>{E(e.id).then(a=>{p.value.openDialog("view","用户详细",a.data)})},ae=e=>{E(e.id).then(a=>{const t={...a.data};delete t.id,t.userName=t.userName+"(副本)",t.password="123456",t.createTime="",p.value.openDialog("add","复制用户",t)})},ie=e=>{const a=e.id;r.$modal.confirm('是否确认删除编号为"'+a+'"的数据项?').then(function(){return U(a)}).then(()=>{g(),r.$modal.msgSuccess("删除成功")}).catch(()=>{})},le=()=>{r.$modal.msgSuccess("导出功能演示 - 已生成模拟数据文件")},se=()=>{r.$modal.msgInfo("导入功能演示中...")},oe=({type:e,data:a})=>{e==="add"?j(a).then(t=>{r.$modal.msgSuccess("新增成功"),p.value.onSubmitSuccess(),g()}).catch(()=>{p.value.onSubmitError()}):e==="edit"&&B(a).then(t=>{r.$modal.msgSuccess("修改成功"),p.value.onSubmitSuccess(),g()}).catch(()=>{p.value.onSubmitError()})},ne=()=>{};return(e,a)=>{const t=W("el-button"),i=W("el-switch"),f=W("el-empty"),d=xe("hasPermi");return n(),D("div",we,[S.value&&_.value.length>0?(n(),h(pe,{key:0,columns:_.value,data:w.value,loading:T.value,showIndex:!0,searchColumns:I.value,showOperation:!0,operationLabel:"操作",operationWidth:"220",fixedOperation:!0,ref_key:"tableListRef",ref:y,onSearch:K,onReset:J,defaultPage:{pageSize:k.value,currentPage:b.value,total:C.value},onCurrentChange:Q,onSizeChange:X},{"menu-left":c(()=>[v((n(),h(t,{type:"primary",class:"common-btn",onClick:q},{default:c(()=>[x(" 新 增 ")]),_:1})),[[d,["template:add"]]]),v((n(),h(t,{type:"warning",class:"common-btn",onClick:le},{default:c(()=>[x(" 导 出 ")]),_:1})),[[d,["template:export"]]]),v((n(),h(t,{type:"info",class:"common-btn",onClick:se},{default:c(()=>[x(" 导 入 ")]),_:1})),[[d,["template:import"]]])]),status:c(({row:s})=>[s&&s.id&&s.userName?(n(),h(i,{key:0,modelValue:s.status,"onUpdate:modelValue":m=>s.status=m,"active-value":"0","inactive-value":"1",onChange:m=>Z(s,m)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(n(),D("span",Ie,"-"))]),price:c(({row:s})=>[V("div",Se,[s.price?(n(),D("span",Ne," ¥"+_e(parseFloat(s.price).toFixed(2)),1)):(n(),D("span",Ye,"-"))])]),menu:c(({row:s})=>[V("div",null,[v((n(),h(t,{type:"primary",link:"",onClick:m=>ee(s)},{default:c(()=>[x(" 修改 ")]),_:2},1032,["onClick"])),[[d,["template:edit"]]]),v((n(),h(t,{type:"danger",link:"",onClick:m=>ie(s)},{default:c(()=>[x(" 删除 ")]),_:2},1032,["onClick"])),[[d,["template:remove"]]]),v((n(),h(t,{type:"info",link:"",onClick:m=>te(s)},{default:c(()=>[x(" 详细 ")]),_:2},1032,["onClick"])),[[d,["template:query"]]]),v((n(),h(t,{type:"success",link:"",onClick:m=>ae(s)},{default:c(()=>[x(" 复制 ")]),_:2},1032,["onClick"])),[[d,["template:add"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(n(),D("div",Ee,[$(f,{description:"正在加载表格配置..."})])),ye(e.$slots,"custom-content",{},void 0,!0),$(me,{ref_key:"templateFormDialogRef",ref:p,formFields:L.value,formOption:N.value,onSubmit:oe,onCancel:ne},null,8,["formFields","formOption"])])}}}),He=he(Fe,[["__scopeId","data-v-aea4e0b3"]]);export{He as default};
