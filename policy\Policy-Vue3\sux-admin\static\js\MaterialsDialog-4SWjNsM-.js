import{_ as E,r as b,A as x,f as p,l as d,o as n,k as e,h as i,c as r,m as C,i as a,p as s,t as c,j as k,a7 as F,L as S,M as I,N as J,af as O,ad as G,a1 as H,$ as K,x as Q,y as R}from"./index-DP10CBaW.js";const v=y=>(Q("data-v-5610f30c"),y=y(),R(),y),W={class:"materials-container"},X={class:"application-info"},Y=v(()=>i("h4",null,"申请人信息",-1)),Z={key:0,class:"company-info"},ee=v(()=>i("h4",null,"企业基本信息",-1)),ae={key:1,class:"bank-info"},le=v(()=>i("h4",null,"银行开户信息",-1)),se={class:"materials-list"},te=v(()=>i("h4",null,"申请材料文件",-1)),oe={key:0,class:"materials-grid"},ne={class:"material-header"},ce={class:"material-icon"},ie={class:"material-status"},ue={class:"material-content"},de={class:"material-name"},re={key:0,class:"files-list"},_e={key:1,class:"no-file"},pe=v(()=>i("span",null,"未上传文件",-1)),me={class:"dialog-footer"},fe={__name:"MaterialsDialog",setup(y,{expose:V}){const h=b(!1),M=b("申请材料"),l=b({}),m=b([]),A=x(()=>{const t=l.value;return t.companyName||t.companyCode||t.companyLegalPerson||t.companyAddress||t.companyContactPerson||t.companyContactPhone}),P=x(()=>{const t=l.value;return t.bankName||t.bankAccountName||t.bankAccountNumber}),T=t=>{h.value=!0,M.value=`申请材料 - ${t.policyName}`,l.value={...t};try{const _=typeof t.requiredMaterials=="string"?JSON.parse(t.requiredMaterials):t.requiredMaterials||[];m.value=_.map(o=>({...o,files:o.files||[]}))}catch(_){console.error("解析材料数据失败:",_),m.value=[]}},q=()=>{h.value=!1,l.value={},m.value=[]},w=t=>({0:"warning",1:"success",2:"danger",3:"warning",4:"success",5:"danger",6:"info"})[t]||"info",z=t=>({0:"待初审",1:"初审通过",2:"初审拒绝",3:"待终审",4:"终审通过",5:"终审拒绝",6:"已完成"})[t]||"未知状态";return V({openDialog:T}),(t,_)=>{const o=p("el-descriptions-item"),f=p("el-tag"),g=p("el-descriptions"),N=p("el-icon"),D=p("el-empty"),B=p("el-button"),L=p("el-dialog");return n(),d(L,{modelValue:h.value,"onUpdate:modelValue":_[0]||(_[0]=u=>h.value=u),title:M.value,width:"1000px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:e(()=>[i("div",me,[a(B,{onClick:q},{default:e(()=>[s("关闭")]),_:1})])]),default:e(()=>[i("div",W,[i("div",X,[Y,a(g,{column:2,border:""},{default:e(()=>[a(o,{label:"申请ID"},{default:e(()=>[s(c(l.value.applicationId),1)]),_:1}),a(o,{label:"政策名称"},{default:e(()=>[s(c(l.value.policyName),1)]),_:1}),a(o,{label:"申请人姓名"},{default:e(()=>[s(c(l.value.applicantName||l.value.applicantUserName),1)]),_:1}),a(o,{label:"联系电话"},{default:e(()=>[s(c(l.value.applicantPhone||"未填写"),1)]),_:1}),a(o,{label:"申请人账号"},{default:e(()=>[s(c(l.value.applicantUserName),1)]),_:1}),a(o,{label:"提交时间"},{default:e(()=>[s(c(k(F)(l.value.submitTime)),1)]),_:1}),a(o,{label:"当前状态",span:2},{default:e(()=>[a(f,{type:w(l.value.applicationStatus),size:"small"},{default:e(()=>[s(c(z(l.value.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),l.value.remark?(n(),d(o,{key:0,label:"备注信息",span:2},{default:e(()=>[s(c(l.value.remark),1)]),_:1})):C("",!0)]),_:1})]),A.value?(n(),r("div",Z,[ee,a(g,{column:2,border:""},{default:e(()=>[a(o,{label:"企业名称"},{default:e(()=>[s(c(l.value.companyName||"未填写"),1)]),_:1}),a(o,{label:"法定代表人"},{default:e(()=>[s(c(l.value.companyLegalPerson||"未填写"),1)]),_:1}),a(o,{label:"统一社会信用代码",span:2},{default:e(()=>[s(c(l.value.companyCode||"未填写"),1)]),_:1}),a(o,{label:"企业注册地址",span:2},{default:e(()=>[s(c(l.value.companyAddress||"未填写"),1)]),_:1}),a(o,{label:"企业联系人"},{default:e(()=>[s(c(l.value.companyContactPerson||"未填写"),1)]),_:1}),a(o,{label:"企业联系电话"},{default:e(()=>[s(c(l.value.companyContactPhone||"未填写"),1)]),_:1})]),_:1})])):C("",!0),P.value?(n(),r("div",ae,[le,a(g,{column:2,border:""},{default:e(()=>[a(o,{label:"开户银行"},{default:e(()=>[s(c(l.value.bankName||"未填写"),1)]),_:1}),a(o,{label:"账户名称"},{default:e(()=>[s(c(l.value.bankAccountName||"未填写"),1)]),_:1}),a(o,{label:"银行账号",span:2},{default:e(()=>[s(c(l.value.bankAccountNumber||"未填写"),1)]),_:1})]),_:1})])):C("",!0),i("div",se,[te,m.value.length>0?(n(),r("div",oe,[(n(!0),r(S,null,I(m.value,(u,U)=>(n(),r("div",{key:U,class:J(["material-card",{required:u.required,uploaded:u.uploaded}])},[i("div",ne,[i("div",ce,[u.uploaded?(n(),d(N,{key:0,class:"success-icon"},{default:e(()=>[a(k(O))]),_:1})):(n(),d(N,{key:1,class:"pending-icon"},{default:e(()=>[a(k(G))]),_:1}))]),i("div",ie,[u.required?(n(),d(f,{key:0,type:"danger",size:"small"},{default:e(()=>[s("必需")]),_:1})):(n(),d(f,{key:1,type:"info",size:"small"},{default:e(()=>[s("可选")]),_:1})),u.uploaded?(n(),d(f,{key:2,type:"success",size:"small"},{default:e(()=>[s("已上传")]),_:1})):(n(),d(f,{key:3,type:"warning",size:"small"},{default:e(()=>[s("未上传")]),_:1}))])]),i("div",ue,[i("div",de,c(u.name),1),u.files&&u.files.length>0?(n(),r("div",re,[(n(!0),r(S,null,I(u.files,($,j)=>(n(),r("div",{key:j,class:"file-item"},[a(H,{file:$},null,8,["file"])]))),128))])):(n(),r("div",_e,[a(N,{class:"no-file-icon"},{default:e(()=>[a(k(K))]),_:1}),pe]))])],2))),128))])):(n(),d(D,{key:1,description:"暂无材料信息"}))])])]),_:1},8,["modelValue","title"])}}},ye=E(fe,[["__scopeId","data-v-5610f30c"]]);export{ye as default};
