import{C as G,u as H,d as J,r as p,D as M,f as r,K as V,c as W,o as h,G as d,i as e,H as $,j as t,k as o,n as x,p as _,l as w,F as X,h as Y,t as Z}from"./index-DP10CBaW.js";import ee from"./selectUser-YfNsRCAn.js";import{a as te,b as oe,c as le}from"./role-FWqI-CVH.js";const ne={class:"app-container"},ae=G({name:"AuthUser"}),me=Object.assign(ae,{setup(se){const R=H(),{proxy:u}=J(),{sys_normal_disable:A}=u.useDict("sys_normal_disable"),N=p([]),y=p(!0),b=p(!0),S=p(!0),v=p(0),U=p([]),n=M({pageNum:1,pageSize:10,roleId:R.params.roleId,userName:void 0,phonenumber:void 0});function c(){y.value=!0,te(n).then(s=>{N.value=s.rows,v.value=s.total,y.value=!1})}function D(){const s={path:"/system/role"};u.$tab.closeOpenPage(s)}function f(){n.pageNum=1,c()}function P(){u.resetForm("queryRef"),f()}function T(s){U.value=s.map(a=>a.userId),S.value=!s.length}function j(){u.$refs.selectRef.show()}function B(s){u.$modal.confirm('确认要取消该用户"'+s.userName+'"角色吗？').then(function(){return le({userId:s.userId,roleId:n.roleId})}).then(()=>{c(),u.$modal.msgSuccess("取消授权成功")}).catch(()=>{})}function K(s){const a=n.roleId,g=U.value.join(",");u.$modal.confirm("是否取消选中用户授权数据项?").then(function(){return oe({roleId:a,userIds:g})}).then(()=>{c(),u.$modal.msgSuccess("取消授权成功")}).catch(()=>{})}return c(),(s,a)=>{const g=r("el-input"),C=r("el-form-item"),m=r("el-button"),q=r("el-form"),I=r("el-col"),z=r("right-toolbar"),L=r("el-row"),i=r("el-table-column"),O=r("dict-tag"),Q=r("el-table"),F=r("pagination"),k=V("hasPermi"),E=V("loading");return h(),W("div",ne,[d(e(q,{model:t(n),ref:"queryRef",inline:!0},{default:o(()=>[e(C,{label:"用户名称",prop:"userName"},{default:o(()=>[e(g,{modelValue:t(n).userName,"onUpdate:modelValue":a[0]||(a[0]=l=>t(n).userName=l),placeholder:"请输入用户名称",clearable:"",style:{width:"240px"},onKeyup:x(f,["enter"])},null,8,["modelValue"])]),_:1}),e(C,{label:"手机号码",prop:"phonenumber"},{default:o(()=>[e(g,{modelValue:t(n).phonenumber,"onUpdate:modelValue":a[1]||(a[1]=l=>t(n).phonenumber=l),placeholder:"请输入手机号码",clearable:"",style:{width:"240px"},onKeyup:x(f,["enter"])},null,8,["modelValue"])]),_:1}),e(C,null,{default:o(()=>[e(m,{type:"primary",class:"common-btn",onClick:f},{default:o(()=>[_("搜 索")]),_:1}),e(m,{class:"common-btn",onClick:P},{default:o(()=>[_("重 置")]),_:1})]),_:1})]),_:1},8,["model"]),[[$,t(b)]]),e(L,{gutter:10,class:"mb8"},{default:o(()=>[e(I,{span:1.5},{default:o(()=>[d((h(),w(m,{type:"primary",plain:"",class:"common-btn",onClick:j},{default:o(()=>[_("添加用户")]),_:1})),[[k,["system:role:add"]]])]),_:1}),e(I,{span:1.5},{default:o(()=>[d((h(),w(m,{type:"danger",plain:"",class:"common-btn",disabled:t(S),onClick:K},{default:o(()=>[_("批量取消授权")]),_:1},8,["disabled"])),[[k,["system:role:remove"]]])]),_:1}),e(I,{span:1.5},{default:o(()=>[e(m,{type:"warning",plain:"",class:"common-btn",onClick:D},{default:o(()=>[_("关 闭")]),_:1})]),_:1}),e(z,{showSearch:t(b),"onUpdate:showSearch":a[2]||(a[2]=l=>X(b)?b.value=l:null),onQueryTable:c},null,8,["showSearch"])]),_:1}),d((h(),w(Q,{data:t(N),onSelectionChange:T},{default:o(()=>[e(i,{type:"selection",width:"55",align:"center"}),e(i,{label:"用户名称",prop:"userName","show-overflow-tooltip":!0}),e(i,{label:"用户昵称",prop:"nickName","show-overflow-tooltip":!0}),e(i,{label:"邮箱",prop:"email","show-overflow-tooltip":!0}),e(i,{label:"手机",prop:"phonenumber","show-overflow-tooltip":!0}),e(i,{label:"状态",align:"center",prop:"status"},{default:o(l=>[e(O,{options:t(A),value:l.row.status},null,8,["options","value"])]),_:1}),e(i,{label:"创建时间",align:"center",prop:"createTime",width:"180"},{default:o(l=>[Y("span",null,Z(s.parseTime(l.row.createTime)),1)]),_:1}),e(i,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:o(l=>[d((h(),w(m,{link:"",type:"primary",onClick:re=>B(l.row)},{default:o(()=>[_("取消授权")]),_:2},1032,["onClick"])),[[k,["system:role:remove"]]])]),_:1})]),_:1},8,["data"])),[[E,t(y)]]),d(e(F,{total:t(v),page:t(n).pageNum,"onUpdate:page":a[3]||(a[3]=l=>t(n).pageNum=l),limit:t(n).pageSize,"onUpdate:limit":a[4]||(a[4]=l=>t(n).pageSize=l),onPagination:c},null,8,["total","page","limit"]),[[$,t(v)>0]]),e(t(ee),{ref:"selectRef",roleId:t(n).roleId,onOk:f},null,8,["roleId"])])}}});export{me as default};
