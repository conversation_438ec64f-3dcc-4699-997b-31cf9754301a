<template>
  <div class="recruitment-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="recruitmentList" :loading="tableLoading" :showIndex="true"
      :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作" operationWidth="280"
      :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['recruitment:institution:add']">新 增</el-button>
        <el-button type="warning" plain class="custom-btn" @click="handleExport" v-hasPermi="['recruitment:institution:export']">导 出</el-button>
      </template>

      <!-- 招募状态列插槽 -->
      <template #recruitmentStatus="{ row }">
        <el-tag :type="getStatusTagType(row.recruitmentStatus)">
          {{ getStatusText(row.recruitmentStatus) }}
        </el-tag>
      </template>

      <!-- 是否推荐列插槽 -->
      <template #isFeatured="{ row }">
        <el-switch v-model="row.isFeatured" active-value="1" inactive-value="0"
          @change="handleFeaturedChange(row)" :disabled="isInitializing"></el-switch>
      </template>

      <!-- 是否紧急列插槽 -->
      <template #isUrgent="{ row }">
        <el-switch v-model="row.isUrgent" active-value="1" inactive-value="0"
          @change="handleUrgentChange(row)" :disabled="isInitializing"></el-switch>
      </template>

      <!-- 培训费用列插槽 -->
      <template #trainingFee="{ row }">
        <span v-if="row.trainingFeeMin && row.trainingFeeMax">
          ¥{{ row.trainingFeeMin }} - ¥{{ row.trainingFeeMax }}
        </span>
        <span v-else-if="row.trainingFeeMin">
          ¥{{ row.trainingFeeMin }}起
        </span>
        <span v-else>
          面议
        </span>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button type="primary" link @click="handleUpdate(row)" v-hasPermi="['recruitment:institution:edit']">编辑</el-button>
          <el-button v-if="row.recruitmentStatus === '0'" type="success" link @click="handlePublish(row)" v-hasPermi="['recruitment:institution:publish']">发布</el-button>
          <el-button v-if="['0','1','2'].includes(row.recruitmentStatus)" type="warning" link @click="handleCancel(row)" v-hasPermi="['recruitment:institution:cancel']">取消</el-button>
          <el-button type="info" link @click="handleViewApplications(row)" v-hasPermi="['recruitment:institution:applications']">查看申请</el-button>
          <el-button type="danger" link @click="handleDelete(row)" v-hasPermi="['recruitment:institution:remove']">删除</el-button>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <RecruitmentFormDialog ref="recruitmentFormDialogRef" :formFields="formFields" :formOption="formOption"
      @submit="handleFormSubmit" @cancel="handleFormCancel" />
  </div>
</template>

<script setup name="InstitutionRecruitment">
import { listInstitutionRecruitment, getInstitutionRecruitment, delInstitutionRecruitment, addInstitutionRecruitment, updateInstitutionRecruitment, publishInstitutionRecruitment, cancelInstitutionRecruitment } from "@/api/recruitment/institution"
import { createInstitutionRecruitmentTableOption } from "@/const/recruitment/institution"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import TableList from '@/components/TableList/index.vue'
import RecruitmentFormDialog from './RecruitmentFormDialog.vue'

const { proxy } = getCurrentInstance()

const recruitmentList = ref([])
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const isInitializing = ref(true) // 添加初始化标志

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
  dialogWidth: '1200px',
  dialogHeight: '80vh'
})
const tableListRef = ref(null)
const recruitmentFormDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    recruitmentTitle: undefined,
    trainingCategory: undefined,
    trainingLevel: undefined,
    recruitmentStatus: undefined,
    isFeatured: undefined,
    isUrgent: undefined,
    contactPerson: undefined
  }
})

const { queryParams } = toRefs(data)

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  getList()
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createInstitutionRecruitmentTableOption(proxy);

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
    formOption.value = {
      ...formOption.value, // 保留默认配置
      ...formOptions       // 使用从配置文件中提取的完整选项
    };

    isTableReady.value = true;
  } catch (error) {
    isTableReady.value = false;
    console.error('初始化配置失败:', error);
  }
};

/** 查询机构招募列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listInstitutionRecruitment(params).then(res => {
    tableLoading.value = false
    loading.value = false
    recruitmentList.value = res.rows
    total.value = res.total

    // 数据加载完成后，设置初始化完成
    nextTick(() => {
      isInitializing.value = false
    })
  })
}

// 处理搜索
const handleSearch = (params) => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params };

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {};
  Object.assign(queryParams.value, otherParams);
  queryParams.value.pageNum = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    recruitmentTitle: undefined,
    trainingCategory: undefined,
    trainingLevel: undefined,
    recruitmentStatus: undefined,
    isFeatured: undefined,
    isUrgent: undefined,
    contactPerson: undefined
  };
  searchParams.value = {};
  getList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  isInitializing.value = true;
  queryParams.value.pageNum = page;
  getList();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  isInitializing.value = true;
  queryParams.value.pageSize = size;
  queryParams.value.pageNum = 1;
  getList();
};

/** 删除按钮操作 */
function handleDelete(row) {
  const recruitmentIds = row.recruitmentId || ids.value
  proxy.$modal.confirm('是否确认删除机构招募编号为"' + recruitmentIds + '"的数据项？').then(function () {
    return delInstitutionRecruitment(recruitmentIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("recruitment/institution/export", {
    ...queryParams.value,
  }, `institution_recruitment_${new Date().getTime()}.xlsx`)
}

/** 推荐状态修改  */
function handleFeaturedChange(row) {
  // 如果正在初始化，则忽略状态变更事件
  if (isInitializing.value) {
    return
  }

  let text = row.isFeatured === "1" ? "推荐" : "取消推荐"
  proxy.$modal.confirm('确认要"' + text + '""' + row.recruitmentTitle + '"机构招募吗?').then(function () {
    return updateInstitutionRecruitment(row)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function () {
    row.isFeatured = row.isFeatured === "1" ? "0" : "1"
  })
}

/** 紧急状态修改  */
function handleUrgentChange(row) {
  // 如果正在初始化，则忽略状态变更事件
  if (isInitializing.value) {
    return
  }

  let text = row.isUrgent === "1" ? "设为紧急" : "取消紧急"
  proxy.$modal.confirm('确认要"' + text + '""' + row.recruitmentTitle + '"机构招募吗?').then(function () {
    return updateInstitutionRecruitment(row)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function () {
    row.isUrgent = row.isUrgent === "1" ? "0" : "1"
  })
}

/** 发布招募 */
function handlePublish(row) {
  proxy.$modal.confirm('确认要发布"' + row.recruitmentTitle + '"机构招募吗？发布后将对外可见。').then(function () {
    return publishInstitutionRecruitment(row.recruitmentId)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("发布成功")
  }).catch(() => { })
}

/** 取消招募 */
function handleCancel(row) {
  proxy.$modal.confirm('确认要取消"' + row.recruitmentTitle + '"机构招募吗？取消后将不再接受申请。').then(function () {
    return cancelInstitutionRecruitment(row.recruitmentId)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("取消成功")
  }).catch(() => { })
}

/** 查看申请 */
function handleViewApplications(row) {
  // 跳转到申请管理页面
  proxy.$router.push({
    path: '/recruitment/applications',
    query: { recruitmentId: row.recruitmentId }
  })
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.recruitmentId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const statusMap = {
    '0': 'info',     // 草稿
    '1': 'success',  // 发布
    '2': 'warning',  // 进行中
    '3': 'primary',  // 已完成
    '4': 'danger'    // 已取消
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    '0': '草稿',
    '1': '发布',
    '2': '进行中',
    '3': '已完成',
    '4': '已取消'
  }
  return statusMap[status] || '未知'
}

// 查看
const handleView = (row) => {
  recruitmentFormDialogRef.value?.openDialog('view', '查看机构招募', row)
}

// 编辑
const handleEdit = (row) => {
  const recruitmentId = row.recruitmentId
  getInstitutionRecruitment(recruitmentId).then(response => {
    recruitmentFormDialogRef.value?.openDialog('edit', '编辑机构招募', response.data)
  })
}

// 新增
const handleAddRecruitment = () => {
  const defaultData = {
    recruitmentStatus: "0",
    isFeatured: "0",
    isUrgent: "0",
    onlineSupport: "0",
    offlineSupport: "0",
    applicationCount: 0,
    selectedCount: 0,
    viewCount: 0
  }
  recruitmentFormDialogRef.value?.openDialog('add', '新增机构招募', defaultData)
}

// 处理表单提交事件
const handleFormSubmit = async (payload) => {
  try {
    if (payload.type === 'add') {
      // 新增
      await addInstitutionRecruitment(payload.data)
      proxy.$modal.msgSuccess("添加成功")
    } else if (payload.type === 'edit') {
      // 编辑
      await updateInstitutionRecruitment(payload.data)
      proxy.$modal.msgSuccess("修改成功")
    }

    // 通知子组件提交成功
    recruitmentFormDialogRef.value?.onSubmitSuccess()
    getList()
  } catch (error) {
    // 通知子组件提交失败
    recruitmentFormDialogRef.value?.onSubmitError()
    console.error('提交失败:', error)
  }
}

// 处理表单取消事件
const handleFormCancel = () => {
  // 可以在这里添加取消逻辑
}

/** 新增按钮操作 */
function handleAdd() {
  handleAddRecruitment()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  if (row) {
    handleEdit(row)
  } else {
    // 批量编辑
    const recruitmentId = ids.value[0]
    const selectedRow = recruitmentList.value.find(item => item.recruitmentId === recruitmentId)
    if (selectedRow) {
      handleEdit(selectedRow)
    }
  }
}

</script>

<style lang="scss" scoped></style>
