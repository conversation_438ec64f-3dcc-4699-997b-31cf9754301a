import{b as fe,A as ge,c as be}from"./ApprovalRecordsDialog-DW515uqd.js";import{_ as _e,C as ve,d as Ne,r,D as j,I as ke,e as he,f,c as b,o as u,l as N,i as e,k as l,h as _,m as z,p as i,t as k,j as L,a7 as Ce,L as Ae,M as Pe,$ as Ve,a0 as Se}from"./index-DP10CBaW.js";import Te from"./MaterialsDialog-4SWjNsM-.js";import{T as qe}from"./index-BWWetMd6.js";const Le={class:"policy-container app-container"},Ue={key:1},Ie={key:0},Re={key:1},De={key:0},Me={key:1},ze={class:"operation-btns"},we={key:1,class:"loading-placeholder"},Fe={class:"apply-form"},$e={class:"required-materials"},Ee={class:"material-header"},Oe={class:"material-info"},xe={class:"material-name"},Be={class:"material-tags"},je={class:"material-upload"},We={class:"dialog-footer"},Je=ve({name:"PolicyS"}),Ge=Object.assign(Je,{setup(He){const{proxy:d}=Ne(),U=r([]),w=r(!0),F=r(0),I=r([]),$=r([]),R=r(!1),W=r(!0),J=r(null),A=r(!1),T=r(null),D=r(!1),P=r(null),E=r(null),O=r(null),G=j({queryParams:{pageNum:1,pageSize:10,policyName:void 0,policyType:void 0,status:void 0}}),{queryParams:g}=ke(G),o=j({policyId:"",applicantName:"",applicantPhone:"",companyName:"",companyCode:"",companyLegalPerson:"",companyAddress:"",companyContactPerson:"",companyContactPhone:"",bankName:"",bankAccountName:"",bankAccountNumber:"",remark:""}),H={applicantName:[{required:!0,message:"请输入申请人姓名",trigger:"blur"}],applicantPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"}],companyName:[{required:!0,message:"请输入企业名称",trigger:"blur"}],companyCode:[{required:!0,message:"请输入统一社会信用代码",trigger:"blur"}],companyLegalPerson:[{required:!0,message:"请输入法定代表人",trigger:"blur"}],companyAddress:[{required:!0,message:"请输入企业注册地址",trigger:"blur"}],companyContactPerson:[{required:!0,message:"请输入企业联系人",trigger:"blur"}],companyContactPhone:[{required:!0,message:"请输入企业联系电话",trigger:"blur"}],bankName:[{required:!0,message:"请输入开户银行名称",trigger:"blur"}],bankAccountName:[{required:!0,message:"请输入银行账户名称",trigger:"blur"}],bankAccountNumber:[{required:!0,message:"请输入银行账号",trigger:"blur"}]},h=r([{name:"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》",required:!0,files:[]},{name:"企业的营业执照副本复印件",required:!0,files:[]},{name:"退役军人需提供退伍证原件及复印件",required:!1,files:[]},{name:"企业社保缴费凭证",required:!0,files:[]}]),K=()=>{I.value=[{prop:"policyName",label:"政策名称",minWidth:200},{prop:"policyType",label:"政策类型",width:120,tableSlot:!0},{prop:"applicationStatus",label:"申请状态",width:120,tableSlot:!0},{prop:"submitTime",label:"申请时间",width:160,tableSlot:!0},{prop:"companyName",label:"申请企业",width:200,tableSlot:!0},{prop:"policyDescription",label:"政策描述",width:300}],$.value=[{prop:"policyName",label:"政策名称",type:"input"},{prop:"applicationStatus",label:"申请状态",type:"select",dicData:[{label:"待初审",value:"0"},{label:"初审通过",value:"1"},{label:"初审拒绝",value:"2"},{label:"待终审",value:"3"},{label:"终审通过",value:"4"},{label:"终审拒绝",value:"5"},{label:"已完成",value:"6"}]}]};he(()=>{K(),v()});const Q=s=>{Object.assign(g.value,s),g.value.pageNum=1,v()},X=()=>{g.value={pageNum:1,pageSize:10,policyName:void 0,policyType:void 0,status:void 0},v()},Y=s=>{g.value.pageNum=s,v()},Z=s=>{g.value.pageSize=s,g.value.pageNum=1,v()},v=async()=>{var s;w.value=!0,R.value=!0;try{const t=await fe({...g.value}),m=t.rows||[];F.value=t.total||0,console.log("已申请的政策列表数据:",m),U.value=m.map(n=>{const V={applicationId:n.applicationId,policyId:n.policyId,applicantUserId:n.applicantUserId,applicantName:n.applicantName,applicantPhone:n.applicantPhone,applicationStatus:n.applicationStatus,requiredMaterials:n.requiredMaterials,companyName:n.companyName,companyCode:n.companyCode,companyLegalPerson:n.companyLegalPerson,companyAddress:n.companyAddress,companyContactPerson:n.companyContactPerson,companyContactPhone:n.companyContactPhone,bankName:n.bankName,bankAccountName:n.bankAccountName,bankAccountNumber:n.bankAccountNumber,submitTime:n.submitTime,completeTime:n.completeTime,remark:n.remark};return{policyId:n.policyId,policyName:n.policyName,policyDescription:n.policyDescription,policyType:n.policyType,status:n.status,userApplication:V,applicationStatus:n.applicationStatus,canApply:["2","5"].includes(n.applicationStatus),submitTime:n.submitTime,companyName:n.companyName}}),console.log("最终已申请政策列表:",U.value)}catch(t){console.error("获取已申请政策列表失败:",t),(s=d==null?void 0:d.$modal)==null||s.msgError("获取已申请政策列表失败")}finally{w.value=!1,R.value=!1}},ee=()=>{},ae=()=>{v()},le=s=>({就业扶持:"success",创业支持:"warning",技能培训:"info",社会保障:"primary",其他:"default"})[s]||"default",oe=s=>({0:"warning",1:"success",2:"danger",3:"warning",4:"success",5:"danger",6:"success"})[s]||"info",te=s=>({0:"待初审",1:"初审通过",2:"初审拒绝",3:"待终审",4:"终审通过",5:"终审拒绝",6:"已完成"})[s]||"未知状态",ne=s=>{T.value=s,o.policyId=s.policyId,x(),ue(),A.value=!0},se=s=>{var t;(t=E.value)==null||t.openDialog(s)},pe=s=>{var t;(t=O.value)==null||t.openDialog(s.applicationId)},x=()=>{h.value.forEach(s=>{s.files=[]})},ue=()=>{h.value.forEach(s=>{s.files||(s.files=[])})},re=(s,t)=>{h.value[t].files=s.fileList||[]},ie=async()=>{var s,t,m;if(P.value)try{if(await P.value.validate(),h.value.filter(p=>p.required&&(!p.files||p.files.length===0)).length>0){(s=d==null?void 0:d.$modal)==null||s.msgError("请上传所有必需的材料文件");return}D.value=!0;const V={policyId:o.policyId,applicantName:o.applicantName,applicantPhone:o.applicantPhone,companyName:o.companyName,companyCode:o.companyCode,companyLegalPerson:o.companyLegalPerson,companyAddress:o.companyAddress,companyContactPerson:o.companyContactPerson,companyContactPhone:o.companyContactPhone,bankName:o.bankName,bankAccountName:o.bankAccountName,bankAccountNumber:o.bankAccountNumber,requiredMaterials:JSON.stringify(h.value),remark:o.remark};await be(V),(t=d==null?void 0:d.$modal)==null||t.msgSuccess("申请提交成功，请等待审核"),A.value=!1,ce(),v()}catch(n){console.error("提交申请失败:",n),(m=d==null?void 0:d.$modal)==null||m.msgError("提交申请失败")}finally{D.value=!1}},ce=()=>{o.policyId="",o.applicantName="",o.applicantPhone="",o.companyName="",o.companyCode="",o.companyLegalPerson="",o.companyAddress="",o.companyContactPerson="",o.companyContactPhone="",o.bankName="",o.bankAccountName="",o.bankAccountNumber="",o.remark="",x(),P.value&&P.value.resetFields()};return(s,t)=>{var B;const m=f("el-button"),n=f("el-tag"),V=f("el-empty"),p=f("el-input"),c=f("el-form-item"),q=f("el-divider"),y=f("el-col"),C=f("el-row"),de=f("el-icon"),me=f("el-form"),ye=f("el-dialog");return u(),b("div",Le,[W.value&&I.value.length>0?(u(),N(qe,{key:0,columns:I.value,data:U.value,loading:R.value,showIndex:!1,searchColumns:$.value,showOperation:!0,operationLabel:"操作",operationWidth:"200",fixedOperation:!0,ref_key:"tableListRef",ref:J,onSearch:Q,onReset:X,defaultPage:{pageSize:L(g).pageSize,currentPage:L(g).pageNum,total:F.value},onCurrentChange:Y,onSizeChange:Z,onSelectionChange:ee},{"menu-left":l(()=>[e(m,{type:"primary",class:"custom-btn",onClick:ae},{default:l(()=>[i("刷新")]),_:1})]),policyType:l(({row:a})=>[e(n,{type:le(a.policyType),size:"small"},{default:l(()=>[i(k(a.policyType),1)]),_:2},1032,["type"])]),status:l(({row:a})=>[e(n,{type:a.status==="0"?"success":"danger",size:"small"},{default:l(()=>[i(k(a.status==="0"?"正常":"停用"),1)]),_:2},1032,["type"])]),applicationStatus:l(({row:a})=>[a.userApplication?(u(),N(n,{key:0,type:oe(a.applicationStatus),size:"small"},{default:l(()=>[i(k(te(a.applicationStatus)),1)]),_:2},1032,["type"])):(u(),b("span",Ue,"未申请"))]),submitTime:l(({row:a})=>[a.submitTime?(u(),b("span",Ie,k(L(Ce)(a.submitTime,"{y}-{m}-{d} {h}:{i}")),1)):(u(),b("span",Re,"-"))]),companyName:l(({row:a})=>[a.companyName?(u(),b("span",De,k(a.companyName),1)):(u(),b("span",Me,"-"))]),menu:l(({row:a})=>[_("div",ze,[a.canApply?(u(),N(m,{key:0,type:"primary",link:"",onClick:S=>ne(a),disabled:a.status!=="0"},{default:l(()=>[i(k(a.userApplication?"重新申请":"立即申请"),1)]),_:2},1032,["onClick","disabled"])):z("",!0),a.userApplication?(u(),N(m,{key:1,type:"success",link:"",onClick:S=>se(a.userApplication)},{default:l(()=>[i(" 查看申请 ")]),_:2},1032,["onClick"])):z("",!0),a.userApplication?(u(),N(m,{key:2,type:"info",link:"",onClick:S=>pe(a.userApplication)},{default:l(()=>[i(" 审核状态 ")]),_:2},1032,["onClick"])):z("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(u(),b("div",we,[e(V,{description:"正在加载表格配置..."})])),e(ye,{modelValue:A.value,"onUpdate:modelValue":t[14]||(t[14]=a=>A.value=a),title:`申请 - ${(B=T.value)==null?void 0:B.policyName}`,width:"1000px","close-on-click-modal":!1,"append-to-body":""},{footer:l(()=>[_("div",We,[e(m,{onClick:t[13]||(t[13]=a=>A.value=!1)},{default:l(()=>[i("取消")]),_:1}),e(m,{type:"primary",loading:D.value,onClick:ie},{default:l(()=>[i(" 提交申请 ")]),_:1},8,["loading"])])]),default:l(()=>[_("div",Fe,[e(me,{ref_key:"applyFormRef",ref:P,model:o,rules:H,"label-width":"120px"},{default:l(()=>[e(c,{label:"申请政策",prop:"policyId"},{default:l(()=>[e(p,{modelValue:T.value.policyName,"onUpdate:modelValue":t[0]||(t[0]=a=>T.value.policyName=a),disabled:""},null,8,["modelValue"])]),_:1}),e(q,{"content-position":"left"},{default:l(()=>[i("申请人信息")]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(c,{label:"申请人姓名",prop:"applicantName"},{default:l(()=>[e(p,{modelValue:o.applicantName,"onUpdate:modelValue":t[1]||(t[1]=a=>o.applicantName=a),placeholder:"请输入申请人姓名"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(c,{label:"联系电话",prop:"applicantPhone"},{default:l(()=>[e(p,{modelValue:o.applicantPhone,"onUpdate:modelValue":t[2]||(t[2]=a=>o.applicantPhone=a),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(q,{"content-position":"left"},{default:l(()=>[i("企业基本信息")]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(c,{label:"企业名称",prop:"companyName"},{default:l(()=>[e(p,{modelValue:o.companyName,"onUpdate:modelValue":t[3]||(t[3]=a=>o.companyName=a),placeholder:"请输入企业名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:24},{default:l(()=>[e(c,{label:"统一社会信用代码",prop:"companyCode"},{default:l(()=>[e(p,{modelValue:o.companyCode,"onUpdate:modelValue":t[4]||(t[4]=a=>o.companyCode=a),placeholder:"请输入统一社会信用代码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(c,{label:"法定代表人",prop:"companyLegalPerson"},{default:l(()=>[e(p,{modelValue:o.companyLegalPerson,"onUpdate:modelValue":t[5]||(t[5]=a=>o.companyLegalPerson=a),placeholder:"请输入法定代表人"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(c,{label:"企业联系人",prop:"companyContactPerson"},{default:l(()=>[e(p,{modelValue:o.companyContactPerson,"onUpdate:modelValue":t[6]||(t[6]=a=>o.companyContactPerson=a),placeholder:"请输入企业联系人"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(c,{label:"企业联系电话",prop:"companyContactPhone"},{default:l(()=>[e(p,{modelValue:o.companyContactPhone,"onUpdate:modelValue":t[7]||(t[7]=a=>o.companyContactPhone=a),placeholder:"请输入企业联系电话"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(c,{label:"企业注册地址",prop:"companyAddress"},{default:l(()=>[e(p,{modelValue:o.companyAddress,"onUpdate:modelValue":t[8]||(t[8]=a=>o.companyAddress=a),placeholder:"请输入企业注册地址"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(q,{"content-position":"left"},{default:l(()=>[i("银行对公户信息")]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:8},{default:l(()=>[e(c,{label:"开户银行",prop:"bankName"},{default:l(()=>[e(p,{modelValue:o.bankName,"onUpdate:modelValue":t[9]||(t[9]=a=>o.bankName=a),placeholder:"请输入开户银行名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:8},{default:l(()=>[e(c,{label:"账户名称",prop:"bankAccountName"},{default:l(()=>[e(p,{modelValue:o.bankAccountName,"onUpdate:modelValue":t[10]||(t[10]=a=>o.bankAccountName=a),placeholder:"请输入银行账户名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:8},{default:l(()=>[e(c,{label:"银行账号",prop:"bankAccountNumber"},{default:l(()=>[e(p,{modelValue:o.bankAccountNumber,"onUpdate:modelValue":t[11]||(t[11]=a=>o.bankAccountNumber=a),placeholder:"请输入银行账号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(c,{label:"备注",prop:"remark"},{default:l(()=>[e(p,{modelValue:o.remark,"onUpdate:modelValue":t[12]||(t[12]=a=>o.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),e(q,{"content-position":"left"},{default:l(()=>[i("所需材料")]),_:1}),_("div",$e,[(u(!0),b(Ae,null,Pe(h.value,(a,S)=>(u(),b("div",{class:"material-item",key:S},[_("div",Ee,[_("div",Oe,[e(de,{class:"material-icon"},{default:l(()=>[e(L(Ve))]),_:1}),_("span",xe,k(a.name),1),_("div",Be,[a.required?(u(),N(n,{key:0,type:"danger",size:"small"},{default:l(()=>[i("必需")]),_:1})):(u(),N(n,{key:1,type:"info",size:"small"},{default:l(()=>[i("可选")]),_:1}))])])]),_("div",je,[e(Se,{value:a.files,"onUpdate:value":M=>a.files=M,limit:5,"file-size":0,"file-type":[],"is-show-tip":!1,onFileLoad:M=>re(M,S)},null,8,["value","onUpdate:value","onFileLoad"])])]))),128))])]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),e(Te,{ref_key:"materialsDialogRef",ref:E},null,512),e(ge,{ref_key:"approvalRecordsDialogRef",ref:O},null,512)])}}}),Ze=_e(Ge,[["__scopeId","data-v-f597fe72"]]);export{Ze as default};
