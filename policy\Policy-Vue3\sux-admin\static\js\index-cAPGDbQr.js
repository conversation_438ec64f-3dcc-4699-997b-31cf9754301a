import{l as ae,d as te}from"./market-CPtm-ZGb.js";import{e as ne}from"./columnUtils-DYlA-XL_.js";import{T as re}from"./index-BWWetMd6.js";import oe from"./MarketFormDialog-D46zJA2K.js";import le from"./MarketDetailDialog-BsUZtJnh.js";import{_ as ie,C as se,d as ce,r as t,D as ue,I as me,e as de,f as y,K as pe,c as S,o as i,l as u,i as m,k as n,h as ge,G as k,p as s,t as b,j as x}from"./index-DP10CBaW.js";function ke(){return Promise.resolve({column:[{prop:"marketId",label:"市场ID",width:80,align:"center",showColumn:!0},{prop:"marketName",label:"市场名称",minWidth:150,align:"center",showOverflowTooltip:!0,showColumn:!0,search:!0,type:"input",placeholder:"请输入市场名称"},{prop:"marketCode",label:"市场编码",width:120,align:"center",showColumn:!0},{prop:"marketType",label:"市场类型",width:100,align:"center",slot:"marketType",showColumn:!0,search:!0,type:"select",placeholder:"请选择市场类型",dicData:[{label:"综合市场",value:"综合市场"},{label:"专业市场",value:"专业市场"},{label:"临时市场",value:"临时市场"}]},{prop:"regionName",label:"区域",width:100,align:"center",showColumn:!0,search:!0,type:"input",placeholder:"请输入区域名称"},{prop:"contactPerson",label:"联系人",width:100,align:"center",showColumn:!0},{prop:"contactPhone",label:"联系电话",width:120,align:"center",showColumn:!0},{prop:"workerCapacity",label:"零工容纳量",width:100,align:"center",showColumn:!0},{prop:"currentWorkerCount",label:"当前零工数",width:100,align:"center",showColumn:!0},{prop:"dailyAvgDemand",label:"日均需求",width:100,align:"center",showColumn:!0},{prop:"managementFee",label:"管理费用",width:100,align:"center",showColumn:!0},{prop:"isFeatured",label:"是否推荐",width:100,align:"center",slot:"isFeatured",showColumn:!0,search:!0,type:"select",placeholder:"请选择推荐状态",dicData:[{label:"推荐",value:1},{label:"不推荐",value:0}]},{prop:"status",label:"状态",width:80,align:"center",slot:"status",showColumn:!0,search:!0,type:"select",placeholder:"请选择状态",dicData:[{label:"正常",value:"0"},{label:"停用",value:"1"}]},{prop:"viewCount",label:"浏览次数",width:100,align:"center",showColumn:!0},{prop:"createTime",label:"创建时间",width:180,align:"center",showColumn:!0,formatter:f=>f.createTime?f.createTime.substring(0,10):""}],defaultData:[{marketId:1,marketName:"中心城区综合零工市场",marketCode:"MKT001",marketType:"综合市场",address:"北京市朝阳区建国路88号",regionCode:"110105",regionName:"朝阳区",contactPerson:"张经理",contactPhone:"13800138001",contactEmail:"<EMAIL>",operatingHours:"06:00-18:00",workerCapacity:500,currentWorkerCount:320,dailyAvgDemand:280,peakDemandTime:"08:00-10:00",managementFee:"50元/月",serviceFeeRate:"5%",safetyMeasures:"安全帽、保险、培训",description:"位于市中心的大型综合零工市场，提供多种工种服务",status:"0",isFeatured:1,viewCount:1250,createTime:"2024-01-15 09:30:00",remark:"重点推荐市场"},{marketId:2,marketName:"建筑专业零工市场",marketCode:"MKT002",marketType:"专业市场",address:"北京市海淀区中关村大街100号",regionCode:"110108",regionName:"海淀区",contactPerson:"李主任",contactPhone:"13800138002",contactEmail:"<EMAIL>",operatingHours:"05:30-17:30",workerCapacity:300,currentWorkerCount:180,dailyAvgDemand:150,peakDemandTime:"07:00-09:00",managementFee:"60元/月",serviceFeeRate:"6%",safetyMeasures:"专业培训、工具检查、安全监督",description:"专门服务建筑行业的专业零工市场",status:"0",isFeatured:1,viewCount:890,createTime:"2024-02-20 14:15:00",remark:"建筑行业首选"},{marketId:3,marketName:"西城区临时零工市场",marketCode:"MKT003",marketType:"临时市场",address:"北京市西城区西单北大街50号",regionCode:"110102",regionName:"西城区",contactPerson:"王协调员",contactPhone:"13800138003",contactEmail:"<EMAIL>",operatingHours:"07:00-16:00",workerCapacity:150,currentWorkerCount:95,dailyAvgDemand:80,peakDemandTime:"09:00-11:00",managementFee:"30元/月",serviceFeeRate:"3%",safetyMeasures:"基础培训、身份验证",description:"临时性零工服务市场，灵活就业",status:"0",isFeatured:0,viewCount:456,createTime:"2024-03-10 11:20:00",remark:"临时性质"},{marketId:4,marketName:"东城区家政服务市场",marketCode:"MKT004",marketType:"专业市场",address:"北京市东城区王府井大街200号",regionCode:"110101",regionName:"东城区",contactPerson:"赵经理",contactPhone:"13800138004",contactEmail:"<EMAIL>",operatingHours:"08:00-17:00",workerCapacity:200,currentWorkerCount:160,dailyAvgDemand:120,peakDemandTime:"10:00-12:00",managementFee:"40元/月",serviceFeeRate:"4%",safetyMeasures:"健康证检查、技能培训、背景调查",description:"专业家政服务人员聚集地",status:"0",isFeatured:1,viewCount:678,createTime:"2024-01-25 16:45:00",remark:"家政服务专业"},{marketId:5,marketName:"丰台区物流零工市场",marketCode:"MKT005",marketType:"综合市场",address:"北京市丰台区南三环西路88号",regionCode:"110106",regionName:"丰台区",contactPerson:"刘站长",contactPhone:"13800138005",contactEmail:"<EMAIL>",operatingHours:"24小时",workerCapacity:400,currentWorkerCount:280,dailyAvgDemand:250,peakDemandTime:"全天候",managementFee:"55元/月",serviceFeeRate:"5.5%",safetyMeasures:"驾驶证验证、车辆检查、GPS监控",description:"24小时物流配送零工服务",status:"1",isFeatured:0,viewCount:234,createTime:"2024-02-05 08:30:00",remark:"暂停服务维护中"}]})}const he={class:"market-container app-container"},fe={class:"operation-btns"},Ce={key:1,class:"loading-placeholder"},ve=se({name:"LaborMarketInfo"}),ye=Object.assign(ve,{setup(f){const{proxy:d}=ce(),_=t([]),C=t([]),R=t(!0),L=t(!0),T=t(0),w=t([]),D=t([]),h=t(!1),F=t(!1),O=t({dialogWidth:"1000px",dialogHeight:"70vh"}),W=t(null),v=t(null),N=t(null),I=t([]),P=t({}),z=ue({queryParams:{pageNum:1,pageSize:10,marketName:null,marketCode:null,marketType:null,regionCode:null,status:null}}),{queryParams:o}=me(z);function $(e){return{综合市场:"primary",专业市场:"success",临时市场:"warning"}[e]||"info"}function E(e){return e==="0"?"success":"danger"}function A(e){return e==="0"?"正常":"停用"}async function H(){try{const e=await ke(),{tableColumns:a,searchColumns:l,formFields:p}=ne(e);w.value=a,D.value=l,I.value=p,F.value=!0}catch(e){console.error("初始化表格配置失败:",e),d.$modal.msgError("表格配置加载失败")}}function c(){h.value=!0,ae(o.value).then(e=>{_.value=e.rows||[],T.value=e.total||0,h.value=!1}).catch(()=>{h.value=!1})}function K(e){P.value=e,Object.assign(o.value,e),o.value.pageNum=1,c()}function j(){P.value={},o.value={pageNum:1,pageSize:10,marketName:null,marketCode:null,marketType:null,regionCode:null,status:null},c()}function B(e){o.value.pageNum=e,c()}function q(e){o.value.pageSize=e,o.value.pageNum=1,c()}function V(e){C.value=e.map(a=>a.marketId),R.value=e.length!==1,L.value=!e.length}function G(){var e;(e=v.value)==null||e.openDialog("add")}function U(e){var l;const a=(e==null?void 0:e.marketId)||C.value[0];a&&((l=v.value)==null||l.openDialog("edit",a))}function J(e){var a;(a=N.value)==null||a.openDialog(e.marketId)}function Q(e){const a=e!=null&&e.marketId?[e.marketId]:C.value;if(a.length===0){d.$modal.msgWarning("请选择要删除的数据");return}d.$modal.confirm(`是否确认删除选中的${a.length}条记录？`).then(()=>te(a.join(","))).then(()=>{c(),d.$modal.msgSuccess("删除成功")}).catch(()=>{})}function X(){d.download("place/market/export",{...o.value},`labor_market_info_${new Date().getTime()}.xlsx`)}function Y(){c()}function Z(){}return de(async()=>{await H(),c()}),(e,a)=>{const l=y("el-button"),p=y("el-tag"),ee=y("el-empty"),g=pe("hasPermi");return i(),S("div",he,[F.value?(i(),u(re,{key:0,columns:w.value,data:_.value,loading:h.value,showIndex:!0,searchColumns:D.value,showOperation:!0,operationLabel:"操作",operationWidth:"280",fixedOperation:!0,ref_key:"tableListRef",ref:W,onSearch:K,onReset:j,defaultPage:{pageSize:x(o).pageSize,currentPage:x(o).pageNum,total:T.value},onCurrentChange:B,onSizeChange:q,onSelectionChange:V},{"menu-left":n(()=>[k((i(),u(l,{type:"primary",class:"custom-btn",onClick:G},{default:n(()=>[s("新 增")]),_:1})),[[g,["place:market:add"]]]),k((i(),u(l,{type:"warning",plain:"",class:"custom-btn",onClick:X},{default:n(()=>[s("导 出")]),_:1})),[[g,["place:market:export"]]])]),marketType:n(({row:r})=>[m(p,{type:$(r.marketType)},{default:n(()=>[s(b(r.marketType),1)]),_:2},1032,["type"])]),isFeatured:n(({row:r})=>[m(p,{type:r.isFeatured?"success":"info"},{default:n(()=>[s(b(r.isFeatured?"是":"否"),1)]),_:2},1032,["type"])]),status:n(({row:r})=>[m(p,{type:E(r.status)},{default:n(()=>[s(b(A(r.status)),1)]),_:2},1032,["type"])]),menu:n(({row:r})=>[ge("div",fe,[k((i(),u(l,{type:"primary",link:"",onClick:M=>J(r)},{default:n(()=>[s("详情")]),_:2},1032,["onClick"])),[[g,["place:market:query"]]]),k((i(),u(l,{type:"success",link:"",onClick:M=>U(r)},{default:n(()=>[s("修改")]),_:2},1032,["onClick"])),[[g,["place:market:edit"]]]),k((i(),u(l,{type:"danger",link:"",onClick:M=>Q(r)},{default:n(()=>[s("删除")]),_:2},1032,["onClick"])),[[g,["place:market:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(i(),S("div",Ce,[m(ee,{description:"Loading table configuration..."})])),m(oe,{ref_key:"marketFormDialogRef",ref:v,formFields:I.value,formOption:O.value,onSubmit:Y,onCancel:Z},null,8,["formFields","formOption"]),m(le,{ref_key:"marketDetailDialogRef",ref:N},null,512)])}}}),Ne=ie(ye,[["__scopeId","data-v-e688ba15"]]);export{Ne as default};
