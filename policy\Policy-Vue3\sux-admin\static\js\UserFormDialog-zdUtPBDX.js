import{F as $}from"./index-BylsdGrt.js";import{V as ee}from"./index-B-A7bGAb.js";import{_ as le,C as ae,d as te,r as f,A as F,f as v,l as p,o as u,k as o,h as x,Z as oe,N as se,a2 as ue,i as c,c as C,L as O,M as D,j as A,p as g,t as _,m as de}from"./index-DP10CBaW.js";const ne={class:"dialog-footer"},ie=ae({name:"UserFormDialog"}),re=Object.assign(ie,{props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({dialogWidth:"900px",dialogHeight:"70vh"})},deptOptions:{type:Array,default:()=>[]},postOptions:{type:Array,default:()=>[]},roleOptions:{type:Array,default:()=>[]}},emits:["submit","cancel"],setup(m,{expose:H,emit:B}){const{proxy:j}=te(),{sys_normal_disable:N,sys_user_sex:fe}=j.useDict("sys_normal_disable","sys_user_sex"),y=m,S=B,h=f(!1),d=f("add"),T=f("新增用户"),w=f(null),l=f({}),n=f(!1),i=f(!1),r=f(!1),E=F(()=>r.value?"100%":y.formOption.dialogWidth||"900px"),U=F(()=>{if(!y.formFields.length)return[];let t=[];return d.value==="add"?t=y.formFields.filter(e=>e.addDisplay!==!1):d.value==="edit"?t=y.formFields.filter(e=>e.editDisplay!==!1):t=y.formFields.filter(e=>e.viewDisplay!==!1),t.filter(e=>typeof e.display=="function"?e.display(l.value):!0)}),X=F(()=>{const t={overflow:"visible",padding:"20px 10px",overflowX:"hidden"};return r.value?{...t,maxHeight:"calc(100vh - 180px)",overflowY:"auto",overflowX:"hidden"}:{...t,maxHeight:y.formOption.dialogHeight||"70vh",overflowY:"auto",overflowX:"hidden",minHeight:"auto"}}),z=()=>{r.value=!r.value},R=(t,e)=>{const b=t.find(k=>k.value===e);return b?b.label:e},Y=(t,e,b={})=>{d.value=t,T.value=e,t==="add"?l.value={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:void 0,userType:"00",status:"0",remark:void 0,postIds:[],roleIds:[]}:(l.value={...b},l.value.userType||(l.value.userType="00")),h.value=!0,r.value=!1},V=()=>{h.value=!1},M=(t,e)=>{t==="userType"&&e==="01"&&(l.value.postIds=[],l.value.roleIds=[])},Z=async()=>{if(!(n.value||i.value)&&w.value)try{n.value=!0,i.value=!0,await w.value.validate(),S("submit",{type:d.value,data:l.value})}catch{n.value=!1,i.value=!1}},q=()=>{S("cancel"),V()},G=()=>{n.value=!1,i.value=!1},J=()=>{l.value={},n.value=!1,i.value=!1,r.value=!1};return H({openDialog:Y,closeDialog:V,onSubmitSuccess:()=>{n.value=!1,i.value=!1,V()},onSubmitError:()=>{n.value=!1,i.value=!1}}),(t,e)=>{const b=v("el-tree-select"),k=v("el-radio"),K=v("el-radio-group"),W=v("el-option"),L=v("el-select"),P=v("el-tag"),I=v("el-button"),Q=v("el-dialog");return u(),p(Q,{modelValue:h.value,"onUpdate:modelValue":e[6]||(e[6]=s=>h.value=s),title:T.value,width:E.value,"destroy-on-close":"","close-on-click-modal":!1,fullscreen:r.value,onClosed:J,onOpen:G,class:"custom-dialog"},{footer:o(()=>[x("span",ne,[c(I,{class:"custom-btn",onClick:z},{default:o(()=>[g(_(r.value?"退出全屏":"全屏显示"),1)]),_:1}),c(I,{class:"custom-btn",onClick:q},{default:o(()=>[g(_(d.value==="view"?"关 闭":"取 消"),1)]),_:1}),d.value!=="view"?(u(),p(I,{key:0,type:"primary",class:"custom-btn",onClick:Z,loading:n.value,disabled:i.value},{default:o(()=>[g(" 确 认 ")]),_:1},8,["loading","disabled"])):de("",!0)])]),default:o(()=>[x("div",{class:se(["dialog-content",{"view-mode":d.value==="view"}]),style:oe(X.value)},[d.value!=="view"?(u(),p($,{key:0,ref_key:"formListRef",ref:w,modelValue:l.value,"onUpdate:modelValue":e[4]||(e[4]=s=>l.value=s),fields:U.value,"is-view":d.value==="view",showActions:!1,labelWidth:m.formOption.labelWidth,inline:!1,onFieldChange:M},ue({deptName:o(({row:s})=>[c(b,{modelValue:l.value.deptId,"onUpdate:modelValue":e[0]||(e[0]=a=>l.value.deptId=a),data:m.deptOptions,props:{value:"id",label:"label",children:"children"},"value-key":"id",placeholder:"请选择归属部门","check-strictly":"",style:{width:"100%"}},null,8,["modelValue","data"])]),status:o(({row:s})=>[c(K,{modelValue:l.value.status,"onUpdate:modelValue":e[1]||(e[1]=a=>l.value.status=a)},{default:o(()=>[(u(!0),C(O,null,D(A(N),a=>(u(),p(k,{key:a.value,value:a.value},{default:o(()=>[g(_(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:2},[l.value.userType==="00"?{name:"postIds",fn:o(({row:s})=>[c(L,{modelValue:l.value.postIds,"onUpdate:modelValue":e[2]||(e[2]=a=>l.value.postIds=a),multiple:"",placeholder:"请选择岗位",style:{width:"100%"}},{default:o(()=>[(u(!0),C(O,null,D(m.postOptions,a=>(u(),p(W,{key:a.postId,label:a.postName,value:a.postId,disabled:a.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),key:"0"}:void 0,l.value.userType==="00"?{name:"roleIds",fn:o(({row:s})=>[c(L,{modelValue:l.value.roleIds,"onUpdate:modelValue":e[3]||(e[3]=a=>l.value.roleIds=a),multiple:"",placeholder:"请选择角色",style:{width:"100%"}},{default:o(()=>[(u(!0),C(O,null,D(m.roleOptions,a=>(u(),p(W,{key:a.roleId,label:a.roleName,value:a.roleId,disabled:a.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),key:"1"}:void 0]),1032,["modelValue","fields","is-view","labelWidth"])):(u(),p(ee,{key:1,modelValue:l.value,"onUpdate:modelValue":e[5]||(e[5]=s=>l.value=s),fields:U.value,labelWidth:m.formOption.labelWidth},{deptName:o(({row:s})=>{var a;return[x("span",null,_(((a=l.value.dept)==null?void 0:a.deptName)||"-"),1)]}),status:o(({row:s})=>[c(P,{type:l.value.status==="0"?"success":"danger"},{default:o(()=>[g(_(R(A(N),l.value.status)),1)]),_:1},8,["type"])]),_:1},8,["modelValue","fields","labelWidth"]))],6)]),_:1},8,["modelValue","title","width","fullscreen"])}}}),be=le(re,[["__scopeId","data-v-22a233d5"]]);export{be as default};
