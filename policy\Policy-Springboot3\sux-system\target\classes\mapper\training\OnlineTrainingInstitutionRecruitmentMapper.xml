<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.OnlineTrainingInstitutionRecruitmentMapper">
    
    <resultMap type="OnlineTrainingInstitutionRecruitment" id="OnlineTrainingInstitutionRecruitmentResult">
        <result property="recruitmentId"    column="recruitment_id"    />
        <result property="recruitmentTitle"    column="recruitment_title"    />
        <result property="recruitmentDescription"    column="recruitment_description"    />
        <result property="trainingCategory"    column="training_category"    />
        <result property="trainingLevel"    column="training_level"    />
        <result property="trainingDuration"    column="training_duration"    />
        <result property="maxParticipants"    column="max_participants"    />
        <result property="trainingFeeMin"    column="training_fee_min"    />
        <result property="trainingFeeMax"    column="training_fee_max"    />
        <result property="trainingLocation"    column="training_location"    />
        <result property="onlineSupport"    column="online_support"    />
        <result property="offlineSupport"    column="offline_support"    />
        <result property="qualificationRequirements"    column="qualification_requirements"    />
        <result property="experienceRequirements"    column="experience_requirements"    />
        <result property="teacherRequirements"    column="teacher_requirements"    />
        <result property="facilityRequirements"    column="facility_requirements"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="applicationStartDate"    column="application_start_date"    />
        <result property="applicationEndDate"    column="application_end_date"    />
        <result property="expectedStartDate"    column="expected_start_date"    />
        <result property="expectedEndDate"    column="expected_end_date"    />
        <result property="recruitmentStatus"    column="recruitment_status"    />
        <result property="isFeatured"    column="is_featured"    />
        <result property="isUrgent"    column="is_urgent"    />
        <result property="viewCount"    column="view_count"    />
        <result property="applicationCount"    column="application_count"    />
        <result property="selectedCount"    column="selected_count"    />
        <result property="budgetAmount"    column="budget_amount"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="evaluationCriteria"    column="evaluation_criteria"    />
        <result property="additionalRequirements"    column="additional_requirements"    />
        <result property="attachmentFiles"    column="attachment_files"    />
        <result property="publisherUserId"    column="publisher_user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createId"    column="create_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateId"    column="update_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOnlineTrainingInstitutionRecruitmentVo">
        select recruitment_id, recruitment_title, recruitment_description, training_category, training_level, training_duration, max_participants, training_fee_min, training_fee_max, training_location, online_support, offline_support, qualification_requirements, experience_requirements, teacher_requirements, facility_requirements, contact_person, contact_phone, contact_email, application_start_date, application_end_date, expected_start_date, expected_end_date, recruitment_status, is_featured, is_urgent, view_count, application_count, selected_count, budget_amount, payment_method, evaluation_criteria, additional_requirements, attachment_files, publisher_user_id, del_flag, create_id, create_time, update_id, update_time, remark from online_training_institution_recruitment
    </sql>

    <select id="selectOnlineTrainingInstitutionRecruitmentList" parameterType="OnlineTrainingInstitutionRecruitment" resultMap="OnlineTrainingInstitutionRecruitmentResult">
        <include refid="selectOnlineTrainingInstitutionRecruitmentVo"/>
        <where>  
            <if test="recruitmentTitle != null  and recruitmentTitle != ''"> and recruitment_title like concat('%', #{recruitmentTitle}, '%')</if>
            <if test="trainingCategory != null  and trainingCategory != ''"> and training_category = #{trainingCategory}</if>
            <if test="trainingLevel != null  and trainingLevel != ''"> and training_level = #{trainingLevel}</if>
            <if test="recruitmentStatus != null  and recruitmentStatus != ''"> and recruitment_status = #{recruitmentStatus}</if>
            <if test="isFeatured != null  and isFeatured != ''"> and is_featured = #{isFeatured}</if>
            <if test="isUrgent != null  and isUrgent != ''"> and is_urgent = #{isUrgent}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person like concat('%', #{contactPerson}, '%')</if>
            <if test="publisherUserId != null "> and publisher_user_id = #{publisherUserId}</if>
            and del_flag = '0'
        </where>
        order by is_featured desc, is_urgent desc, create_time desc
    </select>
    
    <select id="selectOnlineTrainingInstitutionRecruitmentByRecruitmentId" parameterType="Long" resultMap="OnlineTrainingInstitutionRecruitmentResult">
        <include refid="selectOnlineTrainingInstitutionRecruitmentVo"/>
        where recruitment_id = #{recruitmentId} and del_flag = '0'
    </select>
        
    <insert id="insertOnlineTrainingInstitutionRecruitment" parameterType="OnlineTrainingInstitutionRecruitment" useGeneratedKeys="true" keyProperty="recruitmentId">
        insert into online_training_institution_recruitment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recruitmentTitle != null and recruitmentTitle != ''">recruitment_title,</if>
            <if test="recruitmentDescription != null">recruitment_description,</if>
            <if test="trainingCategory != null and trainingCategory != ''">training_category,</if>
            <if test="trainingLevel != null">training_level,</if>
            <if test="trainingDuration != null">training_duration,</if>
            <if test="maxParticipants != null">max_participants,</if>
            <if test="trainingFeeMin != null">training_fee_min,</if>
            <if test="trainingFeeMax != null">training_fee_max,</if>
            <if test="trainingLocation != null">training_location,</if>
            <if test="onlineSupport != null">online_support,</if>
            <if test="offlineSupport != null">offline_support,</if>
            <if test="qualificationRequirements != null">qualification_requirements,</if>
            <if test="experienceRequirements != null">experience_requirements,</if>
            <if test="teacherRequirements != null">teacher_requirements,</if>
            <if test="facilityRequirements != null">facility_requirements,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="applicationStartDate != null">application_start_date,</if>
            <if test="applicationEndDate != null">application_end_date,</if>
            <if test="expectedStartDate != null">expected_start_date,</if>
            <if test="expectedEndDate != null">expected_end_date,</if>
            <if test="recruitmentStatus != null">recruitment_status,</if>
            <if test="isFeatured != null">is_featured,</if>
            <if test="isUrgent != null">is_urgent,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="applicationCount != null">application_count,</if>
            <if test="selectedCount != null">selected_count,</if>
            <if test="budgetAmount != null">budget_amount,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="evaluationCriteria != null">evaluation_criteria,</if>
            <if test="additionalRequirements != null">additional_requirements,</if>
            <if test="attachmentFiles != null">attachment_files,</if>
            <if test="publisherUserId != null">publisher_user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recruitmentTitle != null and recruitmentTitle != ''">#{recruitmentTitle},</if>
            <if test="recruitmentDescription != null">#{recruitmentDescription},</if>
            <if test="trainingCategory != null and trainingCategory != ''">#{trainingCategory},</if>
            <if test="trainingLevel != null">#{trainingLevel},</if>
            <if test="trainingDuration != null">#{trainingDuration},</if>
            <if test="maxParticipants != null">#{maxParticipants},</if>
            <if test="trainingFeeMin != null">#{trainingFeeMin},</if>
            <if test="trainingFeeMax != null">#{trainingFeeMax},</if>
            <if test="trainingLocation != null">#{trainingLocation},</if>
            <if test="onlineSupport != null">#{onlineSupport},</if>
            <if test="offlineSupport != null">#{offlineSupport},</if>
            <if test="qualificationRequirements != null">#{qualificationRequirements},</if>
            <if test="experienceRequirements != null">#{experienceRequirements},</if>
            <if test="teacherRequirements != null">#{teacherRequirements},</if>
            <if test="facilityRequirements != null">#{facilityRequirements},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="applicationStartDate != null">#{applicationStartDate},</if>
            <if test="applicationEndDate != null">#{applicationEndDate},</if>
            <if test="expectedStartDate != null">#{expectedStartDate},</if>
            <if test="expectedEndDate != null">#{expectedEndDate},</if>
            <if test="recruitmentStatus != null">#{recruitmentStatus},</if>
            <if test="isFeatured != null">#{isFeatured},</if>
            <if test="isUrgent != null">#{isUrgent},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="applicationCount != null">#{applicationCount},</if>
            <if test="selectedCount != null">#{selectedCount},</if>
            <if test="budgetAmount != null">#{budgetAmount},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="evaluationCriteria != null">#{evaluationCriteria},</if>
            <if test="additionalRequirements != null">#{additionalRequirements},</if>
            <if test="attachmentFiles != null">#{attachmentFiles},</if>
            <if test="publisherUserId != null">#{publisherUserId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOnlineTrainingInstitutionRecruitment" parameterType="OnlineTrainingInstitutionRecruitment">
        update online_training_institution_recruitment
        <trim prefix="SET" suffixOverrides=",">
            <if test="recruitmentTitle != null and recruitmentTitle != ''">recruitment_title = #{recruitmentTitle},</if>
            <if test="recruitmentDescription != null">recruitment_description = #{recruitmentDescription},</if>
            <if test="trainingCategory != null and trainingCategory != ''">training_category = #{trainingCategory},</if>
            <if test="trainingLevel != null">training_level = #{trainingLevel},</if>
            <if test="trainingDuration != null">training_duration = #{trainingDuration},</if>
            <if test="maxParticipants != null">max_participants = #{maxParticipants},</if>
            <if test="trainingFeeMin != null">training_fee_min = #{trainingFeeMin},</if>
            <if test="trainingFeeMax != null">training_fee_max = #{trainingFeeMax},</if>
            <if test="trainingLocation != null">training_location = #{trainingLocation},</if>
            <if test="onlineSupport != null">online_support = #{onlineSupport},</if>
            <if test="offlineSupport != null">offline_support = #{offlineSupport},</if>
            <if test="qualificationRequirements != null">qualification_requirements = #{qualificationRequirements},</if>
            <if test="experienceRequirements != null">experience_requirements = #{experienceRequirements},</if>
            <if test="teacherRequirements != null">teacher_requirements = #{teacherRequirements},</if>
            <if test="facilityRequirements != null">facility_requirements = #{facilityRequirements},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="applicationStartDate != null">application_start_date = #{applicationStartDate},</if>
            <if test="applicationEndDate != null">application_end_date = #{applicationEndDate},</if>
            <if test="expectedStartDate != null">expected_start_date = #{expectedStartDate},</if>
            <if test="expectedEndDate != null">expected_end_date = #{expectedEndDate},</if>
            <if test="recruitmentStatus != null">recruitment_status = #{recruitmentStatus},</if>
            <if test="isFeatured != null">is_featured = #{isFeatured},</if>
            <if test="isUrgent != null">is_urgent = #{isUrgent},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="applicationCount != null">application_count = #{applicationCount},</if>
            <if test="selectedCount != null">selected_count = #{selectedCount},</if>
            <if test="budgetAmount != null">budget_amount = #{budgetAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="evaluationCriteria != null">evaluation_criteria = #{evaluationCriteria},</if>
            <if test="additionalRequirements != null">additional_requirements = #{additionalRequirements},</if>
            <if test="attachmentFiles != null">attachment_files = #{attachmentFiles},</if>
            <if test="publisherUserId != null">publisher_user_id = #{publisherUserId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where recruitment_id = #{recruitmentId}
    </update>

    <delete id="deleteOnlineTrainingInstitutionRecruitmentByRecruitmentId" parameterType="Long">
        update online_training_institution_recruitment set del_flag = '2' where recruitment_id = #{recruitmentId}
    </delete>

    <delete id="deleteOnlineTrainingInstitutionRecruitmentByRecruitmentIds" parameterType="String">
        update online_training_institution_recruitment set del_flag = '2' where recruitment_id in 
        <foreach item="recruitmentId" collection="array" open="(" separator="," close=")">
            #{recruitmentId}
        </foreach>
    </delete>

    <!-- 发布招募信息 -->
    <update id="publishRecruitment" parameterType="Long">
        update online_training_institution_recruitment 
        set recruitment_status = '1', update_time = now() 
        where recruitment_id = #{recruitmentId} and del_flag = '0'
    </update>

    <!-- 取消招募信息 -->
    <update id="cancelRecruitment" parameterType="Long">
        update online_training_institution_recruitment 
        set recruitment_status = '4', update_time = now() 
        where recruitment_id = #{recruitmentId} and del_flag = '0'
    </update>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount" parameterType="Long">
        update online_training_institution_recruitment 
        set view_count = view_count + 1, update_time = now() 
        where recruitment_id = #{recruitmentId} and del_flag = '0'
    </update>

    <!-- 增加申请次数 -->
    <update id="incrementApplicationCount" parameterType="Long">
        update online_training_institution_recruitment 
        set application_count = application_count + 1, update_time = now() 
        where recruitment_id = #{recruitmentId} and del_flag = '0'
    </update>

    <!-- 更新选中机构数量 -->
    <update id="updateSelectedCount">
        update online_training_institution_recruitment 
        set selected_count = #{selectedCount}, update_time = now() 
        where recruitment_id = #{recruitmentId} and del_flag = '0'
    </update>

    <!-- 获取招募统计信息 -->
    <select id="selectRecruitmentStats" resultMap="OnlineTrainingInstitutionRecruitmentResult">
        select 
            recruitment_status,
            count(*) as view_count,
            sum(application_count) as application_count,
            sum(selected_count) as selected_count
        from online_training_institution_recruitment 
        where del_flag = '0'
        group by recruitment_status
    </select>

    <!-- 批量更新招募状态 -->
    <update id="batchUpdateRecruitmentStatus">
        update online_training_institution_recruitment 
        set recruitment_status = #{status}, update_time = now() 
        where recruitment_id in 
        <foreach item="recruitmentId" collection="recruitmentIds" open="(" separator="," close=")">
            #{recruitmentId}
        </foreach>
        and del_flag = '0'
    </update>

</mapper>
