import{g as H,a as W,u as L}from"./market-CPtm-ZGb.js";import{_ as j,C as q,d as B,r as i,D as O,f as u,l as $,o as S,k as l,i as e,p,h as z}from"./index-DP10CBaW.js";const G={class:"dialog-footer"},J=q({name:"MarketFormDialog"}),K=Object.assign(J,{emits:["submit","cancel"],setup(Q,{expose:N,emit:T}){const{proxy:g}=B(),c=i(null),f=i(!1),C=i(""),k=i(!1),V=i("add"),o=O({marketId:null,marketName:"",marketCode:"",marketType:"",address:"",regionCode:"",regionName:"",contactPerson:"",contactPhone:"",contactEmail:"",operatingHours:"",workerCapacity:0,currentWorkerCount:0,dailyAvgDemand:0,peakDemandTime:"",managementFee:"",serviceFeeRate:"",safetyMeasures:"",description:"",status:"0",isFeatured:0,remark:""}),x={marketName:[{required:!0,message:"市场名称不能为空",trigger:"blur"}],marketType:[{required:!0,message:"市场类型不能为空",trigger:"change"}],address:[{required:!0,message:"市场地址不能为空",trigger:"blur"}]},U=T;function F(){var m;Object.assign(o,{marketId:null,marketName:"",marketCode:"",marketType:"",address:"",regionCode:"",regionName:"",contactPerson:"",contactPhone:"",contactEmail:"",operatingHours:"",workerCapacity:0,currentWorkerCount:0,dailyAvgDemand:0,peakDemandTime:"",managementFee:"",serviceFeeRate:"",safetyMeasures:"",description:"",status:"0",isFeatured:0,remark:""}),(m=c.value)==null||m.resetFields()}function M(m,a=null){V.value=m,C.value=m==="add"?"新增市场信息":"修改市场信息",F(),m==="edit"&&a&&H(a).then(n=>{Object.assign(o,n.data)}).catch(()=>{g.$modal.msgError("获取市场信息失败")}),f.value=!0}function b(){f.value=!1,F()}function P(){b(),U("cancel")}function R(){var m;(m=c.value)==null||m.validate(a=>{if(a){k.value=!0;const n={...o};(V.value==="add"?W(n):L(n)).then(()=>{g.$modal.msgSuccess(V.value==="add"?"新增成功":"修改成功"),b(),U("submit")}).catch(()=>{g.$modal.msgError(V.value==="add"?"新增失败":"修改失败")}).finally(()=>{k.value=!1})}})}return N({openDialog:M}),(m,a)=>{const n=u("el-input"),d=u("el-form-item"),r=u("el-col"),s=u("el-row"),y=u("el-option"),E=u("el-select"),v=u("el-input-number"),_=u("el-radio"),w=u("el-radio-group"),I=u("el-form"),D=u("el-button"),A=u("el-dialog");return S(),$(A,{title:C.value,modelValue:f.value,"onUpdate:modelValue":a[21]||(a[21]=t=>f.value=t),width:"800px","append-to-body":"",onClose:b},{footer:l(()=>[z("div",G,[e(D,{onClick:P},{default:l(()=>[p("取 消")]),_:1}),e(D,{type:"primary",onClick:R,loading:k.value},{default:l(()=>[p("确 定")]),_:1},8,["loading"])])]),default:l(()=>[e(I,{ref_key:"formRef",ref:c,model:o,rules:x,"label-width":"120px"},{default:l(()=>[e(s,{gutter:20},{default:l(()=>[e(r,{span:12},{default:l(()=>[e(d,{label:"市场名称",prop:"marketName"},{default:l(()=>[e(n,{modelValue:o.marketName,"onUpdate:modelValue":a[0]||(a[0]=t=>o.marketName=t),placeholder:"请输入市场名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(d,{label:"市场编码",prop:"marketCode"},{default:l(()=>[e(n,{modelValue:o.marketCode,"onUpdate:modelValue":a[1]||(a[1]=t=>o.marketCode=t),placeholder:"请输入市场编码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(s,{gutter:20},{default:l(()=>[e(r,{span:12},{default:l(()=>[e(d,{label:"市场类型",prop:"marketType"},{default:l(()=>[e(E,{modelValue:o.marketType,"onUpdate:modelValue":a[2]||(a[2]=t=>o.marketType=t),placeholder:"请选择市场类型",style:{width:"100%"}},{default:l(()=>[e(y,{label:"综合市场",value:"综合市场"}),e(y,{label:"专业市场",value:"专业市场"}),e(y,{label:"临时市场",value:"临时市场"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(d,{label:"营业时间",prop:"operatingHours"},{default:l(()=>[e(n,{modelValue:o.operatingHours,"onUpdate:modelValue":a[3]||(a[3]=t=>o.operatingHours=t),placeholder:"请输入营业时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(d,{label:"市场地址",prop:"address"},{default:l(()=>[e(n,{modelValue:o.address,"onUpdate:modelValue":a[4]||(a[4]=t=>o.address=t),placeholder:"请输入市场地址"},null,8,["modelValue"])]),_:1}),e(s,{gutter:20},{default:l(()=>[e(r,{span:12},{default:l(()=>[e(d,{label:"区域代码",prop:"regionCode"},{default:l(()=>[e(n,{modelValue:o.regionCode,"onUpdate:modelValue":a[5]||(a[5]=t=>o.regionCode=t),placeholder:"请输入区域代码"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(d,{label:"区域名称",prop:"regionName"},{default:l(()=>[e(n,{modelValue:o.regionName,"onUpdate:modelValue":a[6]||(a[6]=t=>o.regionName=t),placeholder:"请输入区域名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(s,{gutter:20},{default:l(()=>[e(r,{span:12},{default:l(()=>[e(d,{label:"联系人",prop:"contactPerson"},{default:l(()=>[e(n,{modelValue:o.contactPerson,"onUpdate:modelValue":a[7]||(a[7]=t=>o.contactPerson=t),placeholder:"请输入联系人"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(d,{label:"联系电话",prop:"contactPhone"},{default:l(()=>[e(n,{modelValue:o.contactPhone,"onUpdate:modelValue":a[8]||(a[8]=t=>o.contactPhone=t),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(d,{label:"联系邮箱",prop:"contactEmail"},{default:l(()=>[e(n,{modelValue:o.contactEmail,"onUpdate:modelValue":a[9]||(a[9]=t=>o.contactEmail=t),placeholder:"请输入联系邮箱"},null,8,["modelValue"])]),_:1}),e(s,{gutter:20},{default:l(()=>[e(r,{span:8},{default:l(()=>[e(d,{label:"零工容纳量",prop:"workerCapacity"},{default:l(()=>[e(v,{modelValue:o.workerCapacity,"onUpdate:modelValue":a[10]||(a[10]=t=>o.workerCapacity=t),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(d,{label:"当前零工数",prop:"currentWorkerCount"},{default:l(()=>[e(v,{modelValue:o.currentWorkerCount,"onUpdate:modelValue":a[11]||(a[11]=t=>o.currentWorkerCount=t),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:8},{default:l(()=>[e(d,{label:"日均需求",prop:"dailyAvgDemand"},{default:l(()=>[e(v,{modelValue:o.dailyAvgDemand,"onUpdate:modelValue":a[12]||(a[12]=t=>o.dailyAvgDemand=t),min:0,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(s,{gutter:20},{default:l(()=>[e(r,{span:12},{default:l(()=>[e(d,{label:"管理费用",prop:"managementFee"},{default:l(()=>[e(n,{modelValue:o.managementFee,"onUpdate:modelValue":a[13]||(a[13]=t=>o.managementFee=t),placeholder:"元/人/天"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(d,{label:"服务费率",prop:"serviceFeeRate"},{default:l(()=>[e(n,{modelValue:o.serviceFeeRate,"onUpdate:modelValue":a[14]||(a[14]=t=>o.serviceFeeRate=t),placeholder:"%"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(d,{label:"用工高峰时段",prop:"peakDemandTime"},{default:l(()=>[e(n,{modelValue:o.peakDemandTime,"onUpdate:modelValue":a[15]||(a[15]=t=>o.peakDemandTime=t),placeholder:"请输入用工高峰时段"},null,8,["modelValue"])]),_:1}),e(d,{label:"安全措施",prop:"safetyMeasures"},{default:l(()=>[e(n,{modelValue:o.safetyMeasures,"onUpdate:modelValue":a[16]||(a[16]=t=>o.safetyMeasures=t),type:"textarea",rows:3,placeholder:"请输入安全措施描述"},null,8,["modelValue"])]),_:1}),e(d,{label:"市场描述",prop:"description"},{default:l(()=>[e(n,{modelValue:o.description,"onUpdate:modelValue":a[17]||(a[17]=t=>o.description=t),type:"textarea",rows:3,placeholder:"请输入市场详细描述"},null,8,["modelValue"])]),_:1}),e(s,{gutter:20},{default:l(()=>[e(r,{span:12},{default:l(()=>[e(d,{label:"是否推荐",prop:"isFeatured"},{default:l(()=>[e(w,{modelValue:o.isFeatured,"onUpdate:modelValue":a[18]||(a[18]=t=>o.isFeatured=t)},{default:l(()=>[e(_,{label:1},{default:l(()=>[p("是")]),_:1}),e(_,{label:0},{default:l(()=>[p("否")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:l(()=>[e(d,{label:"状态",prop:"status"},{default:l(()=>[e(w,{modelValue:o.status,"onUpdate:modelValue":a[19]||(a[19]=t=>o.status=t)},{default:l(()=>[e(_,{label:"0"},{default:l(()=>[p("正常")]),_:1}),e(_,{label:"1"},{default:l(()=>[p("停用")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(d,{label:"备注",prop:"remark"},{default:l(()=>[e(n,{modelValue:o.remark,"onUpdate:modelValue":a[20]||(a[20]=t=>o.remark=t),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])}}}),Z=j(K,[["__scopeId","data-v-a93ead22"]]);export{Z as default};
