import{a7 as a}from"./index-DP10CBaW.js";const r=[{label:"立即执行",value:"1"},{label:"执行一次",value:"2"},{label:"放弃执行",value:"3"}],t=[{label:"允许",value:"0"},{label:"禁止",value:"1"}],d=e=>{const{sys_job_group:s,sys_job_status:i}=e.useDict("sys_job_group","sys_job_status");return{dialogWidth:"700px",dialogHeight:"70vh",labelWidth:"140px",column:[{label:"任务编号",prop:"jobId",minWidth:100,align:"center",addDisplay:!1,editDisplay:!1,search:!1},{label:"任务名称",prop:"jobName",search:!0,minWidth:150,rules:[{required:!0,message:"任务名称不能为空",trigger:"blur"}],span:12,showOverflowTooltip:!0},{label:"任务分组",prop:"jobGroup",type:"select",span:12,search:!0,dicData:[{label:"默认",value:"DEFAULT"},{label:"系统",value:"SYSTEM"}],rules:[{required:!0,message:"任务分组不能为空",trigger:"blur"}],minWidth:100,placeholder:"请选择任务分组",defaultValue:"DEFAULT"},{label:"调用目标字符串",prop:"invokeTarget",search:!1,rules:[{required:!0,message:"调用目标字符串不能为空",trigger:"blur"}],span:24,minWidth:200,showOverflowTooltip:!0,formSlot:!0},{label:"cron表达式",prop:"cronExpression",search:!1,rules:[{required:!0,message:"cron执行表达式不能为空",trigger:"change"}],span:24,minWidth:180,showOverflowTooltip:!0,formSlot:!0,tableSlot:!0},{label:"执行策略",prop:"misfirePolicy",type:"select",span:12,dicData:r,placeholder:"请选择执行策略",defaultValue:"1",addDisplay:!0,editDisplay:!0,viewDisplay:!0,showColumn:!1},{label:"是否并发",prop:"concurrent",type:"select",span:12,dicData:t,placeholder:"请选择并发执行",defaultValue:"1",addDisplay:!0,editDisplay:!0,viewDisplay:!0,showColumn:!1},{label:"状态",prop:"status",type:"select",span:24,minWidth:100,search:!0,dicData:[{label:"正常",value:"0"},{label:"暂停",value:"1"}],placeholder:"请选择任务状态",align:"center",defaultValue:"0",slot:!0,addDisplay:!1},{label:"创建时间",prop:"createTime",editDisplay:!1,addDisplay:!1,type:"datetime",minWidth:160,align:"center",searchRange:!0,formatter:(o,l,u)=>a(l,"{y}-{m}-{d} {h}:{i}:{s}")}]}};export{d as c};
