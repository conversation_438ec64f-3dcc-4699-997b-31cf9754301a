import{g as M}from"./market-CPtm-ZGb.js";import{_ as N,C as x,d as V,r as _,D as P,f as i,K as E,l as f,o as g,k as e,G as I,i as t,p as l,t as n,h as j}from"./index-DP10CBaW.js";const A={class:"dialog-footer"},B=x({name:"MarketDetailDialog"}),H=Object.assign(B,{setup(O,{expose:b}){const{proxy:m}=V(),d=_(!1),c=_(!1),a=P({marketId:null,marketName:"",marketCode:"",marketType:"",address:"",regionCode:"",regionName:"",contactPerson:"",contactPhone:"",contactEmail:"",operatingHours:"",workerCapacity:0,currentWorkerCount:0,dailyAvgDemand:0,peakDemandTime:"",managementFee:"",serviceFeeRate:"",safetyMeasures:"",description:"",status:"0",isFeatured:0,viewCount:0,createTime:"",remark:""});function k(r){return{综合市场:"primary",专业市场:"success",临时市场:"warning"}[r]||"info"}function y(r){return r?m.parseTime(r,"{y}-{m}-{d} {h}:{i}:{s}"):""}function p(){Object.assign(a,{marketId:null,marketName:"",marketCode:"",marketType:"",address:"",regionCode:"",regionName:"",contactPerson:"",contactPhone:"",contactEmail:"",operatingHours:"",workerCapacity:0,currentWorkerCount:0,dailyAvgDemand:0,peakDemandTime:"",managementFee:"",serviceFeeRate:"",safetyMeasures:"",description:"",status:"0",isFeatured:0,viewCount:0,createTime:"",remark:""})}function v(r){p(),d.value=!0,r&&(c.value=!0,M(r).then(s=>{Object.assign(a,s.data)}).catch(()=>{m.$modal.msgError("获取市场详情失败")}).finally(()=>{c.value=!1}))}function C(){d.value=!1,p()}return b({openDialog:v}),(r,s)=>{const o=i("el-descriptions-item"),u=i("el-tag"),D=i("el-descriptions"),T=i("el-button"),h=i("el-dialog"),w=E("loading");return g(),f(h,{title:"零工市场详情",modelValue:d.value,"onUpdate:modelValue":s[0]||(s[0]=F=>d.value=F),width:"800px","append-to-body":""},{footer:e(()=>[j("div",A,[t(T,{onClick:C},{default:e(()=>[l("关 闭")]),_:1})])]),default:e(()=>[I((g(),f(D,{column:2,border:""},{default:e(()=>[t(o,{label:"市场名称"},{default:e(()=>[l(n(a.marketName),1)]),_:1}),t(o,{label:"市场编码"},{default:e(()=>[l(n(a.marketCode),1)]),_:1}),t(o,{label:"市场类型"},{default:e(()=>[t(u,{type:k(a.marketType)},{default:e(()=>[l(n(a.marketType),1)]),_:1},8,["type"])]),_:1}),t(o,{label:"营业时间"},{default:e(()=>[l(n(a.operatingHours),1)]),_:1}),t(o,{label:"市场地址",span:2},{default:e(()=>[l(n(a.address),1)]),_:1}),t(o,{label:"区域代码"},{default:e(()=>[l(n(a.regionCode),1)]),_:1}),t(o,{label:"区域名称"},{default:e(()=>[l(n(a.regionName),1)]),_:1}),t(o,{label:"联系人"},{default:e(()=>[l(n(a.contactPerson),1)]),_:1}),t(o,{label:"联系电话"},{default:e(()=>[l(n(a.contactPhone),1)]),_:1}),t(o,{label:"联系邮箱",span:2},{default:e(()=>[l(n(a.contactEmail),1)]),_:1}),t(o,{label:"零工容纳量"},{default:e(()=>[l(n(a.workerCapacity),1)]),_:1}),t(o,{label:"当前零工数量"},{default:e(()=>[l(n(a.currentWorkerCount),1)]),_:1}),t(o,{label:"日均用工需求"},{default:e(()=>[l(n(a.dailyAvgDemand),1)]),_:1}),t(o,{label:"用工高峰时段"},{default:e(()=>[l(n(a.peakDemandTime),1)]),_:1}),t(o,{label:"管理费用"},{default:e(()=>[l(n(a.managementFee)+" 元/人/天",1)]),_:1}),t(o,{label:"服务费率"},{default:e(()=>[l(n(a.serviceFeeRate)+"%",1)]),_:1}),t(o,{label:"是否推荐"},{default:e(()=>[t(u,{type:a.isFeatured?"success":"info"},{default:e(()=>[l(n(a.isFeatured?"是":"否"),1)]),_:1},8,["type"])]),_:1}),t(o,{label:"状态"},{default:e(()=>[t(u,{type:a.status==="0"?"success":"danger"},{default:e(()=>[l(n(a.status==="0"?"正常":"停用"),1)]),_:1},8,["type"])]),_:1}),t(o,{label:"浏览次数"},{default:e(()=>[l(n(a.viewCount),1)]),_:1}),t(o,{label:"创建时间"},{default:e(()=>[l(n(y(a.createTime)),1)]),_:1}),t(o,{label:"安全措施",span:2},{default:e(()=>[l(n(a.safetyMeasures),1)]),_:1}),t(o,{label:"市场描述",span:2},{default:e(()=>[l(n(a.description),1)]),_:1}),t(o,{label:"备注",span:2},{default:e(()=>[l(n(a.remark),1)]),_:1})]),_:1})),[[w,c.value]])]),_:1},8,["modelValue"])}}}),G=N(H,[["__scopeId","data-v-df98c8d1"]]);export{G as default};
