package com.ruoyi.recruitment.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.recruitment.domain.OnlineTrainingInstitutionRecruitment;
import com.ruoyi.recruitment.service.IOnlineTrainingInstitutionRecruitmentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 线上招募培训机构发布Controller
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/recruitment/institution")
public class OnlineTrainingInstitutionRecruitmentController extends BaseController
{
    @Autowired
    private IOnlineTrainingInstitutionRecruitmentService onlineTrainingInstitutionRecruitmentService;

    /**
     * 查询线上招募培训机构发布列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:list')")
    @GetMapping("/list")
    public TableDataInfo list(OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        startPage();
        List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(onlineTrainingInstitutionRecruitment);
        return getDataTable(list);
    }

    /**
     * 导出线上招募培训机构发布列表
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:export')")
    @Log(title = "线上招募培训机构发布", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        List<OnlineTrainingInstitutionRecruitment> list = onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentList(onlineTrainingInstitutionRecruitment);
        ExcelUtil<OnlineTrainingInstitutionRecruitment> util = new ExcelUtil<OnlineTrainingInstitutionRecruitment>(OnlineTrainingInstitutionRecruitment.class);
        util.exportExcel(response, list, "线上招募培训机构发布数据");
    }

    /**
     * 获取线上招募培训机构发布详细信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:query')")
    @GetMapping(value = "/{recruitmentId}")
    public AjaxResult getInfo(@PathVariable("recruitmentId") Long recruitmentId)
    {
        return success(onlineTrainingInstitutionRecruitmentService.selectOnlineTrainingInstitutionRecruitmentByRecruitmentId(recruitmentId));
    }

    /**
     * 新增线上招募培训机构发布
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:add')")
    @Log(title = "线上招募培训机构发布", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.insertOnlineTrainingInstitutionRecruitment(onlineTrainingInstitutionRecruitment));
    }

    /**
     * 修改线上招募培训机构发布
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:edit')")
    @Log(title = "线上招募培训机构发布", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OnlineTrainingInstitutionRecruitment onlineTrainingInstitutionRecruitment)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.updateOnlineTrainingInstitutionRecruitment(onlineTrainingInstitutionRecruitment));
    }

    /**
     * 删除线上招募培训机构发布
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:remove')")
    @Log(title = "线上招募培训机构发布", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recruitmentIds}")
    public AjaxResult remove(@PathVariable Long[] recruitmentIds)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.deleteOnlineTrainingInstitutionRecruitmentByRecruitmentIds(recruitmentIds));
    }

    /**
     * 发布招募信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:publish')")
    @Log(title = "发布招募信息", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{recruitmentId}")
    public AjaxResult publish(@PathVariable Long recruitmentId)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.publishRecruitment(recruitmentId));
    }

    /**
     * 取消招募信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:cancel')")
    @Log(title = "取消招募信息", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{recruitmentId}")
    public AjaxResult cancel(@PathVariable Long recruitmentId)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.cancelRecruitment(recruitmentId));
    }

    /**
     * 增加浏览次数
     */
    @PostMapping("/view/{recruitmentId}")
    public AjaxResult incrementView(@PathVariable Long recruitmentId)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.incrementViewCount(recruitmentId));
    }

    /**
     * 增加申请次数
     */
    @PostMapping("/apply/{recruitmentId}")
    public AjaxResult incrementApplication(@PathVariable Long recruitmentId)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.incrementApplicationCount(recruitmentId));
    }

    /**
     * 更新选中机构数量
     */
    @PutMapping("/selected/{recruitmentId}/{selectedCount}")
    public AjaxResult updateSelectedCount(@PathVariable Long recruitmentId, @PathVariable Integer selectedCount)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.updateSelectedCount(recruitmentId, selectedCount));
    }

    /**
     * 获取招募统计信息
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:stats')")
    @GetMapping("/stats")
    public AjaxResult getStats()
    {
        List<OnlineTrainingInstitutionRecruitment> stats = onlineTrainingInstitutionRecruitmentService.selectRecruitmentStats();
        return success(stats);
    }

    /**
     * 批量更新招募状态
     */
    @PreAuthorize("@ss.hasPermi('recruitment:institution:batch')")
    @Log(title = "批量更新招募状态", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/status")
    public AjaxResult batchUpdateStatus(@RequestBody BatchUpdateStatusRequest request)
    {
        return toAjax(onlineTrainingInstitutionRecruitmentService.batchUpdateRecruitmentStatus(request.getRecruitmentIds(), request.getStatus()));
    }

    /**
     * 批量更新状态请求对象
     */
    public static class BatchUpdateStatusRequest {
        private Long[] recruitmentIds;
        private String status;

        public Long[] getRecruitmentIds() {
            return recruitmentIds;
        }

        public void setRecruitmentIds(Long[] recruitmentIds) {
            this.recruitmentIds = recruitmentIds;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }
}
