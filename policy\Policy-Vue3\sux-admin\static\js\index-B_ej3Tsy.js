import{_ as J,C as W,d as X,r as m,D as Y,I as Z,f as s,K as T,c as R,o as u,G as p,i as e,m as ee,H as te,j as o,k as t,n as le,L as ne,M as ae,l as _,p as d,ah as oe,h as se,t as ue,R as re}from"./index-DP10CBaW.js";import{l as ie,d as ce,a as de,u as me}from"./menu-D5wKpgmc.js";import pe from"./MenuFormDialog-DmJDrnca.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";import"./columnUtils-DYlA-XL_.js";const _e={class:"app-container"},fe=W({name:"Menu"}),he=Object.assign(fe,{setup(ve){const{proxy:r}=X(),{sys_show_hide:ye,sys_normal_disable:N}=r.useDict("sys_show_hide","sys_normal_disable"),V=m([]),w=m(!0),q=m(!0),k=m(!1),C=m(!0),f=m(),x=m(0),E=Y({queryParams:{menuName:void 0,visible:void 0}}),{queryParams:h}=Z(E);function b(){w.value=!0,ie(h.value).then(n=>{V.value=r.handleTree(n.data,"menuId"),w.value=!1})}function D(){b()}function F(){r.resetForm("queryRef"),D()}function M(n){var a;x.value=(n==null?void 0:n.menuId)||0,(a=f.value)==null||a.openDialog("add","新增菜单")}function P(){C.value=!1,k.value=!k.value,re(()=>{C.value=!0})}function B(n){var a;(a=f.value)==null||a.openDialog("edit","修改菜单",n)}function L(n){var a;(a=f.value)==null||a.openDialog("view","查看菜单",n)}async function A({type:n,data:a}){var g,v;try{n==="add"?(await de(a),r.$modal.msgSuccess("新增成功")):n==="edit"&&(await me(a),r.$modal.msgSuccess("修改成功")),(g=f.value)==null||g.onSubmitSuccess(),b()}catch(S){console.error("提交失败:",S),r.$modal.msgError("操作失败"),(v=f.value)==null||v.onSubmitError()}}function K(){x.value=0}function U(n){r.$modal.confirm('是否确认删除名称为"'+n.menuName+'"的数据项?').then(function(){return ce(n.menuId)}).then(()=>{b(),r.$modal.msgSuccess("删除成功")}).catch(()=>{})}return b(),(n,a)=>{const g=s("el-input"),v=s("el-form-item"),S=s("el-option"),j=s("el-select"),i=s("el-button"),Q=s("el-form"),$=s("el-col"),G=s("el-row"),c=s("el-table-column"),H=s("dict-tag"),O=s("el-table"),y=T("hasPermi"),z=T("loading");return u(),R("div",_e,[p(e(Q,{model:o(h),ref:"queryRef",inline:!0},{default:t(()=>[e(v,{label:"菜单名称",prop:"menuName"},{default:t(()=>[e(g,{modelValue:o(h).menuName,"onUpdate:modelValue":a[0]||(a[0]=l=>o(h).menuName=l),placeholder:"请输入菜单名称",clearable:"",style:{width:"200px"},onKeyup:le(D,["enter"])},null,8,["modelValue"])]),_:1}),e(v,{label:"状态",prop:"status"},{default:t(()=>[e(j,{modelValue:o(h).status,"onUpdate:modelValue":a[1]||(a[1]=l=>o(h).status=l),placeholder:"菜单状态",clearable:"",style:{width:"200px"}},{default:t(()=>[(u(!0),R(ne,null,ae(o(N),l=>(u(),_(S,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(i,{type:"primary",class:"custom-btn",onClick:D},{default:t(()=>[d("搜 索")]),_:1}),e(i,{class:"custom-btn",onClick:F},{default:t(()=>[d("重 置")]),_:1})]),_:1})]),_:1},8,["model"]),[[te,o(q)]]),e(G,{gutter:10,class:"mb8"},{default:t(()=>[e($,{span:1.5},{default:t(()=>[p((u(),_(i,{type:"primary",class:"custom-btn",plain:"",onClick:M},{default:t(()=>[d("新 增")]),_:1})),[[y,["system:menu:add"]]])]),_:1}),e($,{span:1.5},{default:t(()=>[e(i,{type:"info",class:"custom-btn",plain:"",onClick:P},{default:t(()=>[d("展开/折叠")]),_:1})]),_:1})]),_:1}),o(C)?p((u(),_(O,{key:0,data:o(V),"row-key":"menuId","default-expand-all":o(k),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:t(()=>[e(c,{prop:"menuName",label:"菜单名称","show-overflow-tooltip":!0,width:"160"}),e(c,{prop:"icon",label:"图标",align:"center",width:"100"},{default:t(l=>[e(o(oe),{"icon-class":l.row.icon},null,8,["icon-class"])]),_:1}),e(c,{prop:"orderNum",label:"排序",width:"60"}),e(c,{prop:"perms",label:"权限标识","show-overflow-tooltip":!0}),e(c,{prop:"component",label:"组件路径","show-overflow-tooltip":!0}),e(c,{prop:"status",label:"状态",width:"80"},{default:t(l=>[e(H,{options:o(N),value:l.row.status},null,8,["options","value"])]),_:1}),e(c,{label:"创建时间",align:"center",width:"160",prop:"createTime"},{default:t(l=>[se("span",null,ue(n.parseTime(l.row.createTime)),1)]),_:1}),e(c,{label:"操作",align:"center",width:"240","class-name":"small-padding fixed-width"},{default:t(l=>[p((u(),_(i,{link:"",type:"primary",onClick:I=>L(l.row)},{default:t(()=>[d("查看")]),_:2},1032,["onClick"])),[[y,["system:menu:query"]]]),p((u(),_(i,{link:"",type:"primary",onClick:I=>B(l.row)},{default:t(()=>[d("修改")]),_:2},1032,["onClick"])),[[y,["system:menu:edit"]]]),p((u(),_(i,{link:"",type:"primary",onClick:I=>M(l.row)},{default:t(()=>[d("新增")]),_:2},1032,["onClick"])),[[y,["system:menu:add"]]]),p((u(),_(i,{link:"",type:"danger",onClick:I=>U(l.row)},{default:t(()=>[d("删除")]),_:2},1032,["onClick"])),[[y,["system:menu:remove"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[z,o(w)]]):ee("",!0),e(pe,{ref_key:"menuFormDialogRef",ref:f,parentMenuId:o(x),onSubmit:A,onCancel:K},null,8,["parentMenuId"])])}}}),De=J(he,[["__scopeId","data-v-9058d222"]]);export{De as default};
