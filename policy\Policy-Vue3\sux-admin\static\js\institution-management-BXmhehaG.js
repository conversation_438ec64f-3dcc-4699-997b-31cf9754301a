import{l as ie,r as ne}from"./institutionApplication-DSgGW-Aq.js";import{T as oe}from"./index-BWWetMd6.js";import{_ as se,C as ue,d as re,r as c,D as pe,I as ce,e as de,f as p,K as me,c as q,o as f,l as b,i as a,k as e,h as k,G as B,m as y,p as l,t as u,j as U}from"./index-DP10CBaW.js";const ve={class:"institution-application-management app-container"},fe={class:"operation-btns"},_e={key:1,class:"loading-placeholder"},be={class:"dialog-footer"},ge={class:"dialog-footer"},he=ue({name:"InstitutionApplicationManagement"}),ye=Object.assign(he,{setup(we){const{proxy:w}=re(),T=c([]),C=c(!0),V=c(0),P=c([]),N=c([]),g=c(!1),S=c(!1),W=c(null),_=c({}),s=c({visible:!1,title:"",form:{applicationId:null,status:"1",reviewComment:""}}),i=c({visible:!1,title:"查看机构申请详情",data:{}}),j=pe({queryParams:{pageNum:1,pageSize:10,institutionName:void 0,contactPhone:void 0,applicationStatus:void 0}}),{queryParams:d}=ce(j);de(async()=>{await E(),v()});const E=async()=>{try{P.value=[{prop:"institutionName",label:"机构名称",minWidth:200,showOverflowTooltip:!0},{prop:"contactPerson",label:"联系人",width:120},{prop:"contactPhone",label:"联系电话",width:150},{prop:"contactEmail",label:"联系邮箱",minWidth:180,showOverflowTooltip:!0},{prop:"institutionAddress",label:"机构地址",minWidth:200,showOverflowTooltip:!0},{prop:"applicationStatus",label:"申请状态",width:120,slot:!0},{prop:"applicationTime",label:"申请时间",width:150,slot:!0},{prop:"reviewTime",label:"审核时间",width:150,slot:!0},{prop:"reviewer",label:"审核人",width:120}],N.value=[{prop:"institutionName",label:"机构名称",type:"input"},{prop:"contactPhone",label:"联系电话",type:"input"},{prop:"applicationStatus",label:"申请状态",type:"select",options:[{label:"待审核",value:"0"},{label:"已通过",value:"1"},{label:"已拒绝",value:"2"},{label:"已取消",value:"3"}]}],S.value=!0}catch(t){S.value=!1,console.error("初始化配置失败:",t)}};function v(){g.value=!0,C.value=!0;let t={...d.value};_.value.createTime&&Array.isArray(_.value.createTime)&&_.value.createTime.length===2&&(t=w.addDateRange(t,_.value.createTime)),ie(t).then(o=>{g.value=!1,C.value=!1,T.value=o.rows,V.value=o.total}).catch(()=>{g.value=!1,C.value=!1})}const F=t=>{_.value={...t};const{createTime:o,...m}=t||{};Object.assign(d.value,m),d.value.pageNum=1,v()},G=()=>{d.value={pageNum:1,pageSize:10,institutionName:void 0,contactPhone:void 0,applicationStatus:void 0},_.value={},v()},K=t=>{d.value.pageNum=t,v()},H=t=>{d.value.pageSize=t,d.value.pageNum=1,v()},J=()=>{},Q=()=>{v()},x=t=>!t&&t!=="0"?"info":{0:"warning",1:"success",2:"danger",3:"info"}[String(t)]||"info",I=t=>!t&&t!=="0"?"未知":{0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"}[String(t)]||"未知",h=t=>t?w.parseTime(t,"{y}-{m}-{d} {h}:{i}:{s}"):"--",X=t=>{i.value.visible=!0,i.value.data={...t}};function R(t,o){s.value.visible=!0,s.value.title=o==="1"?"通过审核":"拒绝审核",s.value.form.applicationId=t.applicationId,s.value.form.status=o,s.value.form.reviewComment=""}function Y(){const t=s.value.form;ne(t.applicationId,t.status,t.reviewComment).then(()=>{s.value.visible=!1,v(),w.$modal.msgSuccess("审核成功")}).catch(()=>{})}return(t,o)=>{const m=p("el-button"),D=p("el-tag"),Z=p("el-empty"),r=p("el-descriptions-item"),L=p("el-link"),ee=p("el-descriptions"),z=p("el-dialog"),A=p("el-radio"),te=p("el-radio-group"),M=p("el-form-item"),ae=p("el-input"),le=p("el-form"),O=me("hasPermi");return f(),q("div",ve,[S.value?(f(),b(oe,{key:0,columns:P.value,data:T.value,loading:g.value,showIndex:!0,searchColumns:N.value,showOperation:!0,operationLabel:"操作",operationWidth:"250",fixedOperation:!0,ref_key:"tableListRef",ref:W,onSearch:F,onReset:G,defaultPage:{pageSize:U(d).pageSize,currentPage:U(d).pageNum,total:V.value},onCurrentChange:K,onSizeChange:H,onSelectionChange:J},{"menu-left":e(()=>[a(m,{type:"primary",class:"custom-btn",onClick:Q},{default:e(()=>[l("刷新")]),_:1})]),applicationStatus:e(({row:n})=>[a(D,{type:x(n.applicationStatus)},{default:e(()=>[l(u(I(n.applicationStatus)),1)]),_:2},1032,["type"])]),applicationTime:e(({row:n})=>[l(u(h(n.applicationTime)),1)]),reviewTime:e(({row:n})=>[l(u(h(n.reviewTime)),1)]),menu:e(({row:n})=>[k("div",fe,[a(m,{type:"primary",link:"",onClick:$=>X(n)},{default:e(()=>[l("查看")]),_:2},1032,["onClick"]),n.applicationStatus==="0"?B((f(),b(m,{key:0,type:"success",link:"",onClick:$=>R(n,"1")},{default:e(()=>[l("通过")]),_:2},1032,["onClick"])),[[O,["training:institution:application:review"]]]):y("",!0),n.applicationStatus==="0"?B((f(),b(m,{key:1,type:"warning",link:"",onClick:$=>R(n,"2")},{default:e(()=>[l("拒绝")]),_:2},1032,["onClick"])),[[O,["training:institution:application:review"]]]):y("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(f(),q("div",_e,[a(Z,{description:"正在加载表格配置..."})])),a(z,{modelValue:i.value.visible,"onUpdate:modelValue":o[1]||(o[1]=n=>i.value.visible=n),title:i.value.title,width:"800px","append-to-body":""},{footer:e(()=>[k("div",be,[a(m,{onClick:o[0]||(o[0]=n=>i.value.visible=!1)},{default:e(()=>[l("关 闭")]),_:1})])]),default:e(()=>[a(ee,{column:2,border:""},{default:e(()=>[a(r,{label:"机构名称"},{default:e(()=>[l(u(i.value.data.institutionName||"--"),1)]),_:1}),a(r,{label:"联系人"},{default:e(()=>[l(u(i.value.data.contactPerson||"--"),1)]),_:1}),a(r,{label:"联系电话"},{default:e(()=>[l(u(i.value.data.contactPhone||"--"),1)]),_:1}),a(r,{label:"联系邮箱"},{default:e(()=>[l(u(i.value.data.contactEmail||"--"),1)]),_:1}),a(r,{label:"机构地址",span:2},{default:e(()=>[l(u(i.value.data.institutionAddress||"--"),1)]),_:1}),a(r,{label:"申请状态"},{default:e(()=>[a(D,{type:x(i.value.data.applicationStatus)},{default:e(()=>[l(u(I(i.value.data.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),a(r,{label:"申请时间"},{default:e(()=>[l(u(h(i.value.data.applicationTime)),1)]),_:1}),a(r,{label:"审核人"},{default:e(()=>[l(u(i.value.data.reviewer||"--"),1)]),_:1}),a(r,{label:"审核时间"},{default:e(()=>[l(u(h(i.value.data.reviewTime)),1)]),_:1}),a(r,{label:"审核意见",span:2},{default:e(()=>[l(u(i.value.data.reviewComment||"--"),1)]),_:1}),a(r,{label:"机构简介",span:2},{default:e(()=>[l(u(i.value.data.institutionDescription||"--"),1)]),_:1}),i.value.data.businessLicense?(f(),b(r,{key:0,label:"营业执照",span:2},{default:e(()=>[a(L,{href:i.value.data.businessLicense,target:"_blank",type:"primary"},{default:e(()=>[l(" 查看营业执照 ")]),_:1},8,["href"])]),_:1})):y("",!0),i.value.data.qualificationCertificate?(f(),b(r,{key:1,label:"资质证书",span:2},{default:e(()=>[a(L,{href:i.value.data.qualificationCertificate,target:"_blank",type:"primary"},{default:e(()=>[l(" 查看资质证书 ")]),_:1},8,["href"])]),_:1})):y("",!0)]),_:1})]),_:1},8,["modelValue","title"]),a(z,{modelValue:s.value.visible,"onUpdate:modelValue":o[5]||(o[5]=n=>s.value.visible=n),title:s.value.title,width:"500px","append-to-body":""},{footer:e(()=>[k("div",ge,[a(m,{onClick:o[4]||(o[4]=n=>s.value.visible=!1)},{default:e(()=>[l("取 消")]),_:1}),a(m,{type:"primary",onClick:Y},{default:e(()=>[l("确 定")]),_:1})])]),default:e(()=>[a(le,{ref:"reviewFormRef",model:s.value.form,"label-width":"80px"},{default:e(()=>[a(M,{label:"审核状态"},{default:e(()=>[a(te,{modelValue:s.value.form.status,"onUpdate:modelValue":o[2]||(o[2]=n=>s.value.form.status=n)},{default:e(()=>[a(A,{value:"1"},{default:e(()=>[l("通过")]),_:1}),a(A,{value:"2"},{default:e(()=>[l("拒绝")]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(M,{label:"审核意见"},{default:e(()=>[a(ae,{modelValue:s.value.form.reviewComment,"onUpdate:modelValue":o[3]||(o[3]=n=>s.value.form.reviewComment=n),type:"textarea",rows:4,placeholder:"请输入审核意见"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),Te=se(ye,[["__scopeId","data-v-d7a6ac61"]]);export{Te as default};
