import{l as fe}from"./order-ZnAGpiqD.js";import{g as ge,s as ve}from"./application-CqX6Rd9f.js";import{T as be}from"./index-BWWetMd6.js";import{_ as _e,d as ye,r as d,D as W,I as he,e as Ae,J as R,f,c as k,l as S,i as a,k as t,h as A,m as C,o as v,p as i,t as o,j as J,x as Ve,y as Se}from"./index-DP10CBaW.js";const Te=x=>(Ve("data-v-3a18b538"),x=x(),Se(),x),Ce={class:"training-application-container app-container"},xe=Te(()=>A("div",{class:"page-header"},[A("h2",{class:"page-title"},"培训报名"),A("p",{class:"page-description"},"查看所有培训项目，提交个人报名申请。已申请的项目不能重复申请")],-1)),Ee={class:"price"},Ne={key:1},ke={class:"operation-btns"},we={key:1,class:"loading-placeholder"},Ie={class:"apply-form"},Pe={class:"dialog-footer"},De={key:0,class:"application-detail"},Le={__name:"signup",setup(x){const{proxy:X}=ye(),z=d(!1),w=d([]),T=d(!1),I=d(!1),P=d(!1),E=d(null),r=d(null),D=d(null),M=d(0),G=d([]),O=d([]),L=d(!1),U=d(!1),H=d(null),V=d({}),K=W({queryParams:{pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"}}),{queryParams:b}=he(K),p=W({orderId:null,applicantName:"",applicantPhone:"",applicantEmail:"",applicantIdCard:"",applicantGender:"",applicantAge:null,applicantEducation:"",applicantExperience:"",applicantAddress:"",applicationNote:""}),Q={applicantName:[{required:!0,message:"请输入申请人姓名",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],applicantPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],applicantEmail:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],applicantIdCard:[{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"请输入正确的身份证号",trigger:"blur"}]};Ae(async()=>{await Y(),y()});const Y=async()=>{try{G.value=[{prop:"orderTitle",label:"培训标题",minWidth:200,showOverflowTooltip:!0},{prop:"trainingType",label:"培训类型",width:120,tableSlot:!0},{prop:"trainingLevel",label:"培训级别",width:120},{prop:"trainingDuration",label:"培训时长",width:120},{prop:"trainingFee",label:"培训费用",width:120,tableSlot:!0},{prop:"trainingTime",label:"培训时间",width:200,tableSlot:!0},{prop:"trainingAddress",label:"培训地址",minWidth:150,showOverflowTooltip:!0},{prop:"participants",label:"报名人数",width:120,tableSlot:!0},{prop:"applicationStatus",label:"申请状态",width:120,tableSlot:!0},{prop:"applicationTime",label:"申请时间",width:150,tableSlot:!0}],O.value=[{prop:"orderTitle",label:"培训标题",type:"input"},{prop:"trainingType",label:"培训类型",type:"input"},{prop:"trainingLevel",label:"培训级别",type:"input"}],U.value=!0}catch(l){U.value=!1,console.error("初始化配置失败:",l)}},y=async()=>{L.value=!0,z.value=!0;try{let l={...b.value};V.value.createTime&&Array.isArray(V.value.createTime)&&V.value.createTime.length===2&&(l=X.addDateRange(l,V.value.createTime));const[n,c]=await Promise.all([fe(l),ge()]),_=n.rows||[],h=c.data||[];M.value=n.total||0,console.log("所有培训订单数据:",_),console.log("已申请培训申请数据:",h);const g=new Map;h.forEach(s=>{g.set(s.orderId,s)}),w.value=_.map(s=>{const u=g.get(s.orderId);return{...s,userApplication:u||null,applicationStatus:u?u.applicationStatus:null,canApply:!u||["2","3"].includes(u.applicationStatus)}}),console.log("最终培训列表:",w.value)}catch(l){R.error("获取培训列表失败"),console.error(l)}finally{L.value=!1,z.value=!1}},Z=l=>{V.value={...l};const{createTime:n,...c}=l||{};Object.assign(b.value,c),b.value.pageNum=1,y()},ee=()=>{b.value={pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingLevel:void 0,orderStatus:"1"},V.value={},y()},ae=l=>{b.value.pageNum=l,y()},le=l=>{b.value.pageSize=l,b.value.pageNum=1,y()},te=()=>{},ne=()=>{y()},pe=l=>{E.value=l,p.orderId=l.orderId,l.userApplication?Object.assign(p,{applicantName:l.userApplication.applicantName,applicantPhone:l.userApplication.applicantPhone,applicantEmail:l.userApplication.applicantEmail,applicantIdCard:l.userApplication.applicantIdCard,applicantGender:l.userApplication.applicantGender,applicantAge:l.userApplication.applicantAge,applicantEducation:l.userApplication.applicantEducation,applicantExperience:l.userApplication.applicantExperience,applicantAddress:l.userApplication.applicantAddress,applicationNote:""}):Object.assign(p,{orderId:l.orderId,applicantName:"",applicantPhone:"",applicantEmail:"",applicantIdCard:"",applicantGender:"",applicantAge:null,applicantEducation:"",applicantExperience:"",applicantAddress:"",applicationNote:""}),T.value=!0},ie=()=>{D.value&&D.value.validate(async l=>{if(l){P.value=!0;try{await ve(p),R.success("申请提交成功，请等待审核"),T.value=!1,await y()}catch(n){R.error(n.msg||"申请提交失败，请稍后重试")}finally{P.value=!1}}})},oe=l=>{r.value=l,I.value=!0},re=l=>{const n=new Date,c=l.registrationDeadline?new Date(l.registrationDeadline):null;if(c&&n>c)return!1;const _=l.currentParticipants||0,h=l.maxParticipants||0;return!(_>=h&&h>0)},se=l=>({技能培训:"primary",管理培训:"success",安全培训:"warning",专业培训:"info"})[l]||"default",F=l=>!l&&l!=="0"?"info":{0:"warning",1:"success",2:"danger",3:"info"}[String(l)]||"info",$=l=>!l&&l!=="0"?"未知":{0:"待审核",1:"已通过",2:"已拒绝",3:"已取消"}[String(l)]||"未知",N=l=>l?new Date(l).toLocaleDateString("zh-CN"):"--";return(l,n)=>{var B;const c=f("el-button"),_=f("el-tag"),h=f("el-empty"),g=f("el-input"),s=f("el-form-item"),u=f("el-option"),j=f("el-select"),ue=f("el-input-number"),de=f("el-form"),q=f("el-dialog"),m=f("el-descriptions-item"),ce=f("el-descriptions");return v(),k("div",Ce,[xe,U.value?(v(),S(be,{key:0,columns:G.value,data:w.value,loading:L.value,showIndex:!0,searchColumns:O.value,showOperation:!0,operationLabel:"操作",operationWidth:"300",fixedOperation:!0,ref_key:"tableListRef",ref:H,onSearch:Z,onReset:ee,defaultPage:{pageSize:J(b).pageSize,currentPage:J(b).pageNum,total:M.value},onCurrentChange:ae,onSizeChange:le,onSelectionChange:te},{"menu-left":t(()=>[a(c,{type:"primary",class:"custom-btn",onClick:ne},{default:t(()=>[i("刷新")]),_:1})]),trainingType:t(({row:e})=>[a(_,{type:se(e.trainingType),size:"small"},{default:t(()=>[i(o(e.trainingType),1)]),_:2},1032,["type"])]),trainingFee:t(({row:e})=>[A("span",Ee,o(e.trainingFee?"￥"+e.trainingFee:"免费"),1)]),trainingTime:t(({row:e})=>[i(o(N(e.startDate))+" 至 "+o(N(e.endDate)),1)]),participants:t(({row:e})=>[i(o(e.currentParticipants||0)+"/"+o(e.maxParticipants||0)+"人 ",1)]),applicationStatus:t(({row:e})=>[e.userApplication&&e.userApplication.applicationStatus?(v(),S(_,{key:0,type:F(e.userApplication.applicationStatus),size:"small"},{default:t(()=>[i(o($(e.userApplication.applicationStatus)),1)]),_:2},1032,["type"])):(v(),k("span",Ne,"未申请"))]),applicationTime:t(({row:e})=>[i(o(e.userApplication?N(e.userApplication.applicationTime):"--"),1)]),menu:t(({row:e})=>[A("div",ke,[!e.userApplication||["2","3"].includes(e.applicationStatus)?(v(),S(c,{key:0,type:"primary",link:"",onClick:me=>pe(e),disabled:!re(e)},{default:t(()=>[i(o(e.userApplication?"重新申请":"立即申请"),1)]),_:2},1032,["onClick","disabled"])):C("",!0),e.userApplication&&!["2","3"].includes(e.applicationStatus)?(v(),S(c,{key:1,type:"primary",link:"",disabled:""},{default:t(()=>[i(" 已申请 ")]),_:1})):C("",!0),e.userApplication?(v(),S(c,{key:2,type:"success",link:"",onClick:me=>oe(e.userApplication)},{default:t(()=>[i(" 查看申请 ")]),_:2},1032,["onClick"])):C("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(v(),k("div",we,[a(h,{description:"正在加载表格配置..."})])),a(q,{modelValue:T.value,"onUpdate:modelValue":n[12]||(n[12]=e=>T.value=e),title:`申请 - ${(B=E.value)==null?void 0:B.orderTitle}`,width:"800px","close-on-click-modal":!1,"append-to-body":""},{footer:t(()=>[A("div",Pe,[a(c,{onClick:n[11]||(n[11]=e=>T.value=!1)},{default:t(()=>[i("取消")]),_:1}),a(c,{type:"primary",loading:P.value,onClick:ie},{default:t(()=>[i(" 提交申请 ")]),_:1},8,["loading"])])]),default:t(()=>[A("div",Ie,[a(de,{ref_key:"applyFormRef",ref:D,model:p,rules:Q,"label-width":"120px"},{default:t(()=>[a(s,{label:"申请培训",prop:"orderId"},{default:t(()=>[a(g,{modelValue:E.value.orderTitle,"onUpdate:modelValue":n[0]||(n[0]=e=>E.value.orderTitle=e),disabled:""},null,8,["modelValue"])]),_:1}),a(s,{label:"申请人姓名",prop:"applicantName"},{default:t(()=>[a(g,{modelValue:p.applicantName,"onUpdate:modelValue":n[1]||(n[1]=e=>p.applicantName=e),placeholder:"请输入申请人姓名"},null,8,["modelValue"])]),_:1}),a(s,{label:"联系电话",prop:"applicantPhone"},{default:t(()=>[a(g,{modelValue:p.applicantPhone,"onUpdate:modelValue":n[2]||(n[2]=e=>p.applicantPhone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1}),a(s,{label:"邮箱地址",prop:"applicantEmail"},{default:t(()=>[a(g,{modelValue:p.applicantEmail,"onUpdate:modelValue":n[3]||(n[3]=e=>p.applicantEmail=e),placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),a(s,{label:"性别",prop:"applicantGender"},{default:t(()=>[a(j,{modelValue:p.applicantGender,"onUpdate:modelValue":n[4]||(n[4]=e=>p.applicantGender=e),placeholder:"请选择性别",style:{width:"100%"}},{default:t(()=>[a(u,{label:"男",value:"男"}),a(u,{label:"女",value:"女"})]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"年龄",prop:"applicantAge"},{default:t(()=>[a(ue,{modelValue:p.applicantAge,"onUpdate:modelValue":n[5]||(n[5]=e=>p.applicantAge=e),min:16,max:100,placeholder:"请输入年龄",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),a(s,{label:"学历",prop:"applicantEducation"},{default:t(()=>[a(j,{modelValue:p.applicantEducation,"onUpdate:modelValue":n[6]||(n[6]=e=>p.applicantEducation=e),placeholder:"请选择学历",style:{width:"100%"}},{default:t(()=>[a(u,{label:"小学",value:"小学"}),a(u,{label:"初中",value:"初中"}),a(u,{label:"中专",value:"中专"}),a(u,{label:"高中",value:"高中"}),a(u,{label:"大专",value:"大专"}),a(u,{label:"本科",value:"本科"}),a(u,{label:"硕士",value:"硕士"}),a(u,{label:"博士",value:"博士"})]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"身份证号",prop:"applicantIdCard"},{default:t(()=>[a(g,{modelValue:p.applicantIdCard,"onUpdate:modelValue":n[7]||(n[7]=e=>p.applicantIdCard=e),placeholder:"请输入身份证号"},null,8,["modelValue"])]),_:1}),a(s,{label:"联系地址",prop:"applicantAddress"},{default:t(()=>[a(g,{modelValue:p.applicantAddress,"onUpdate:modelValue":n[8]||(n[8]=e=>p.applicantAddress=e),placeholder:"请输入联系地址"},null,8,["modelValue"])]),_:1}),a(s,{label:"工作经验",prop:"applicantExperience"},{default:t(()=>[a(g,{modelValue:p.applicantExperience,"onUpdate:modelValue":n[9]||(n[9]=e=>p.applicantExperience=e),type:"textarea",rows:3,placeholder:"请简要描述您的工作经验"},null,8,["modelValue"])]),_:1}),a(s,{label:"申请备注",prop:"applicationNote"},{default:t(()=>[a(g,{modelValue:p.applicationNote,"onUpdate:modelValue":n[10]||(n[10]=e=>p.applicationNote=e),type:"textarea",rows:3,placeholder:"请输入申请备注（选填）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),a(q,{modelValue:I.value,"onUpdate:modelValue":n[13]||(n[13]=e=>I.value=e),title:"申请详情",width:"600px","append-to-body":""},{default:t(()=>[r.value?(v(),k("div",De,[a(ce,{column:2,border:""},{default:t(()=>[a(m,{label:"申请人姓名"},{default:t(()=>[i(o(r.value.applicantName),1)]),_:1}),a(m,{label:"联系电话"},{default:t(()=>[i(o(r.value.applicantPhone),1)]),_:1}),a(m,{label:"邮箱地址"},{default:t(()=>[i(o(r.value.applicantEmail),1)]),_:1}),a(m,{label:"性别"},{default:t(()=>[i(o(r.value.applicantGender),1)]),_:1}),a(m,{label:"年龄"},{default:t(()=>[i(o(r.value.applicantAge),1)]),_:1}),a(m,{label:"学历"},{default:t(()=>[i(o(r.value.applicantEducation),1)]),_:1}),a(m,{label:"身份证号"},{default:t(()=>[i(o(r.value.applicantIdCard),1)]),_:1}),a(m,{label:"联系地址"},{default:t(()=>[i(o(r.value.applicantAddress),1)]),_:1}),a(m,{label:"申请状态"},{default:t(()=>[a(_,{type:F(r.value.applicationStatus)},{default:t(()=>[i(o($(r.value.applicationStatus)),1)]),_:1},8,["type"])]),_:1}),a(m,{label:"申请时间"},{default:t(()=>[i(o(N(r.value.applicationTime)),1)]),_:1}),a(m,{label:"工作经验",span:2},{default:t(()=>[i(o(r.value.applicantExperience||"--"),1)]),_:1}),a(m,{label:"申请备注",span:2},{default:t(()=>[i(o(r.value.applicationNote||"--"),1)]),_:1}),r.value.reviewComment?(v(),S(m,{key:0,label:"审核意见",span:2},{default:t(()=>[i(o(r.value.reviewComment),1)]),_:1})):C("",!0)]),_:1})])):C("",!0)]),_:1},8,["modelValue"])])}}},Ge=_e(Le,[["__scopeId","data-v-3a18b538"]]);export{Ge as default};
