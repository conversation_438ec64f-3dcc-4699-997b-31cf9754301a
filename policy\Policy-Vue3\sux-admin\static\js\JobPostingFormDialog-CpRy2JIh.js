import{_ as Z,r as c,D as q,A as O,f as t,l as n,o,k as s,h as C,Z as G,i as b,c as V,L as w,M as F,p as g,t as K,m as $,R as Q}from"./index-DP10CBaW.js";const X={class:"dialog-footer"},f={__name:"JobPostingFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(d,{expose:E,emit:L}){const R=d,k=L,m=c(!1),x=c(""),y=c("add"),_=c(!1),i=c(null),l=q({}),B=c({}),p=O(()=>y.value==="view"),N=O(()=>{const u={};return R.formFields.forEach(r=>{r.rules&&r.prop&&(u[r.prop]=r.rules)}),u}),T=(u,r,h={})=>{y.value=u,x.value=r,Object.keys(l).forEach(v=>{delete l[v]}),Object.assign(l,h),B.value={...h},m.value=!0,Q(()=>{i.value&&i.value.clearValidate()})},W=()=>{i.value&&i.value.validate(u=>{u&&(_.value=!0,k("submit",{type:y.value,data:{...l}}))})},j=()=>{m.value=!1,k("cancel")};return E({openDialog:T,onSubmitSuccess:()=>{_.value=!1,m.value=!1},onSubmitError:()=>{_.value=!1}}),(u,r)=>{const h=t("el-divider"),v=t("el-col"),U=t("el-input"),z=t("el-input-number"),A=t("el-option"),H=t("el-select"),J=t("el-switch"),S=t("el-date-picker"),M=t("el-form-item"),P=t("el-row"),I=t("el-form"),D=t("el-button"),Y=t("el-dialog");return o(),n(Y,{modelValue:m.value,"onUpdate:modelValue":r[0]||(r[0]=e=>m.value=e),title:x.value,width:d.formOption.dialogWidth||"800px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:s(()=>[C("div",X,[b(D,{onClick:j},{default:s(()=>[g("取 消")]),_:1}),p.value?$("",!0):(o(),n(D,{key:0,type:"primary",loading:_.value,onClick:W},{default:s(()=>[g(" 确 定 ")]),_:1},8,["loading"]))])]),default:s(()=>[C("div",{class:"form-container",style:G({maxHeight:d.formOption.dialogHeight||"60vh",overflowY:"auto"})},[b(I,{ref_key:"formRef",ref:i,model:l,rules:N.value,"label-width":d.formOption.labelWidth||"100px"},{default:s(()=>[b(P,{gutter:20},{default:s(()=>[(o(!0),V(w,null,F(d.formFields,e=>(o(),V(w,{key:e.prop},[e.divider?(o(),n(v,{key:0,span:24,class:"divider-col"},{default:s(()=>[b(h,{"content-position":"left"},{default:s(()=>[g(K(e.label),1)]),_:2},1024)]),_:2},1024)):(o(),n(v,{key:1,span:e.span||12,class:"form-col"},{default:s(()=>[b(M,{label:e.label,prop:e.prop,rules:e.rules},{default:s(()=>[!e.type||e.type==="input"?(o(),n(U,{key:0,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,placeholder:e.placeholder||`请输入${e.label}`,disabled:p.value||e.disabled,maxlength:e.maxlength,"show-word-limit":e.showWordLimit,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","maxlength","show-word-limit"])):e.type==="textarea"?(o(),n(U,{key:1,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,type:"textarea",placeholder:e.placeholder||`请输入${e.label}`,disabled:p.value||e.disabled,rows:e.minRows||4,maxlength:e.maxlength,"show-word-limit":e.showWordLimit,resize:"vertical"},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","rows","maxlength","show-word-limit"])):e.type==="number"?(o(),n(z,{key:2,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,placeholder:e.placeholder||`请输入${e.label}`,disabled:p.value||e.disabled,min:e.min,max:e.max,precision:e.precision,step:e.step||1,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","min","max","precision","step"])):e.type==="select"?(o(),n(H,{key:3,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,placeholder:e.placeholder||`请选择${e.label}`,disabled:p.value||e.disabled,clearable:"",style:{width:"100%"}},{default:s(()=>[(o(!0),V(w,null,F(e.dicData,a=>(o(),n(A,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="switch"?(o(),n(J,{key:4,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,disabled:p.value||e.disabled,"active-value":"0","inactive-value":"1"},null,8,["modelValue","onUpdate:modelValue","disabled"])):e.type==="date"?(o(),n(S,{key:5,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,type:"date",placeholder:e.placeholder||`请选择${e.label}`,disabled:p.value||e.disabled,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="datetime"?(o(),n(S,{key:6,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,type:"datetime",placeholder:e.placeholder||`请选择${e.label}`,disabled:p.value||e.disabled,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])):$("",!0)]),_:2},1032,["label","prop","rules"])]),_:2},1032,["span"]))],64))),128))]),_:1})]),_:1},8,["model","rules","label-width"])],4)]),_:1},8,["modelValue","title","width"])}}},oe=Z(f,[["__scopeId","data-v-f7e88f30"]]);export{oe as default};
