import{Y as y,_ as ie,C as me,d as ce,r as v,D as fe,I as _e,f as o,K as A,c as q,o as p,G as b,i as e,m as S,H as ve,j as a,k as t,n as he,L as K,M as j,l as h,p as m,h as O,t as Q,F as ge,R as be}from"./index-DP10CBaW.js";function G(r){return y({url:"/system/dept/list",method:"get",params:r})}function ye(r){return y({url:"/system/dept/list/exclude/"+r,method:"get"})}function Ve(r){return y({url:"/system/dept/"+r,method:"get"})}function ke(r){return y({url:"/system/dept",method:"post",data:r})}function Ie(r){return y({url:"/system/dept",method:"put",data:r})}function Ne(r){return y({url:"/system/dept/"+r,method:"delete"})}const xe={class:"app-container"},Ce={class:"dialog-footer"},we=me({name:"Dept"}),De=Object.assign(we,{setup(r){const{proxy:s}=ce(),{sys_normal_disable:x}=s.useDict("sys_normal_disable"),F=v([]),_=v(!1),C=v(!0),H=v(!0),w=v(""),D=v([]),U=v(!0),T=v(!0),M=fe({form:{},queryParams:{deptName:void 0,status:void 0},rules:{parentId:[{required:!0,message:"上级部门不能为空",trigger:"blur"}],deptName:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"显示排序不能为空",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:g,form:d,rules:Y}=_e(M);function V(){C.value=!0,G(g.value).then(u=>{F.value=s.handleTree(u.data,"deptId"),C.value=!1})}function z(){_.value=!1,$()}function $(){d.value={deptId:void 0,parentId:void 0,deptName:void 0,orderNum:0,leader:void 0,phone:void 0,email:void 0,status:"0"},s.resetForm("deptRef")}function R(){V()}function J(){s.resetForm("queryRef"),R()}function B(u){$(),G().then(n=>{D.value=s.handleTree(n.data,"deptId")}),u!=null&&(d.value.parentId=u.deptId),_.value=!0,w.value="添加部门"}function W(){T.value=!1,U.value=!U.value,be(()=>{T.value=!0})}function X(u){$(),ye(u.deptId).then(n=>{D.value=s.handleTree(n.data,"deptId")}),Ve(u.deptId).then(n=>{d.value=n.data,_.value=!0,w.value="修改部门"})}function Z(){s.$refs.deptRef.validate(u=>{u&&(d.value.deptId!=null?Ie(d.value).then(n=>{s.$modal.msgSuccess("修改成功"),_.value=!1,V()}):ke(d.value).then(n=>{s.$modal.msgSuccess("新增成功"),_.value=!1,V()}))})}function ee(u){s.$modal.confirm('是否确认删除名称为"'+u.deptName+'"的数据项?').then(function(){return Ne(u.deptId)}).then(()=>{V(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}return V(),(u,n)=>{const k=o("el-input"),i=o("el-form-item"),te=o("el-option"),le=o("el-select"),c=o("el-button"),E=o("el-form"),f=o("el-col"),L=o("el-row"),I=o("el-table-column"),ae=o("dict-tag"),ne=o("el-table"),de=o("el-tree-select"),oe=o("el-input-number"),ue=o("el-radio"),re=o("el-radio-group"),se=o("el-dialog"),N=A("hasPermi"),pe=A("loading");return p(),q("div",xe,[b(e(E,{model:a(g),ref:"queryRef",inline:!0},{default:t(()=>[e(i,{label:"部门名称",prop:"deptName"},{default:t(()=>[e(k,{modelValue:a(g).deptName,"onUpdate:modelValue":n[0]||(n[0]=l=>a(g).deptName=l),placeholder:"请输入部门名称",clearable:"",style:{width:"200px"},onKeyup:he(R,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"状态",prop:"status"},{default:t(()=>[e(le,{modelValue:a(g).status,"onUpdate:modelValue":n[1]||(n[1]=l=>a(g).status=l),placeholder:"部门状态",clearable:"",style:{width:"200px"}},{default:t(()=>[(p(!0),q(K,null,j(a(x),l=>(p(),h(te,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,null,{default:t(()=>[e(c,{type:"primary",class:"custom-btn",onClick:R},{default:t(()=>[m("搜 索")]),_:1}),e(c,{class:"custom-btn",onClick:J},{default:t(()=>[m("重 置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,a(H)]]),e(L,{gutter:10,class:"mb8"},{default:t(()=>[e(f,{span:1.5},{default:t(()=>[b((p(),h(c,{type:"primary",class:"custom-btn",plain:"",onClick:B},{default:t(()=>[m("新 增")]),_:1})),[[N,["system:dept:add"]]])]),_:1}),e(f,{span:1.5},{default:t(()=>[e(c,{type:"info",class:"custom-btn",plain:"",onClick:W},{default:t(()=>[m("展开/折叠")]),_:1})]),_:1})]),_:1}),a(T)?b((p(),h(ne,{key:0,data:a(F),"row-key":"deptId","default-expand-all":a(U),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:t(()=>[e(I,{prop:"deptName",label:"部门名称",width:"260"}),e(I,{prop:"orderNum",label:"排序",width:"200"}),e(I,{prop:"status",label:"状态",width:"100"},{default:t(l=>[e(ae,{options:a(x),value:l.row.status},null,8,["options","value"])]),_:1}),e(I,{label:"创建时间",align:"center",prop:"createTime",width:"200"},{default:t(l=>[O("span",null,Q(u.parseTime(l.row.createTime)),1)]),_:1}),e(I,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(l=>[b((p(),h(c,{link:"",type:"primary",onClick:P=>X(l.row)},{default:t(()=>[m("修改")]),_:2},1032,["onClick"])),[[N,["system:dept:edit"]]]),b((p(),h(c,{link:"",type:"primary",onClick:P=>B(l.row)},{default:t(()=>[m("新增")]),_:2},1032,["onClick"])),[[N,["system:dept:add"]]]),l.row.parentId!=0?b((p(),h(c,{key:0,link:"",type:"danger",onClick:P=>ee(l.row)},{default:t(()=>[m("删除")]),_:2},1032,["onClick"])),[[N,["system:dept:remove"]]]):S("",!0)]),_:1})]),_:1},8,["data","default-expand-all"])),[[pe,a(C)]]):S("",!0),e(se,{title:a(w),modelValue:a(_),"onUpdate:modelValue":n[9]||(n[9]=l=>ge(_)?_.value=l:null),width:"600px","append-to-body":""},{footer:t(()=>[O("div",Ce,[e(c,{type:"primary",class:"custom-btn",onClick:Z},{default:t(()=>[m("确 定")]),_:1}),e(c,{class:"custom-btn",onClick:z},{default:t(()=>[m("取 消")]),_:1})])]),default:t(()=>[e(E,{ref:"deptRef",model:a(d),rules:a(Y),"label-width":"80px"},{default:t(()=>[e(L,null,{default:t(()=>[a(d).parentId!==0?(p(),h(f,{key:0,span:24},{default:t(()=>[e(i,{label:"上级部门",prop:"parentId"},{default:t(()=>[e(de,{modelValue:a(d).parentId,"onUpdate:modelValue":n[2]||(n[2]=l=>a(d).parentId=l),data:a(D),props:{value:"deptId",label:"deptName",children:"children"},"value-key":"deptId",placeholder:"选择上级部门","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})):S("",!0),e(f,{span:12},{default:t(()=>[e(i,{label:"部门名称",prop:"deptName"},{default:t(()=>[e(k,{modelValue:a(d).deptName,"onUpdate:modelValue":n[3]||(n[3]=l=>a(d).deptName=l),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:t(()=>[e(i,{label:"显示排序",prop:"orderNum"},{default:t(()=>[e(oe,{modelValue:a(d).orderNum,"onUpdate:modelValue":n[4]||(n[4]=l=>a(d).orderNum=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:t(()=>[e(i,{label:"负责人",prop:"leader"},{default:t(()=>[e(k,{modelValue:a(d).leader,"onUpdate:modelValue":n[5]||(n[5]=l=>a(d).leader=l),placeholder:"请输入负责人",maxlength:"20"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:t(()=>[e(i,{label:"联系电话",prop:"phone"},{default:t(()=>[e(k,{modelValue:a(d).phone,"onUpdate:modelValue":n[6]||(n[6]=l=>a(d).phone=l),placeholder:"请输入联系电话",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:t(()=>[e(i,{label:"邮箱",prop:"email"},{default:t(()=>[e(k,{modelValue:a(d).email,"onUpdate:modelValue":n[7]||(n[7]=l=>a(d).email=l),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),e(f,{span:12},{default:t(()=>[e(i,{label:"部门状态"},{default:t(()=>[e(re,{modelValue:a(d).status,"onUpdate:modelValue":n[8]||(n[8]=l=>a(d).status=l)},{default:t(()=>[(p(!0),q(K,null,j(a(x),l=>(p(),h(ue,{key:l.value,value:l.value},{default:t(()=>[m(Q(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Te=ie(De,[["__scopeId","data-v-004d6e75"]]);export{Te as default};
