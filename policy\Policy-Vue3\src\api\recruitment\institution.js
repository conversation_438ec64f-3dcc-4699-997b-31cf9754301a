import request from '@/utils/request'

// 查询机构招募列表
export function listInstitutionRecruitment(query) {
  return request({
    url: '/recruitment/institution/list',
    method: 'get',
    params: query
  })
}

// 查询机构招募详细
export function getInstitutionRecruitment(recruitmentId) {
  return request({
    url: '/recruitment/institution/' + recruitmentId,
    method: 'get'
  })
}

// 新增机构招募
export function addInstitutionRecruitment(data) {
  return request({
    url: '/recruitment/institution',
    method: 'post',
    data: data
  })
}

// 修改机构招募
export function updateInstitutionRecruitment(data) {
  return request({
    url: '/recruitment/institution',
    method: 'put',
    data: data
  })
}

// 删除机构招募
export function delInstitutionRecruitment(recruitmentId) {
  return request({
    url: '/recruitment/institution/' + recruitmentId,
    method: 'delete'
  })
}

// 发布机构招募
export function publishInstitutionRecruitment(recruitmentId) {
  return request({
    url: '/recruitment/institution/publish/' + recruitmentId,
    method: 'put'
  })
}

// 取消机构招募
export function cancelInstitutionRecruitment(recruitmentId) {
  return request({
    url: '/recruitment/institution/cancel/' + recruitmentId,
    method: 'put'
  })
}

// 导出机构招募
export function exportInstitutionRecruitment(query) {
  return request({
    url: '/recruitment/institution/export',
    method: 'get',
    params: query
  })
}

// 获取机构招募统计信息
export function getInstitutionRecruitmentStats() {
  return request({
    url: '/recruitment/institution/stats',
    method: 'get'
  })
}

// 批量更新机构招募状态
export function batchUpdateInstitutionRecruitmentStatus(data) {
  return request({
    url: '/recruitment/institution/batch/status',
    method: 'put',
    data: data
  })
}

// 获取机构招募申请列表
export function listInstitutionRecruitmentApplications(recruitmentId, query) {
  return request({
    url: '/recruitment/institution/' + recruitmentId + '/applications',
    method: 'get',
    params: query
  })
}

// 审核机构招募申请
export function reviewInstitutionRecruitmentApplication(applicationId, data) {
  return request({
    url: '/recruitment/institution/application/' + applicationId + '/review',
    method: 'put',
    data: data
  })
}

// 选中机构
export function selectInstitution(recruitmentId, institutionId) {
  return request({
    url: '/recruitment/institution/' + recruitmentId + '/select/' + institutionId,
    method: 'put'
  })
}

// 取消选中机构
export function unselectInstitution(recruitmentId, institutionId) {
  return request({
    url: '/recruitment/institution/' + recruitmentId + '/unselect/' + institutionId,
    method: 'put'
  })
}
