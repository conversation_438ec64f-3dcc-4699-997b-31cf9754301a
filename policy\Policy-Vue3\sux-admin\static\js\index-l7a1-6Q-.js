import{b as fe,A as ge,c as be}from"./ApprovalRecordsDialog-DW515uqd.js";import{l as _e}from"./info-Da-CLVEk.js";import{_ as ve,C as ke,d as Ne,r,D as j,I as he,e as Ce,f,c as S,o as u,l as v,i as e,k as l,h as _,m as L,p as i,t as q,j as z,L as Pe,M as Ae,$ as Ve,a0 as Se}from"./index-DP10CBaW.js";import qe from"./MaterialsDialog-4SWjNsM-.js";import{T as Ie}from"./index-BWWetMd6.js";const Le={class:"policy-container app-container"},Te={key:1},Ue={class:"operation-btns"},Re={key:1,class:"loading-placeholder"},Me={class:"apply-form"},De={class:"required-materials"},ze={class:"material-header"},we={class:"material-info"},Fe={class:"material-name"},$e={class:"material-tags"},Ee={class:"material-upload"},Oe={class:"dialog-footer"},xe=ke({name:"PolicyS"}),Be=Object.assign(xe,{setup(je){const{proxy:c}=Ne(),T=r([]),w=r(!0),F=r(0),U=r([]),$=r([]),R=r(!1),W=r(!0),J=r(null),P=r(!1),I=r(null),M=r(!1),A=r(null),E=r(null),O=r(null),G=j({queryParams:{pageNum:1,pageSize:10,policyName:void 0,policyType:void 0,status:void 0}}),{queryParams:g}=he(G),o=j({policyId:"",applicantName:"",applicantPhone:"",companyName:"",companyCode:"",companyLegalPerson:"",companyAddress:"",companyContactPerson:"",companyContactPhone:"",bankName:"",bankAccountName:"",bankAccountNumber:"",remark:""}),H={applicantName:[{required:!0,message:"请输入申请人姓名",trigger:"blur"}],applicantPhone:[{required:!0,message:"请输入联系电话",trigger:"blur"}],companyName:[{required:!0,message:"请输入企业名称",trigger:"blur"}],companyCode:[{required:!0,message:"请输入统一社会信用代码",trigger:"blur"}],companyLegalPerson:[{required:!0,message:"请输入法定代表人",trigger:"blur"}],companyAddress:[{required:!0,message:"请输入企业注册地址",trigger:"blur"}],companyContactPerson:[{required:!0,message:"请输入企业联系人",trigger:"blur"}],companyContactPhone:[{required:!0,message:"请输入企业联系电话",trigger:"blur"}],bankName:[{required:!0,message:"请输入开户银行名称",trigger:"blur"}],bankAccountName:[{required:!0,message:"请输入银行账户名称",trigger:"blur"}],bankAccountNumber:[{required:!0,message:"请输入银行账号",trigger:"blur"}]},N=r([{name:"《企业新增岗位吸纳就业困难人员、高校毕业生和退役军人社保补贴申领表》",required:!0,files:[]},{name:"企业的营业执照副本复印件",required:!0,files:[]},{name:"退役军人需提供退伍证原件及复印件",required:!1,files:[]},{name:"企业社保缴费凭证",required:!0,files:[]}]),K=()=>{U.value=[{prop:"policyName",label:"政策名称",minWidth:200},{prop:"policyType",label:"政策类型",width:120,tableSlot:!0},{prop:"policyDescription",label:"政策描述",width:300},{prop:"status",label:"政策状态",width:100,tableSlot:!0},{prop:"applicationStatus",label:"申请状态",width:120,tableSlot:!0}],$.value=[{prop:"policyName",label:"政策名称",type:"input"}]};Ce(()=>{K(),k()});const Q=s=>{Object.assign(g.value,s),g.value.pageNum=1,k()},X=()=>{g.value={pageNum:1,pageSize:10,policyName:void 0,policyType:void 0,status:void 0},k()},Y=s=>{g.value.pageNum=s,k()},Z=s=>{g.value.pageSize=s,g.value.pageNum=1,k()},k=async()=>{var s;w.value=!0,R.value=!0;try{const[t,d]=await Promise.all([_e({...g.value}),fe({...g.value})]),m=t.rows||[],h=d.rows||[];F.value=t.total||0,console.log("所有政策数据:",m),console.log("已申请政策数据:",h);const p=new Map;h.forEach(n=>{p.set(n.policyId,{applicationId:n.applicationId,policyId:n.policyId,applicantUserId:n.applicantUserId,applicantName:n.applicantName,applicantPhone:n.applicantPhone,applicationStatus:n.applicationStatus,requiredMaterials:n.requiredMaterials,companyName:n.companyName,companyCode:n.companyCode,companyLegalPerson:n.companyLegalPerson,companyAddress:n.companyAddress,companyContactPerson:n.companyContactPerson,companyContactPhone:n.companyContactPhone,bankName:n.bankName,bankAccountName:n.bankAccountName,bankAccountNumber:n.bankAccountNumber,submitTime:n.submitTime,completeTime:n.completeTime,remark:n.remark})}),T.value=m.map(n=>{const b=p.get(n.policyId);return{policyId:n.policyId,policyName:n.policyName,policyDescription:n.policyDescription,policyType:n.policyType,status:n.status,userApplication:b||null,applicationStatus:b?b.applicationStatus:null,canApply:n.status==="0"&&(!b||["2","5"].includes(b.applicationStatus))}}),console.log("最终政策列表:",T.value)}catch(t){console.error("获取政策列表失败:",t),(s=c==null?void 0:c.$modal)==null||s.msgError("获取政策列表失败")}finally{w.value=!1,R.value=!1}},ee=()=>{},ae=()=>{k()},le=s=>({就业扶持:"success",创业支持:"warning",技能培训:"info",社会保障:"primary",其他:"default"})[s]||"default",oe=s=>({0:"warning",1:"success",2:"danger",3:"warning",4:"success",5:"danger",6:"success"})[s]||"info",te=s=>({0:"待初审",1:"初审通过",2:"初审拒绝",3:"待终审",4:"终审通过",5:"终审拒绝",6:"已完成"})[s]||"未知状态",ne=s=>{I.value=s,o.policyId=s.policyId,x(),ie(),P.value=!0},se=s=>{var t;(t=E.value)==null||t.openDialog(s)},pe=s=>{var t;(t=O.value)==null||t.openDialog(s.applicationId)},x=()=>{N.value.forEach(s=>{s.files=[]})},ie=()=>{N.value.forEach(s=>{s.files||(s.files=[])})},re=(s,t)=>{N.value[t].files=s.fileList||[]},ue=async()=>{var s,t,d;if(A.value)try{if(await A.value.validate(),N.value.filter(p=>p.required&&(!p.files||p.files.length===0)).length>0){(s=c==null?void 0:c.$modal)==null||s.msgError("请上传所有必需的材料文件");return}M.value=!0;const h={policyId:o.policyId,applicantName:o.applicantName,applicantPhone:o.applicantPhone,companyName:o.companyName,companyCode:o.companyCode,companyLegalPerson:o.companyLegalPerson,companyAddress:o.companyAddress,companyContactPerson:o.companyContactPerson,companyContactPhone:o.companyContactPhone,bankName:o.bankName,bankAccountName:o.bankAccountName,bankAccountNumber:o.bankAccountNumber,requiredMaterials:JSON.stringify(N.value),remark:o.remark};await be(h),(t=c==null?void 0:c.$modal)==null||t.msgSuccess("申请提交成功，请等待审核"),P.value=!1,ce(),k()}catch(m){console.error("提交申请失败:",m),(d=c==null?void 0:c.$modal)==null||d.msgError("提交申请失败")}finally{M.value=!1}},ce=()=>{o.policyId="",o.applicantName="",o.applicantPhone="",o.companyName="",o.companyCode="",o.companyLegalPerson="",o.companyAddress="",o.companyContactPerson="",o.companyContactPhone="",o.bankName="",o.bankAccountName="",o.bankAccountNumber="",o.remark="",x(),A.value&&A.value.resetFields()};return(s,t)=>{var B;const d=f("el-button"),m=f("el-tag"),h=f("el-empty"),p=f("el-input"),n=f("el-form-item"),b=f("el-divider"),y=f("el-col"),C=f("el-row"),de=f("el-icon"),me=f("el-form"),ye=f("el-dialog");return u(),S("div",Le,[W.value&&U.value.length>0?(u(),v(Ie,{key:0,columns:U.value,data:T.value,loading:R.value,showIndex:!1,searchColumns:$.value,showOperation:!0,operationLabel:"操作",operationWidth:"200",fixedOperation:!0,ref_key:"tableListRef",ref:J,onSearch:Q,onReset:X,defaultPage:{pageSize:z(g).pageSize,currentPage:z(g).pageNum,total:F.value},onCurrentChange:Y,onSizeChange:Z,onSelectionChange:ee},{"menu-left":l(()=>[e(d,{type:"primary",class:"custom-btn",onClick:ae},{default:l(()=>[i("刷新")]),_:1})]),policyType:l(({row:a})=>[e(m,{type:le(a.policyType),size:"small"},{default:l(()=>[i(q(a.policyType),1)]),_:2},1032,["type"])]),status:l(({row:a})=>[e(m,{type:a.status==="0"?"success":"danger",size:"small"},{default:l(()=>[i(q(a.status==="0"?"正常":"停用"),1)]),_:2},1032,["type"])]),applicationStatus:l(({row:a})=>[a.userApplication?(u(),v(m,{key:0,type:oe(a.applicationStatus),size:"small"},{default:l(()=>[i(q(te(a.applicationStatus)),1)]),_:2},1032,["type"])):(u(),S("span",Te,"未申请"))]),menu:l(({row:a})=>[_("div",Ue,[!a.userApplication||["2","5"].includes(a.applicationStatus)?(u(),v(d,{key:0,type:"primary",link:"",onClick:V=>ne(a),disabled:a.status!=="0"},{default:l(()=>[i(q(a.userApplication?"重新申请":"立即申请"),1)]),_:2},1032,["onClick","disabled"])):L("",!0),a.userApplication&&!["2","5"].includes(a.applicationStatus)?(u(),v(d,{key:1,type:"primary",link:"",disabled:""},{default:l(()=>[i(" 已申请 ")]),_:1})):L("",!0),a.userApplication?(u(),v(d,{key:2,type:"success",link:"",onClick:V=>se(a.userApplication)},{default:l(()=>[i(" 查看申请 ")]),_:2},1032,["onClick"])):L("",!0),a.userApplication?(u(),v(d,{key:3,type:"info",link:"",onClick:V=>pe(a.userApplication)},{default:l(()=>[i(" 审核状态 ")]),_:2},1032,["onClick"])):L("",!0)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(u(),S("div",Re,[e(h,{description:"正在加载表格配置..."})])),e(ye,{modelValue:P.value,"onUpdate:modelValue":t[14]||(t[14]=a=>P.value=a),title:`申请 - ${(B=I.value)==null?void 0:B.policyName}`,width:"1000px","close-on-click-modal":!1,"append-to-body":""},{footer:l(()=>[_("div",Oe,[e(d,{onClick:t[13]||(t[13]=a=>P.value=!1)},{default:l(()=>[i("取消")]),_:1}),e(d,{type:"primary",loading:M.value,onClick:ue},{default:l(()=>[i(" 提交申请 ")]),_:1},8,["loading"])])]),default:l(()=>[_("div",Me,[e(me,{ref_key:"applyFormRef",ref:A,model:o,rules:H,"label-width":"120px"},{default:l(()=>[e(n,{label:"申请政策",prop:"policyId"},{default:l(()=>[e(p,{modelValue:I.value.policyName,"onUpdate:modelValue":t[0]||(t[0]=a=>I.value.policyName=a),disabled:""},null,8,["modelValue"])]),_:1}),e(b,{"content-position":"left"},{default:l(()=>[i("申请人信息")]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(n,{label:"申请人姓名",prop:"applicantName"},{default:l(()=>[e(p,{modelValue:o.applicantName,"onUpdate:modelValue":t[1]||(t[1]=a=>o.applicantName=a),placeholder:"请输入申请人姓名"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(n,{label:"联系电话",prop:"applicantPhone"},{default:l(()=>[e(p,{modelValue:o.applicantPhone,"onUpdate:modelValue":t[2]||(t[2]=a=>o.applicantPhone=a),placeholder:"请输入联系电话"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b,{"content-position":"left"},{default:l(()=>[i("企业基本信息")]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(n,{label:"企业名称",prop:"companyName"},{default:l(()=>[e(p,{modelValue:o.companyName,"onUpdate:modelValue":t[3]||(t[3]=a=>o.companyName=a),placeholder:"请输入企业名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:24},{default:l(()=>[e(n,{label:"统一社会信用代码",prop:"companyCode"},{default:l(()=>[e(p,{modelValue:o.companyCode,"onUpdate:modelValue":t[4]||(t[4]=a=>o.companyCode=a),placeholder:"请输入统一社会信用代码"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(n,{label:"法定代表人",prop:"companyLegalPerson"},{default:l(()=>[e(p,{modelValue:o.companyLegalPerson,"onUpdate:modelValue":t[5]||(t[5]=a=>o.companyLegalPerson=a),placeholder:"请输入法定代表人"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(n,{label:"企业联系人",prop:"companyContactPerson"},{default:l(()=>[e(p,{modelValue:o.companyContactPerson,"onUpdate:modelValue":t[6]||(t[6]=a=>o.companyContactPerson=a),placeholder:"请输入企业联系人"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:12},{default:l(()=>[e(n,{label:"企业联系电话",prop:"companyContactPhone"},{default:l(()=>[e(p,{modelValue:o.companyContactPhone,"onUpdate:modelValue":t[7]||(t[7]=a=>o.companyContactPhone=a),placeholder:"请输入企业联系电话"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:12},{default:l(()=>[e(n,{label:"企业注册地址",prop:"companyAddress"},{default:l(()=>[e(p,{modelValue:o.companyAddress,"onUpdate:modelValue":t[8]||(t[8]=a=>o.companyAddress=a),placeholder:"请输入企业注册地址"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b,{"content-position":"left"},{default:l(()=>[i("银行对公户信息")]),_:1}),e(C,{gutter:20},{default:l(()=>[e(y,{span:8},{default:l(()=>[e(n,{label:"开户银行",prop:"bankName"},{default:l(()=>[e(p,{modelValue:o.bankName,"onUpdate:modelValue":t[9]||(t[9]=a=>o.bankName=a),placeholder:"请输入开户银行名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:8},{default:l(()=>[e(n,{label:"账户名称",prop:"bankAccountName"},{default:l(()=>[e(p,{modelValue:o.bankAccountName,"onUpdate:modelValue":t[10]||(t[10]=a=>o.bankAccountName=a),placeholder:"请输入银行账户名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{span:8},{default:l(()=>[e(n,{label:"银行账号",prop:"bankAccountNumber"},{default:l(()=>[e(p,{modelValue:o.bankAccountNumber,"onUpdate:modelValue":t[11]||(t[11]=a=>o.bankAccountNumber=a),placeholder:"请输入银行账号"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(n,{label:"备注",prop:"remark"},{default:l(()=>[e(p,{modelValue:o.remark,"onUpdate:modelValue":t[12]||(t[12]=a=>o.remark=a),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"])]),_:1}),e(b,{"content-position":"left"},{default:l(()=>[i("所需材料")]),_:1}),_("div",De,[(u(!0),S(Pe,null,Ae(N.value,(a,V)=>(u(),S("div",{class:"material-item",key:V},[_("div",ze,[_("div",we,[e(de,{class:"material-icon"},{default:l(()=>[e(z(Ve))]),_:1}),_("span",Fe,q(a.name),1),_("div",$e,[a.required?(u(),v(m,{key:0,type:"danger",size:"small"},{default:l(()=>[i("必需")]),_:1})):(u(),v(m,{key:1,type:"info",size:"small"},{default:l(()=>[i("可选")]),_:1}))])])]),_("div",Ee,[e(Se,{value:a.files,"onUpdate:value":D=>a.files=D,limit:5,"file-size":0,"file-type":[],"is-show-tip":!1,onFileLoad:D=>re(D,V)},null,8,["value","onUpdate:value","onFileLoad"])])]))),128))])]),_:1},8,["model"])])]),_:1},8,["modelValue","title"]),e(qe,{ref_key:"materialsDialogRef",ref:E},null,512),e(ge,{ref_key:"approvalRecordsDialogRef",ref:O},null,512)])}}}),Qe=ve(Be,[["__scopeId","data-v-d503d813"]]);export{Qe as default};
