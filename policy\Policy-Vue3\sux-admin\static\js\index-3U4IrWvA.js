import{Y as C,_ as ne,C as re,d as se,r as o,D as ce,I as ie,e as pe,f as _,K as ue,c as w,o as r,l as m,i as f,k as t,h as L,G as S,p,t as u,m as he,j as R}from"./index-DP10CBaW.js";import{g as de,e as me}from"./columnUtils-DYlA-XL_.js";import{T as fe}from"./index-BWWetMd6.js";import ge from"./PlaceInfoFormDialog-LTxYDIeo.js";function we(s){return C({url:"/place/info/list",method:"get",params:s})}function be(s){return C({url:"/place/info",method:"post",data:s})}function Ie(s){return C({url:"/place/info",method:"put",data:s})}function ve(s){return C({url:"/place/info/"+s,method:"delete"})}function ye(s){return{columns:[{prop:"placeId",label:"场地ID",width:80,align:"center",showInTable:!0,showInSearch:!1,showInForm:!1},{prop:"placeName",label:"场地名称",minWidth:200,align:"left",showOverflowTooltip:!0,showInTable:!0,showInSearch:!0,showInForm:!0,required:!0,formType:"input",placeholder:"请输入场地名称"},{prop:"placeCode",label:"场地编码",width:120,align:"center",showInTable:!0,showInSearch:!1,showInForm:!0,formType:"input",placeholder:"请输入场地编码"},{prop:"placeType",label:"场地类型",width:100,align:"center",showInTable:!0,showInSearch:!0,showInForm:!0,required:!0,formType:"select",placeholder:"请选择场地类型",slotName:"placeType",options:[{label:"创业园区",value:"创业园区"},{label:"孵化器",value:"孵化器"},{label:"众创空间",value:"众创空间"},{label:"产业园",value:"产业园"}]},{prop:"placeLevel",label:"场地等级",width:100,align:"center",showInTable:!0,showInSearch:!0,showInForm:!0,formType:"select",placeholder:"请选择场地等级",slotName:"placeLevel",options:[{label:"国家级",value:"国家级"},{label:"省级",value:"省级"},{label:"市级",value:"市级"},{label:"区级",value:"区级"}]},{prop:"address",label:"详细地址",minWidth:200,align:"left",showOverflowTooltip:!0,showInTable:!0,showInSearch:!1,showInForm:!0,required:!0,formType:"input",placeholder:"请输入详细地址"},{prop:"regionName",label:"区域",width:100,align:"center",showInTable:!0,showInSearch:!0,showInForm:!1},{prop:"regionCode",label:"区域代码",width:100,align:"center",showInTable:!1,showInSearch:!0,showInForm:!0,formType:"select",placeholder:"请选择区域",options:[{label:"市南区",value:"370202"},{label:"市北区",value:"370203"},{label:"崂山区",value:"370212"}]},{prop:"placeArea",label:"场地面积",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"number",placeholder:"请输入场地面积（平方米）"},{prop:"usableArea",label:"可使用面积",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"number",placeholder:"请输入可使用面积（平方米）"},{prop:"contactPerson",label:"联系人",width:100,align:"center",showInTable:!0,showInSearch:!1,showInForm:!0,required:!0,formType:"input",placeholder:"请输入联系人"},{prop:"contactPhone",label:"联系电话",width:120,align:"center",showInTable:!0,showInSearch:!1,showInForm:!0,required:!0,formType:"input",placeholder:"请输入联系电话"},{prop:"contactEmail",label:"联系邮箱",width:150,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"input",placeholder:"请输入联系邮箱"},{prop:"companyCount",label:"入驻企业",width:100,align:"center",showInTable:!0,showInSearch:!1,showInForm:!0,formType:"number",placeholder:"请输入入驻企业数"},{prop:"availablePositions",label:"可用工位",width:100,align:"center",showInTable:!0,showInSearch:!1,showInForm:!0,formType:"number",placeholder:"请输入可提供工位数"},{prop:"occupiedPositions",label:"已占工位",width:100,align:"center",showInTable:!0,showInSearch:!1,showInForm:!0,formType:"number",placeholder:"请输入已占用工位数"},{prop:"positionUsage",label:"工位使用率",width:120,align:"center",showInTable:!0,showInSearch:!1,showInForm:!1,slotName:"positionUsage"},{prop:"rentRange",label:"租金范围",width:150,align:"center",showInTable:!0,showInSearch:!1,showInForm:!1,slotName:"rentRange"},{prop:"rentPriceMin",label:"最低租金",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"number",placeholder:"元/月/平方米"},{prop:"rentPriceMax",label:"最高租金",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"number",placeholder:"元/月/平方米"},{prop:"operationMode",label:"运营模式",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"select",placeholder:"请选择运营模式",options:[{label:"自营",value:"自营"},{label:"委托运营",value:"委托运营"},{label:"合作运营",value:"合作运营"}]},{prop:"industryDirection",label:"行业方向",width:150,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"input",placeholder:"多个用逗号分隔"},{prop:"description",label:"场地描述",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"textarea",placeholder:"请输入场地详细描述"},{prop:"status",label:"状态",width:80,align:"center",showInTable:!0,showInSearch:!0,showInForm:!0,formType:"select",placeholder:"请选择状态",slotName:"status",options:[{label:"正常",value:"0"},{label:"停用",value:"1"}]},{prop:"isFeatured",label:"是否推荐",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"radio",options:[{label:"是",value:1},{label:"否",value:0}]},{prop:"isOpenSettle",label:"开放入驻",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!0,formType:"radio",options:[{label:"是",value:1},{label:"否",value:0}]},{prop:"viewCount",label:"浏览次数",width:100,align:"center",showInTable:!1,showInSearch:!1,showInForm:!1},{prop:"createTime",label:"创建时间",width:160,align:"center",showInTable:!0,showInSearch:!0,showInForm:!1,formType:"daterange",placeholder:"请选择创建时间"}],formOptions:{labelWidth:"120px",size:"default",rules:{placeName:[{required:!0,message:"场地名称不能为空",trigger:"blur"}],placeType:[{required:!0,message:"场地类型不能为空",trigger:"change"}],address:[{required:!0,message:"详细地址不能为空",trigger:"blur"}],contactPerson:[{required:!0,message:"联系人不能为空",trigger:"blur"}],contactPhone:[{required:!0,message:"联系电话不能为空",trigger:"blur"}]}}}}const Te={class:"place-info-container app-container"},_e={key:0,class:"rent-range"},Se={key:1,class:"rent-range"},Ce={key:2,class:"rent-range"},Fe={class:"position-usage"},Pe={class:"operation-btns"},xe={key:1,class:"loading-placeholder"},ke=re({name:"PlaceInfo"}),Ne=Object.assign(ke,{setup(s){const{proxy:n}=se(),b=o([]),F=o(!0),$=o([]),z=o(!0),E=o(!0),I=o(0),M=o([]),D=o([]),v=o(!1),P=o(!1),x=o({dialogWidth:"1200px",dialogHeight:"80vh"}),A=o(null),y=o(null),O=o([]),d=o({}),V=ce({queryParams:{pageNum:1,pageSize:10,placeName:void 0,placeType:void 0,placeLevel:void 0,regionCode:void 0,status:void 0}}),{queryParams:c}=ie(V);pe(async()=>{await W(),h()});const W=async()=>{try{const e=ye(n),l=await de({baseOption:e,proxy:n}),{tableColumns:i,searchColumns:g,formFields:k,formOptions:N}=me(l);M.value=i,D.value=g,O.value=k,x.value={...x.value,...N},P.value=!0}catch(e){P.value=!1,console.error("初始化配置失败:",e)}};function h(){v.value=!0,F.value=!0;let e={...c.value};d.value.createTime&&Array.isArray(d.value.createTime)&&d.value.createTime.length===2&&(e=n.addDateRange(e,d.value.createTime)),we(e).then(l=>{v.value=!1,F.value=!1,l.code===200?(b.value=l.rows||[],I.value=l.total||0):(b.value=[],I.value=0,n.$modal.msgError(l.msg||"获取场地信息列表失败"))}).catch(l=>{v.value=!1,F.value=!1,b.value=[],I.value=0,console.error("获取场地信息列表失败:",l),n.$modal.msgError("获取场地信息列表失败")})}const B=e=>{d.value=e,c.value.pageNum=1,h()},U=()=>{d.value={},c.value.pageNum=1,h()},j=e=>{c.value.pageNum=e,h()},G=e=>{c.value.pageSize=e,c.value.pageNum=1,h()},H=e=>{$.value=e.map(l=>l.placeId),z.value=e.length!==1,E.value=!e.length},K=()=>{y.value.openDialog("add",{})},Y=e=>{y.value.openDialog("view",e)},J=e=>{y.value.openDialog("edit",e)},Q=async e=>{try{await n.$modal.confirm("确认要删除该场地信息吗？"),await ve(e.placeId),n.$modal.msgSuccess("删除成功"),h()}catch(l){console.error("删除失败:",l)}},X=()=>{n.download("place/info/export",{...c.value},`place_info_${new Date().getTime()}.xlsx`)},Z=async(e,l)=>{try{l==="add"?(await be(e),n.$modal.msgSuccess("新增成功")):l==="edit"&&(await Ie(e),n.$modal.msgSuccess("修改成功")),h()}catch(i){console.error("操作失败:",i),n.$modal.msgError("操作失败")}},ee=()=>{},ae=e=>({创业园区:"primary",孵化器:"success",众创空间:"warning",产业园:"info"})[e]||"default",le=e=>({国家级:"danger",省级:"warning",市级:"primary",区级:"info"})[e]||"default",oe=e=>e==="0"?"success":"danger",te=e=>e==="0"?"正常":"停用";return(e,l)=>{const i=_("el-button"),g=_("el-tag"),k=_("el-progress"),N=_("el-empty"),T=ue("hasPermi");return r(),w("div",Te,[P.value?(r(),m(fe,{key:0,columns:M.value,data:b.value,loading:v.value,showIndex:!0,searchColumns:D.value,showOperation:!0,operationLabel:"操作",operationWidth:"280",fixedOperation:!0,ref_key:"tableListRef",ref:A,onSearch:B,onReset:U,defaultPage:{pageSize:R(c).pageSize,currentPage:R(c).pageNum,total:I.value},onCurrentChange:j,onSizeChange:G,onSelectionChange:H},{"menu-left":t(()=>[S((r(),m(i,{type:"primary",class:"custom-btn",onClick:K},{default:t(()=>[p("新 增")]),_:1})),[[T,["place:info:add"]]]),S((r(),m(i,{type:"warning",plain:"",class:"custom-btn",onClick:X},{default:t(()=>[p("导 出")]),_:1})),[[T,["place:info:export"]]])]),placeType:t(({row:a})=>[f(g,{type:ae(a.placeType)},{default:t(()=>[p(u(a.placeType),1)]),_:2},1032,["type"])]),placeLevel:t(({row:a})=>[f(g,{type:le(a.placeLevel)},{default:t(()=>[p(u(a.placeLevel),1)]),_:2},1032,["type"])]),rentRange:t(({row:a})=>[a.rentPriceMin&&a.rentPriceMax?(r(),w("span",_e," ¥"+u(a.rentPriceMin)+"-"+u(a.rentPriceMax)+"/月/㎡ ",1)):a.rentPriceMin?(r(),w("span",Se," ¥"+u(a.rentPriceMin)+"/月/㎡起 ",1)):(r(),w("span",Ce,"面议"))]),positionUsage:t(({row:a})=>[L("div",Fe,[L("span",null,u(a.occupiedPositions||0)+"/"+u(a.availablePositions||0),1),a.availablePositions>0?(r(),m(k,{key:0,percentage:Math.round((a.occupiedPositions||0)/a.availablePositions*100),"stroke-width":6,"show-text":!1,style:{"margin-top":"4px"}},null,8,["percentage"])):he("",!0)])]),status:t(({row:a})=>[f(g,{type:oe(a.status)},{default:t(()=>[p(u(te(a.status)),1)]),_:2},1032,["type"])]),menu:t(({row:a})=>[L("div",Pe,[f(i,{type:"primary",link:"",onClick:q=>Y(a)},{default:t(()=>[p("查看")]),_:2},1032,["onClick"]),S((r(),m(i,{type:"primary",link:"",onClick:q=>J(a)},{default:t(()=>[p("编辑")]),_:2},1032,["onClick"])),[[T,["place:info:edit"]]]),S((r(),m(i,{type:"danger",link:"",onClick:q=>Q(a)},{default:t(()=>[p("删除")]),_:2},1032,["onClick"])),[[T,["place:info:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(r(),w("div",xe,[f(N,{description:"Loading table configuration..."})])),f(ge,{ref_key:"placeInfoFormDialogRef",ref:y,formFields:O.value,formOption:x.value,onSubmit:Z,onCancel:ee},null,8,["formFields","formOption"])])}}}),qe=ne(Ne,[["__scopeId","data-v-61c20254"]]);export{qe as default};
