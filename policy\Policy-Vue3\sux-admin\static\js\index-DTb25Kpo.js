import{l as oe,p as ue,c as de,d as ce,u as W,a as ge,g as pe}from"./order-ZnAGpiqD.js";import{g as me,e as he}from"./columnUtils-DYlA-XL_.js";import{T as fe}from"./index-BWWetMd6.js";import be from"./TrainingOrderFormDialog-B7FK-9Xs.js";import{C as ve,d as ye,r as n,D as _e,I as Se,e as Ce,R as xe,f as x,K as De,c as q,o as c,l as h,i as _,j as s,k as i,h as Te,G as y,m as H,p as g,t as we}from"./index-DP10CBaW.js";const ke=Y=>{const{sys_yes_no:r}=Y.useDict("sys_yes_no");return{dialogWidth:"1000px",dialogHeight:"70vh",labelWidth:"120px",column:[{label:"基础信息",prop:"divider_basic_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"订单标题",prop:"orderTitle",search:!0,searchSpan:12,minWidth:200,rules:[{required:!0,message:"订单标题不能为空",trigger:"blur"},{min:2,max:200,message:"订单标题长度必须介于 2 和 200 之间",trigger:"blur"}],span:24},{label:"培训类型",prop:"trainingType",search:!0,searchSpan:8,width:120,align:"center",type:"select",dicData:[{label:"技术培训",value:"技术培训"},{label:"管理培训",value:"管理培训"},{label:"职业技能",value:"职业技能"},{label:"安全培训",value:"安全培训"},{label:"合规培训",value:"合规培训"},{label:"其他",value:"其他"}],span:8,rules:[{required:!0,message:"培训类型不能为空",trigger:"change"}]},{label:"培训分类",prop:"trainingCategory",search:!0,searchSpan:8,width:120,align:"center",type:"select",dicData:[{label:"IT技能",value:"IT技能"},{label:"语言培训",value:"语言培训"},{label:"财务管理",value:"财务管理"},{label:"人力资源",value:"人力资源"},{label:"市场营销",value:"市场营销"},{label:"项目管理",value:"项目管理"},{label:"领导力",value:"领导力"},{label:"沟通技巧",value:"沟通技巧"},{label:"其他",value:"其他"}],span:8,rules:[{required:!0,message:"培训分类不能为空",trigger:"change"}]},{label:"培训级别",prop:"trainingLevel",search:!0,searchSpan:8,width:100,align:"center",type:"select",dicData:[{label:"初级",value:"初级"},{label:"中级",value:"中级"},{label:"高级",value:"高级"},{label:"专家级",value:"专家级"}],span:8},{label:"订单状态",prop:"orderStatus",search:!0,searchSpan:8,width:100,align:"center",type:"select",dicData:[{label:"草稿",value:"0",color:"info"},{label:"发布",value:"1",color:"success"},{label:"进行中",value:"2",color:"warning"},{label:"已完成",value:"3",color:"primary"},{label:"已取消",value:"4",color:"danger"}],span:8,slot:!0,rules:[{required:!0,message:"订单状态不能为空",trigger:"change"}]},{label:"是否推荐",prop:"isFeatured",search:!0,searchSpan:8,width:100,align:"center",type:"select",dicData:r,span:8,slot:!0},{label:"订单描述",prop:"orderDescription",search:!1,type:"textarea",span:24,minRows:3,maxRows:6,showWordLimit:!0,maxlength:2e3,rules:[{max:2e3,message:"订单描述不能超过2000个字符",trigger:"blur"}]},{label:"培训详情",prop:"divider_training_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"培训时长(小时)",prop:"trainingDuration",search:!1,width:120,align:"center",type:"number",span:8,min:1,max:9999,rules:[{required:!0,message:"培训时长不能为空",trigger:"blur"},{type:"number",min:1,max:9999,message:"培训时长必须在1-9999小时之间",trigger:"blur"}]},{label:"最大参与人数",prop:"maxParticipants",search:!1,width:120,align:"center",type:"number",span:8,min:1,max:9999,rules:[{required:!0,message:"最大参与人数不能为空",trigger:"blur"},{type:"number",min:1,max:9999,message:"最大参与人数必须在1-9999之间",trigger:"blur"}]},{label:"当前报名人数",prop:"currentParticipants",search:!1,width:120,align:"center",type:"number",span:8,disabled:!0,addDisplay:!1,editDisplay:!1},{label:"培训费用(元)",prop:"trainingFee",search:!1,width:120,align:"center",type:"number",span:8,min:0,precision:2,rules:[{type:"number",min:0,message:"培训费用不能为负数",trigger:"blur"}]},{label:"培训地址",prop:"trainingAddress",search:!1,type:"textarea",span:16,minRows:2,maxRows:4,showWordLimit:!0,maxlength:500,rules:[{max:500,message:"培训地址不能超过500个字符",trigger:"blur"}]},{label:"联系信息",prop:"divider_contact_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"联系人",prop:"contactPerson",search:!0,searchSpan:12,width:120,span:8,rules:[{required:!0,message:"联系人不能为空",trigger:"blur"},{max:50,message:"联系人不能超过50个字符",trigger:"blur"}]},{label:"联系电话",prop:"contactPhone",search:!1,width:140,span:8,rules:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},{label:"联系邮箱",prop:"contactEmail",search:!1,width:180,span:8,rules:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"},{max:100,message:"联系邮箱不能超过100个字符",trigger:"blur"}]},{label:"时间信息",prop:"divider_time_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"开始时间",prop:"startDate",search:!1,width:180,align:"center",type:"datetime",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",span:8,rules:[{required:!0,message:"开始时间不能为空",trigger:"change"}]},{label:"结束时间",prop:"endDate",search:!1,width:180,align:"center",type:"datetime",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",span:8,rules:[{required:!0,message:"结束时间不能为空",trigger:"change"}]},{label:"报名截止时间",prop:"registrationDeadline",search:!1,width:180,align:"center",type:"datetime",format:"YYYY-MM-DD HH:mm:ss",valueFormat:"YYYY-MM-DD HH:mm:ss",span:8,rules:[{required:!0,message:"报名截止时间不能为空",trigger:"change"}]},{label:"其他信息",prop:"divider_other_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"报名要求",prop:"requirements",search:!1,type:"textarea",span:24,minRows:3,maxRows:6,showWordLimit:!0,maxlength:2e3,rules:[{max:2e3,message:"报名要求不能超过2000个字符",trigger:"blur"}]},{label:"证书信息",prop:"certificateInfo",search:!1,type:"textarea",span:24,minRows:2,maxRows:4,showWordLimit:!0,maxlength:500,rules:[{max:500,message:"证书信息不能超过500个字符",trigger:"blur"}]},{label:"系统信息",prop:"divider_system_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0,addDisplay:!1,editDisplay:!1},{label:"备注",prop:"remark",search:!1,type:"textarea",span:24,minRows:2,maxRows:4,showWordLimit:!0,maxlength:500,addDisplay:!1,editDisplay:!1}]}},Oe={class:"training-order-container app-container"},Fe={class:"operation-btns"},Ye={key:1,class:"loading-placeholder"},Re=ve({name:"TrainingOrder"}),We=Object.assign(Re,{setup(Y){const{proxy:r}=ye(),S=n([]),D=n(!0),C=n([]),R=n(!0),A=n(!0),$=n(0),p=n(!0),L=n([]),M=n([]),T=n(!1),w=n(!1),k=n({dialogWidth:"1000px",dialogHeight:"70vh"}),N=n(null),f=n(null),P=n([]),b=n({}),z=_e({queryParams:{pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingCategory:void 0,trainingLevel:void 0,orderStatus:void 0,isFeatured:void 0,contactPerson:void 0}}),{queryParams:o}=Se(z);Ce(async()=>{await V(),d()});const V=async()=>{try{const e=ke(r),a=await me({baseOption:e,proxy:r}),{tableColumns:t,searchColumns:u,formFields:O,formOptions:F}=he(a);L.value=t,M.value=u,P.value=O,k.value={...k.value,...F},w.value=!0}catch(e){w.value=!1,console.error("初始化配置失败:",e)}};function d(){T.value=!0,D.value=!0;let e={...o.value};b.value.createTime&&Array.isArray(b.value.createTime)&&b.value.createTime.length===2&&(e=r.addDateRange(e,b.value.createTime)),oe(e).then(a=>{T.value=!1,D.value=!1,S.value=a.rows,$.value=a.total,xe(()=>{p.value=!1})})}const E=e=>{p.value=!0,b.value={...e};const{createTime:a,...t}=e||{};Object.assign(o.value,t),o.value.pageNum=1,d()},B=()=>{p.value=!0,o.value={pageNum:1,pageSize:10,orderTitle:void 0,trainingType:void 0,trainingCategory:void 0,trainingLevel:void 0,orderStatus:void 0,isFeatured:void 0,contactPerson:void 0},b.value={},d()},j=e=>{p.value=!0,o.value.pageNum=e,d()},U=e=>{p.value=!0,o.value.pageSize=e,o.value.pageNum=1,d()};function G(e){const a=e.orderId||C.value;r.$modal.confirm('是否确认删除培训订单编号为"'+a+'"的数据项？').then(function(){return ce(a)}).then(()=>{d(),r.$modal.msgSuccess("删除成功")}).catch(()=>{})}function K(){r.download("training/order/export",{...o.value},`training_order_${new Date().getTime()}.xlsx`)}function J(e){if(p.value)return;let a=e.isFeatured==="1"?"推荐":"取消推荐";r.$modal.confirm('确认要"'+a+'""'+e.orderTitle+'"培训订单吗?').then(function(){return W(e)}).then(()=>{r.$modal.msgSuccess(a+"成功")}).catch(function(){e.isFeatured=e.isFeatured==="1"?"0":"1"})}function Q(e){r.$modal.confirm('确认要发布"'+e.orderTitle+'"培训订单吗？发布后将对外可见。').then(function(){return ue(e.orderId)}).then(()=>{d(),r.$modal.msgSuccess("发布成功")}).catch(()=>{})}function X(e){r.$modal.confirm('确认要取消"'+e.orderTitle+'"培训订单吗？取消后将不再接受报名。').then(function(){return de(e.orderId)}).then(()=>{d(),r.$modal.msgSuccess("取消成功")}).catch(()=>{})}function Z(e){C.value=e.map(a=>a.orderId),R.value=e.length!=1,A.value=!e.length}const ee=e=>({0:"info",1:"success",2:"warning",3:"primary",4:"danger"})[e]||"info",ae=e=>({0:"草稿",1:"发布",2:"进行中",3:"已完成",4:"已取消"})[e]||"未知",te=e=>{var a;(a=f.value)==null||a.openDialog("view","查看培训订单",e)},I=e=>{const a=e.orderId;pe(a).then(t=>{var u;(u=f.value)==null||u.openDialog("edit","编辑培训订单",t.data)})},re=()=>{var a;const e={orderStatus:"0",isFeatured:"0",currentParticipants:0};(a=f.value)==null||a.openDialog("add","新增培训订单",e)},ne=async e=>{var a,t;try{e.type==="add"?(await ge(e.data),r.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await W(e.data),r.$modal.msgSuccess("修改成功")),(a=f.value)==null||a.onSubmitSuccess(),d()}catch(u){(t=f.value)==null||t.onSubmitError(),console.error("提交失败:",u)}},le=()=>{};function ie(){re()}function se(e){if(e)I(e);else{const a=C.value[0],t=S.value.find(u=>u.orderId===a);t&&I(t)}}return(e,a)=>{const t=x("el-button"),u=x("el-tag"),O=x("el-switch"),F=x("el-empty"),v=De("hasPermi");return c(),q("div",Oe,[s(w)?(c(),h(fe,{key:0,columns:s(L),data:s(S),loading:s(T),showIndex:!0,searchColumns:s(M),showOperation:!0,operationLabel:"操作",operationWidth:"250",fixedOperation:!0,ref_key:"tableListRef",ref:N,onSearch:E,onReset:B,defaultPage:{pageSize:s(o).pageSize,currentPage:s(o).pageNum,total:s($)},onCurrentChange:j,onSizeChange:U,onSelectionChange:Z},{"menu-left":i(()=>[y((c(),h(t,{type:"primary",class:"custom-btn",onClick:ie},{default:i(()=>[g("新 增")]),_:1})),[[v,["training:order:add"]]]),y((c(),h(t,{type:"warning",plain:"",class:"custom-btn",onClick:K},{default:i(()=>[g("导 出")]),_:1})),[[v,["training:order:export"]]])]),orderStatus:i(({row:l})=>[_(u,{type:ee(l.orderStatus)},{default:i(()=>[g(we(ae(l.orderStatus)),1)]),_:2},1032,["type"])]),isFeatured:i(({row:l})=>[_(O,{modelValue:l.isFeatured,"onUpdate:modelValue":m=>l.isFeatured=m,"active-value":"1","inactive-value":"0",onChange:m=>J(l),disabled:s(p)},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),menu:i(({row:l})=>[Te("div",Fe,[_(t,{type:"primary",link:"",onClick:m=>te(l)},{default:i(()=>[g("查看")]),_:2},1032,["onClick"]),y((c(),h(t,{type:"primary",link:"",onClick:m=>se(l)},{default:i(()=>[g("编辑")]),_:2},1032,["onClick"])),[[v,["training:order:edit"]]]),l.orderStatus==="0"?y((c(),h(t,{key:0,type:"success",link:"",onClick:m=>Q(l)},{default:i(()=>[g("发布")]),_:2},1032,["onClick"])),[[v,["training:order:publish"]]]):H("",!0),["0","1","2"].includes(l.orderStatus)?y((c(),h(t,{key:1,type:"warning",link:"",onClick:m=>X(l)},{default:i(()=>[g("取消")]),_:2},1032,["onClick"])),[[v,["training:order:cancel"]]]):H("",!0),y((c(),h(t,{type:"danger",link:"",onClick:m=>G(l)},{default:i(()=>[g("删除")]),_:2},1032,["onClick"])),[[v,["training:order:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(c(),q("div",Ye,[_(F,{description:"正在加载表格配置..."})])),_(be,{ref_key:"orderFormDialogRef",ref:f,formFields:s(P),formOption:s(k),onSubmit:ne,onCancel:le},null,8,["formFields","formOption"])])}}});export{We as default};
