import{Y as _,a7 as se,_ as oe,C as le,d as ne,r as a,e as re,f as T,K as ie,c as W,o as r,l as m,i as w,k as i,h as R,t as ce,G as v,p as f}from"./index-DP10CBaW.js";import{g as ue,e as de}from"./columnUtils-DYlA-XL_.js";import{T as me}from"./index-BWWetMd6.js";import pe from"./PostFormDialog-BE0uX0q8.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";function he(l){return _({url:"/system/post/list",method:"get",params:l})}function V(l){return _({url:"/system/post/"+l,method:"get"})}function ge(l){return _({url:"/system/post",method:"post",data:l})}function z(l){return _({url:"/system/post",method:"put",data:l})}function ve(l){return _({url:"/system/post/"+l,method:"delete"})}const fe=l=>{const{sys_normal_disable:t}=l.useDict("sys_normal_disable");return{dialogWidth:"600px",dialogHeight:"65vh",labelWidth:"100px",column:[{label:"基础信息",prop:"divider_basic_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"岗位编号",prop:"postId",minWidth:100,align:"center",addDisplay:!1,editDisplay:!1,viewDisplay:!0,search:!1},{label:"岗位名称",prop:"postName",search:!0,rules:[{required:!0,message:"岗位名称不能为空",trigger:"blur"},{min:2,max:50,message:"岗位名称长度必须介于 2 和 50 之间",trigger:"blur"}],span:12,minWidth:150,placeholder:"请输入岗位名称",showOverflowTooltip:!0},{label:"岗位编码",prop:"postCode",search:!0,rules:[{required:!0,message:"岗位编码不能为空",trigger:"blur"},{min:2,max:64,message:"岗位编码长度必须介于 2 和 64 之间",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"岗位编码只能包含字母、数字和下划线",trigger:"blur"}],span:12,minWidth:150,placeholder:"请输入岗位编码",showOverflowTooltip:!0},{label:"岗位顺序",prop:"postSort",type:"number",rules:[{required:!0,message:"岗位顺序不能为空",trigger:"blur"}],span:12,minWidth:120,align:"center",min:0,max:999,controlsPosition:"right",placeholder:"请输入岗位顺序",defaultValue:0},{label:"岗位状态",prop:"status",type:"radio",span:12,minWidth:100,search:!0,dicData:t,slot:!0,defaultValue:"0"},{label:"创建时间",prop:"createTime",editDisplay:!1,addDisplay:!1,type:"datetime",minWidth:180,align:"center",search:!0,searchRange:!0,formatter:(E,k,y)=>se(y,"{y}-{m}-{d} {h}:{i}:{s}")},{label:"详细信息",prop:"divider_detail_info",formSlot:!0,headerAlign:"left",labelWidth:0,span:24,divider:!0},{label:"备注",prop:"remark",type:"textarea",minRows:3,maxRows:6,span:24,showColumn:!1,placeholder:"请输入备注信息",showWordLimit:!0,maxlength:500}]}},_e={class:"post-container app-container"},ye={class:"operation-btns"},be={key:1},Ce={key:1,class:"loading-placeholder"},xe=le({name:"Post"}),ke=Object.assign(xe,{setup(l){const{proxy:t}=ne(),{sys_normal_disable:E}=t.useDict("sys_normal_disable"),k=a([]),y=a([]),b=a(!1),S=a(!1),L=a(null),p=a(null),h=a(1),$=a(10),F=a(0),C=a({}),N=a([]),D=a([]),q=a(!0),A=a(!0),x=a(!1),P=a({dialogWidth:"600px",dialogHeight:"65vh"}),O=a([]);re(async()=>{await B(),c()});const B=async()=>{try{const e=fe(t),o=await ue({baseOption:e,proxy:t}),{tableColumns:s,searchColumns:u,formFields:I,formOptions:d}=de(o);k.value=s,y.value=u,O.value=I,P.value={...P.value,...d},S.value=!0}catch(e){S.value=!1,console.error("初始化配置失败:",e),t.$modal.msgError("表格配置加载失败")}},c=()=>{x.value=!0,b.value=!0;const e={pageNum:h.value,pageSize:$.value,...C.value};he(e).then(o=>{N.value=o.rows,F.value=o.total,b.value=!1,setTimeout(()=>{x.value=!1},100)}).catch(()=>{b.value=!1,x.value=!1,t.$modal.msgError("数据加载失败")})},j=e=>{C.value=e,h.value=1,c()},H=()=>{C.value={},h.value=1,c()},U=e=>{h.value=e,c()},G=e=>{$.value=e,h.value=1,c()},K=e=>{D.value=e,q.value=e.length!==1,A.value=!e.length},M=e=>t.$auth.hasPermi(e),Y=e=>{if(x.value||!e||!e.postId)return;const o=e.postName||`ID为${e.postId}的岗位`||"该岗位",s=e.status==="0"?"启用":"停用";t.$modal.confirm(`确认要${s}"${o}"岗位吗？`).then(()=>z(e)).then(()=>{t.$modal.msgSuccess(`${s}成功`),c()}).catch(()=>{e.status=e.status==="0"?"1":"0"})},Z=()=>{p.value.openDialog("add","新增岗位",{postSort:0,status:"0"})},J=e=>{const o=e.postId;V(o).then(s=>{p.value.openDialog("view","查看岗位",s.data)}).catch(()=>{t.$modal.msgError("数据加载失败")})},Q=e=>{const o=e.postId;V(o).then(s=>{p.value.openDialog("edit","编辑岗位",s.data)}).catch(()=>{t.$modal.msgError("数据加载失败")})},X=e=>{const o=e?[e.postId]:D.value.map(u=>u.postId),s=e?[e.postName]:D.value.map(u=>u.postName);t.$modal.confirm(`是否确认删除岗位"${s.join("、")}"？`).then(()=>ve(o.join(","))).then(()=>{c(),t.$modal.msgSuccess("删除成功")}).catch(()=>{})},ee=()=>{t.download("system/post/export",{...C.value},`post_${new Date().getTime()}.xlsx`)},te=async e=>{try{e.type==="add"?(await ge(e.data),t.$modal.msgSuccess("新增成功")):e.type==="edit"&&(await z(e.data),t.$modal.msgSuccess("修改成功")),p.value.closeDialog(),c()}catch(o){console.error("提交失败:",o)}},ae=()=>{p.value.closeDialog()};return(e,o)=>{const s=T("el-button"),u=T("el-switch"),I=T("el-empty"),d=ie("hasPermi");return r(),W("div",_e,[S.value?(r(),m(me,{key:0,columns:k.value,data:N.value,loading:b.value,showIndex:!0,searchColumns:y.value,showOperation:!0,operationLabel:"操作",operationWidth:"200",fixedOperation:!0,ref_key:"tableListRef",ref:L,onSearch:j,onReset:H,defaultPage:{pageSize:$.value,currentPage:h.value,total:F.value},onCurrentChange:U,onSizeChange:G,onSelectionChange:K},{"menu-left":i(()=>[v((r(),m(s,{type:"primary",class:"custom-btn",onClick:Z},{default:i(()=>[f("新 增")]),_:1})),[[d,["system:post:add"]]]),v((r(),m(s,{type:"warning",class:"custom-btn",plain:"",onClick:ee},{default:i(()=>[f("导 出")]),_:1})),[[d,["system:post:export"]]])]),menu:i(({row:n})=>[R("div",ye,[v((r(),m(s,{type:"primary",link:"",onClick:g=>J(n)},{default:i(()=>[f("查看")]),_:2},1032,["onClick"])),[[d,["system:post:query"]]]),v((r(),m(s,{type:"primary",link:"",onClick:g=>Q(n)},{default:i(()=>[f("编辑")]),_:2},1032,["onClick"])),[[d,["system:post:edit"]]]),v((r(),m(s,{type:"danger",link:"",onClick:g=>X(n)},{default:i(()=>[f("删除")]),_:2},1032,["onClick"])),[[d,["system:post:remove"]]])])]),status:i(({row:n})=>[n&&n.postId&&n.postName?(r(),m(u,{key:0,modelValue:n.status,"onUpdate:modelValue":g=>n.status=g,"active-value":"0","inactive-value":"1",onChange:g=>Y(n),disabled:!M(["system:post:edit"])},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])):(r(),W("span",be,"-"))]),createTime:i(({row:n})=>[R("span",null,ce(e.parseTime(n.createTime)),1)]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(r(),W("div",Ce,[w(I,{description:"正在加载表格配置..."})])),w(pe,{ref_key:"postFormDialogRef",ref:p,formFields:O.value,formOption:P.value,onSubmit:te,onCancel:ae},null,8,["formFields","formOption"])])}}}),We=oe(ke,[["__scopeId","data-v-efc49c32"]]);export{We as default};
