package com.ruoyi.recruitment.service;

import java.util.List;
import com.ruoyi.recruitment.domain.InstitutionRecruitmentApplication;

/**
 * 机构招募申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface IInstitutionRecruitmentApplicationService 
{
    /**
     * 查询机构招募申请
     * 
     * @param applicationId 机构招募申请主键
     * @return 机构招募申请
     */
    public InstitutionRecruitmentApplication selectInstitutionRecruitmentApplicationByApplicationId(Long applicationId);

    /**
     * 查询机构招募申请列表
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 机构招募申请集合
     */
    public List<InstitutionRecruitmentApplication> selectInstitutionRecruitmentApplicationList(InstitutionRecruitmentApplication institutionRecruitmentApplication);

    /**
     * 新增机构招募申请
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 结果
     */
    public int insertInstitutionRecruitmentApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication);

    /**
     * 修改机构招募申请
     * 
     * @param institutionRecruitmentApplication 机构招募申请
     * @return 结果
     */
    public int updateInstitutionRecruitmentApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication);

    /**
     * 批量删除机构招募申请
     * 
     * @param applicationIds 需要删除的机构招募申请主键集合
     * @return 结果
     */
    public int deleteInstitutionRecruitmentApplicationByApplicationIds(Long[] applicationIds);

    /**
     * 删除机构招募申请信息
     * 
     * @param applicationId 机构招募申请主键
     * @return 结果
     */
    public int deleteInstitutionRecruitmentApplicationByApplicationId(Long applicationId);

    /**
     * 根据招募ID查询申请列表
     * 
     * @param recruitmentId 招募ID
     * @return 申请列表
     */
    public List<InstitutionRecruitmentApplication> selectApplicationsByRecruitmentId(Long recruitmentId);

    /**
     * 根据用户ID查询申请列表
     * 
     * @param userId 用户ID
     * @return 申请列表
     */
    public List<InstitutionRecruitmentApplication> selectApplicationsByUserId(Long userId);

    /**
     * 提交申请
     * 
     * @param institutionRecruitmentApplication 申请信息
     * @return 结果
     */
    public int submitApplication(InstitutionRecruitmentApplication institutionRecruitmentApplication);

    /**
     * 审核申请
     * 
     * @param applicationId 申请ID
     * @param status 审核状态
     * @param reviewComment 审核意见
     * @return 结果
     */
    public int reviewApplication(Long applicationId, String status, String reviewComment);

    /**
     * 选中机构
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int selectInstitution(Long applicationId);

    /**
     * 取消选中机构
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int unselectInstitution(Long applicationId);

    /**
     * 取消申请
     * 
     * @param applicationId 申请ID
     * @return 结果
     */
    public int cancelApplication(Long applicationId);

    /**
     * 检查是否已申请
     * 
     * @param recruitmentId 招募ID
     * @param userId 用户ID
     * @return 申请记录
     */
    public InstitutionRecruitmentApplication checkExistingApplication(Long recruitmentId, Long userId);

    /**
     * 获取申请统计信息
     * 
     * @param recruitmentId 招募ID
     * @return 统计信息
     */
    public InstitutionRecruitmentApplication getApplicationStats(Long recruitmentId);

    /**
     * 批量审核申请
     * 
     * @param applicationIds 申请ID数组
     * @param status 审核状态
     * @param reviewComment 审核意见
     * @return 结果
     */
    public int batchReviewApplications(Long[] applicationIds, String status, String reviewComment);

    /**
     * 更新申请（重新申请）
     * 
     * @param institutionRecruitmentApplication 申请信息
     * @return 结果
     */
    public int updateApplicationForReapply(InstitutionRecruitmentApplication institutionRecruitmentApplication);
}
