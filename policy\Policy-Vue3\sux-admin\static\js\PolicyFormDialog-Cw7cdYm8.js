import{_ as Y,r as u,D as Z,A as D,f as t,l as s,o,k as n,h as O,Z as q,i as _,c as V,L as w,M as C,p as g,t as G,m as F,R as J}from"./index-DP10CBaW.js";const K={class:"dialog-footer"},Q={__name:"PolicyFormDialog",props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({})}},emits:["submit","cancel"],setup(p,{expose:E,emit:L}){const R=p,k=L,m=u(!1),x=u(""),y=u("add"),b=u(!1),i=u(null),l=Z({}),$=u({}),c=D(()=>y.value==="view"),B=D(()=>{const d={};return R.formFields.forEach(r=>{r.rules&&r.prop&&(d[r.prop]=r.rules)}),d}),N=(d,r,v={})=>{y.value=d,x.value=r,Object.keys(l).forEach(h=>{delete l[h]}),Object.assign(l,v),$.value={...v},m.value=!0,J(()=>{i.value&&i.value.clearValidate()})},T=()=>{i.value&&i.value.validate(d=>{d&&(b.value=!0,k("submit",{type:y.value,data:{...l}}))})},W=()=>{m.value=!1,k("cancel")};return E({openDialog:N,onSubmitSuccess:()=>{b.value=!1,m.value=!1},onSubmitError:()=>{b.value=!1}}),(d,r)=>{const v=t("el-divider"),h=t("el-col"),U=t("el-input"),j=t("el-option"),z=t("el-select"),A=t("el-switch"),S=t("el-date-picker"),H=t("el-form-item"),M=t("el-row"),P=t("el-form"),f=t("el-button"),I=t("el-dialog");return o(),s(I,{modelValue:m.value,"onUpdate:modelValue":r[0]||(r[0]=e=>m.value=e),title:x.value,width:p.formOption.dialogWidth||"800px","close-on-click-modal":!1,"close-on-press-escape":!1,"append-to-body":""},{footer:n(()=>[O("div",K,[_(f,{onClick:W},{default:n(()=>[g("取 消")]),_:1}),c.value?F("",!0):(o(),s(f,{key:0,type:"primary",loading:b.value,onClick:T},{default:n(()=>[g(" 确 定 ")]),_:1},8,["loading"]))])]),default:n(()=>[O("div",{class:"form-container",style:q({maxHeight:p.formOption.dialogHeight||"60vh",overflowY:"auto"})},[_(P,{ref_key:"formRef",ref:i,model:l,rules:B.value,"label-width":p.formOption.labelWidth||"100px"},{default:n(()=>[_(M,{gutter:20},{default:n(()=>[(o(!0),V(w,null,C(p.formFields,e=>(o(),V(w,{key:e.prop},[e.divider?(o(),s(h,{key:0,span:24,class:"divider-col"},{default:n(()=>[_(v,{"content-position":"left"},{default:n(()=>[g(G(e.label),1)]),_:2},1024)]),_:2},1024)):(o(),s(h,{key:1,span:e.span||12,class:"form-col"},{default:n(()=>[_(H,{label:e.label,prop:e.prop,rules:e.rules},{default:n(()=>[!e.type||e.type==="input"?(o(),s(U,{key:0,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,placeholder:e.placeholder||`请输入${e.label}`,disabled:c.value||e.disabled,maxlength:e.maxlength,"show-word-limit":e.showWordLimit,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","maxlength","show-word-limit"])):e.type==="textarea"?(o(),s(U,{key:1,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,type:"textarea",placeholder:e.placeholder||`请输入${e.label}`,disabled:c.value||e.disabled,rows:e.minRows||4,maxlength:e.maxlength,"show-word-limit":e.showWordLimit,resize:"vertical"},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled","rows","maxlength","show-word-limit"])):e.type==="select"?(o(),s(z,{key:2,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,placeholder:e.placeholder||`请选择${e.label}`,disabled:c.value||e.disabled,clearable:"",style:{width:"100%"}},{default:n(()=>[(o(!0),V(w,null,C(e.dicData,a=>(o(),s(j,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="switch"?(o(),s(A,{key:3,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,disabled:c.value||e.disabled,"active-value":"0","inactive-value":"1"},null,8,["modelValue","onUpdate:modelValue","disabled"])):e.type==="date"?(o(),s(S,{key:4,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,type:"date",placeholder:e.placeholder||`请选择${e.label}`,disabled:c.value||e.disabled,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])):e.type==="datetime"?(o(),s(S,{key:5,modelValue:l[e.prop],"onUpdate:modelValue":a=>l[e.prop]=a,type:"datetime",placeholder:e.placeholder||`请选择${e.label}`,disabled:c.value||e.disabled,style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"])):F("",!0)]),_:2},1032,["label","prop","rules"])]),_:2},1032,["span"]))],64))),128))]),_:1})]),_:1},8,["model","rules","label-width"])],4)]),_:1},8,["modelValue","title","width"])}}},ae=Y(Q,[["__scopeId","data-v-d27efac8"]]);export{ae as default};
