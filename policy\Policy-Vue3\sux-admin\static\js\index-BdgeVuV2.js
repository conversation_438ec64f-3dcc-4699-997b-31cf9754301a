import{_,b as h,r as p,A as i,e as m,c as v,h as e,t as n,B as w,p as k,x as f,y as g,o as L}from"./index-DP10CBaW.js";const l=a=>(f("data-v-daa7130d"),a=a(),g(),a),N={class:"welcome-container"},x={class:"welcome-card"},S={class:"welcome-header"},$={class:"welcome-title"},I=l(()=>e("p",{class:"welcome-subtitle"},"Welcome to Our Platform",-1)),B={class:"welcome-content"},M=w('<div class="welcome-icon" data-v-daa7130d><svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-daa7130d><path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-daa7130d></path><path d="M2 17L12 22L22 17" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-daa7130d></path><path d="M2 12L12 17L22 12" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-daa7130d></path></svg></div>',1),y={class:"welcome-message"},D=l(()=>e("p",null,"您已成功进入我们的系统平台",-1)),E={class:"user-info"},j={class:"user-detail"},F={class:"user-name"},V={class:"login-time"},b={__name:"index",setup(a){const o=h(),r=p(new Date().toLocaleString("zh-CN")),c=i(()=>o.nickName),d=i(()=>o.name),u=()=>{const t=new Date().getHours(),s=c.value||d.value||"用户";return t<6?`凌晨好，${s}！`:t<9?`早上好，${s}！`:t<12?`上午好，${s}！`:t<14?`中午好，${s}！`:t<17?`下午好，${s}！`:t<19?`傍晚好，${s}！`:`晚上好，${s}！`};return setInterval(()=>{r.value=new Date().toLocaleString("zh-CN")},1e3),m(async()=>{try{!o.nickName&&!o.name&&await o.getInfo()}catch(t){console.error("获取用户信息失败:",t)}}),(t,s)=>(L(),v("div",N,[e("div",x,[e("div",S,[e("h1",$,"欢迎，"+n(c.value||"用户")+"！",1),I]),e("div",B,[M,e("div",y,[e("h2",null,n(u()),1),D,e("div",E,[e("p",j,[k("当前登录用户："),e("span",F,n(c.value||d.value||"未知用户"),1)]),e("p",V,"登录时间："+n(r.value),1)])])])])]))}},C=_(b,[["__scopeId","data-v-daa7130d"]]);export{C as default};
