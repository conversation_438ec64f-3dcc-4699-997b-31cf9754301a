import{a as Y,l as Z,g as ee,d as te,r as ae,b as ne,u as oe}from"./type-CNISLLEN.js";import{g as le,e as se}from"./columnUtils-DYlA-XL_.js";import{_ as ce,C as ie,d as re,r as t,e as de,f as F,K as ue,c as w,o as c,l as g,i as S,k as s,h as z,G as y,p as h,t as me,V as pe}from"./index-DP10CBaW.js";import{T as ve}from"./index-BWWetMd6.js";import ge from"./DictFormDialog-DaQKc8ko.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";const he={class:"dict-container app-container"},fe={class:"operation-btns"},ye={key:1,class:"loading-placeholder"},_e=ie({name:"Dict"}),Ce=Object.assign(_e,{setup(be){const{proxy:l}=re(),P=t([]),R=t([]),_=t(!1),T=t(!1),D=t({dialogWidth:"600px",dialogHeight:"60vh"}),u=t(null),m=t(null),i=t(1),C=t(10),x=t(0),b=t({}),f=t([]),k=t([]),N=t(!0),L=t(!0),I=t([]),O=t([]);de(async()=>{await V()});const V=async()=>{try{const e=Y(l),a=await le({baseOption:e,proxy:l}),{tableColumns:n,searchColumns:o,formFields:p,formOptions:d}=se(a);P.value=n,R.value=o,O.value=p,D.value={...D.value,...d},T.value=!0,r()}catch(e){T.value=!1,console.error("初始化配置失败:",e)}},r=()=>{_.value=!0;const e={pageNum:i.value,pageSize:C.value,...b.value};Z(l.addDateRange(e,f.value)).then(a=>{I.value=a.rows,x.value=a.total,_.value=!1,B()}).catch(()=>{_.value=!1})},B=()=>{u.value&&u.value.page&&(u.value.page.total=x.value,u.value.page.currentPage=i.value,u.value.page.pageSize=C.value)},E=e=>{e.createTime&&Array.isArray(e.createTime)?(f.value=e.createTime,delete e.createTime):f.value=[],b.value=e||{},i.value=1,r()},A=()=>{b.value={},f.value=[],i.value=1,r()},j=e=>{i.value=e,r()},q=e=>{C.value=e,i.value=1,r()},W=e=>{k.value=e,N.value=e.length!==1,L.value=!e.length},G=e=>{var a;(a=m.value)==null||a.openDialog("view","查看字典",e)},H=e=>{var n;const a=e?e.dictId:(n=k.value[0])==null?void 0:n.dictId;a&&ee(a).then(o=>{var p;(p=m.value)==null||p.openDialog("edit","编辑字典",o.data)})},K=()=>{var e;(e=m.value)==null||e.openDialog("add","新增字典",{status:"0"})},M=async e=>{var a,n;try{e.type==="add"?(await ne(e.data),l.$modal.msgSuccess("添加成功")):e.type==="edit"&&(await oe(e.data),l.$modal.msgSuccess("修改成功")),(a=m.value)==null||a.onSubmitSuccess(),r()}catch(o){(n=m.value)==null||n.onSubmitError(),console.error("提交失败:",o)}},J=()=>{},Q=e=>{const a=e?e.dictId:k.value.map(o=>o.dictId),n=e?e.dictName:k.value.map(o=>o.dictName).join("、");l.$modal.confirm("是否确认删除字典【"+n+"】的数据项？").then(()=>te(a)).then(()=>{r(),l.$modal.msgSuccess("删除成功")})},U=()=>{const e={...b.value};l.download("system/dict/type/export",l.addDateRange(e,f.value),`dict_${new Date().getTime()}.xlsx`)},X=()=>{ae().then(()=>{l.$modal.msgSuccess("刷新成功"),pe().cleanDict()})};return(e,a)=>{const n=F("el-button"),o=F("router-link"),p=F("el-empty"),d=ue("hasPermi");return c(),w("div",he,[T.value?(c(),g(ve,{key:0,columns:P.value,data:I.value,loading:_.value,showIndex:!0,searchColumns:R.value,showOperation:!0,operationLabel:"操作",operationWidth:"200",fixedOperation:!0,ref_key:"tableListRef",ref:u,onSearch:E,onReset:A,defaultPage:{pageSize:C.value,currentPage:i.value,total:x.value},onCurrentChange:j,onSizeChange:q,onSelectionChange:W},{"menu-left":s(()=>[y((c(),g(n,{type:"primary",class:"custom-btn",onClick:K},{default:s(()=>[h(" 新 增 ")]),_:1})),[[d,["system:dict:add"]]]),y((c(),g(n,{type:"warning",class:"custom-btn",onClick:U},{default:s(()=>[h(" 导 出 ")]),_:1})),[[d,["system:dict:export"]]]),y((c(),g(n,{type:"danger",class:"custom-btn",plain:"",onClick:X},{default:s(()=>[h(" 刷 新 缓 存 ")]),_:1})),[[d,["system:dict:remove"]]])]),dictType:s(({row:v})=>[S(o,{to:"/system/dict-data/index/"+v.dictId,class:"link-type"},{default:s(()=>[z("span",null,me(v.dictType),1)]),_:2},1032,["to"])]),menu:s(({row:v})=>[z("div",fe,[S(n,{type:"primary",link:"",class:"table-action-btn",onClick:$=>G(v)},{default:s(()=>[h("查看")]),_:2},1032,["onClick"]),y((c(),g(n,{type:"primary",link:"",class:"table-action-btn",onClick:$=>H(v)},{default:s(()=>[h("编辑")]),_:2},1032,["onClick"])),[[d,["system:dict:edit"]]]),y((c(),g(n,{type:"danger",link:"",class:"table-action-btn",onClick:$=>Q(v)},{default:s(()=>[h("删除")]),_:2},1032,["onClick"])),[[d,["system:dict:remove"]]])])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(c(),w("div",ye,[S(p,{description:"正在加载表格配置..."})])),S(ge,{ref_key:"dictFormDialogRef",ref:m,formFields:O.value,formOption:D.value,onSubmit:M,onCancel:J},null,8,["formFields","formOption"])])}}}),Re=ce(Ce,[["__scopeId","data-v-ab6890a6"]]);export{Re as default};
