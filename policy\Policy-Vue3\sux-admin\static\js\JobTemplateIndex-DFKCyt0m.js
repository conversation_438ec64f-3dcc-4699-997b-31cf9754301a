import{c as ce}from"./job-Co8Wkw3E.js";import{l as ie,g as O,d as re,r as de,c as me,a as pe,u as he}from"./job-DYlEC7Ga.js";import{T as _e}from"./index-BWWetMd6.js";import be from"./JobFormDialog-1LH-z8Lc.js";import{_ as ve,C as fe,a as ge,d as Ce,r as n,e as je,f as c,K as ye,c as P,o as i,l as r,i as o,k as a,h as Se,G as h,j as ke,p as D}from"./index-DP10CBaW.js";import"./index-BylsdGrt.js";import"./index-B-A7bGAb.js";const xe={class:"job-container app-container"},De={class:"operation-btns"},Re={key:1,class:"loading-placeholder"},$e=fe({name:"JobTemplateIndex"}),Ie=Object.assign($e,{setup(Je){const T=ge(),{proxy:s}=Ce(),{sys_job_group:V,sys_job_status:Fe}=s.useDict("sys_job_group","sys_job_status"),R=n([]),$=n([]),C=n(!1),S=n(!1),k=n({dialogWidth:"900px",dialogHeight:"70vh"}),v=n(null),d=n(null),_=n(1),j=n(10),x=n(0),y=n({}),I=n([]),J=n([]);je(()=>{q()});const q=()=>{try{const e=ce(s);e.dialogWidth&&(k.value.dialogWidth=e.dialogWidth),e.dialogHeight&&(k.value.dialogHeight=e.dialogHeight),R.value=z(e),$.value=E(e),J.value=W(e),S.value=!0,m()}catch(e){S.value=!1,console.error("初始化配置失败:",e)}},z=e=>!e||!e.column||!e.column.length?[]:e.column.filter(t=>t.showColumn!==!1&&t.divider!==!0).map(t=>({prop:t.prop,label:t.label,search:!!t.search,type:t.type,dicData:t.dicData,width:t.width,minWidth:t.minWidth||(t.width?null:100),slot:t.slot||!1,searchRange:t.searchRange,sortable:t.sortable||!1,align:t.align||"center"})),E=e=>!e||!e.column||!e.column.length?[]:e.column.filter(t=>t.search===!0||t.search===1).map(t=>({prop:t.prop,label:t.label,type:t.type||"input",dicData:t.dicData,searchMultiple:t.searchMultiple,searchRange:t.searchRange,searchSlot:t.searchSlot})),W=e=>!e||!e.column||!e.column.length?[]:e.column.map(t=>({...t,required:t.isRequired||t.rules&&t.rules.some(l=>l.required)||!1})),m=()=>{C.value=!0;const e={pageNum:_.value,pageSize:j.value,...y.value};ie(e).then(t=>{I.value=t.rows,x.value=t.total,L(),C.value=!1}).catch(()=>{C.value=!1})},L=()=>{v.value&&v.value.page&&(v.value.page.total=x.value,v.value.page.currentPage=_.value,v.value.page.pageSize=j.value)},N=e=>{y.value=e,_.value=1,m()},B=()=>{y.value={},_.value=1,m()},G=e=>{_.value=e,m()},H=e=>{j.value=e,_.value=1,m()},M=e=>{let t=e.status==="0"?"启用":"停用";s.$modal.confirm('确认要"'+t+'""'+e.jobName+'"任务吗?').then(function(){return me(e.jobId,e.status)}).then(()=>{s.$modal.msgSuccess(t+"成功")}).catch(function(){e.status=e.status==="0"?"1":"0"})},w=e=>{s.$modal.confirm('确认要立即执行一次"'+e.jobName+'"任务吗?').then(function(){return de(e.jobId,e.jobGroup)}).then(()=>{s.$modal.msgSuccess("执行成功")}).catch(()=>{})},U=()=>{d.value.openDialog("add","新增任务")},A=e=>{O(e.jobId).then(t=>{d.value.openDialog("edit","修改任务",t.data)})},K=e=>{O(e.jobId).then(t=>{d.value.openDialog("view","任务详细",t.data)})},Q=e=>{const t=e.jobId;s.$modal.confirm('是否确认删除定时任务编号为"'+t+'"的数据项?').then(function(){return re(t)}).then(()=>{m(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})},X=()=>{s.download("monitor/job/export",{...y.value},`job_${new Date().getTime()}.xlsx`)},F=e=>{const t=(e==null?void 0:e.jobId)||0;T.push("/monitor/job-log/index/"+t)},Y=({type:e,data:t})=>{e==="add"?pe(t).then(l=>{s.$modal.msgSuccess("新增成功"),d.value.onSubmitSuccess(),m()}).catch(()=>{d.value.onSubmitError()}):e==="edit"&&he(t).then(l=>{s.$modal.msgSuccess("修改成功"),d.value.onSubmitSuccess(),m()}).catch(()=>{d.value.onSubmitError()})},Z=()=>{};return(e,t)=>{const l=c("el-button"),ee=c("dict-tag"),te=c("el-switch"),ae=c("Edit"),f=c("el-icon"),g=c("el-tooltip"),oe=c("Delete"),ne=c("CaretRight"),le=c("View"),se=c("Operation"),ue=c("el-empty"),p=ye("hasPermi");return i(),P("div",xe,[S.value?(i(),r(_e,{key:0,columns:R.value,data:I.value,loading:C.value,showIndex:!0,searchColumns:$.value,showOperation:!0,operationLabel:"操作",operationWidth:"200",fixedOperation:!0,ref_key:"tableListRef",ref:v,onSearch:N,onReset:B,defaultPage:{pageSize:j.value,currentPage:_.value,total:x.value},onCurrentChange:G,onSizeChange:H},{"menu-left":a(()=>[h((i(),r(l,{type:"primary",class:"custom-btn",onClick:U},{default:a(()=>[D(" 新增 ")]),_:1})),[[p,["monitor:job:add"]]]),h((i(),r(l,{type:"warning",class:"custom-btn",onClick:X},{default:a(()=>[D(" 导出 ")]),_:1})),[[p,["monitor:job:export"]]]),h((i(),r(l,{type:"info",class:"custom-btn",onClick:F},{default:a(()=>[D(" 日志 ")]),_:1})),[[p,["monitor:job:query"]]])]),jobGroup:a(({row:u})=>[o(ee,{options:ke(V),value:u.jobGroup},null,8,["options","value"])]),status:a(({row:u})=>[o(te,{modelValue:u.status,"onUpdate:modelValue":b=>u.status=b,"active-value":"0","inactive-value":"1",onChange:b=>M(u)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),menu:a(({row:u})=>[Se("div",De,[o(g,{content:"修改",placement:"top"},{default:a(()=>[h((i(),r(l,{type:"primary",link:"",class:"op-btn",onClick:b=>A(u)},{default:a(()=>[o(f,null,{default:a(()=>[o(ae)]),_:1})]),_:2},1032,["onClick"])),[[p,["monitor:job:edit"]]])]),_:2},1024),o(g,{content:"删除",placement:"top"},{default:a(()=>[h((i(),r(l,{type:"danger",link:"",class:"op-btn",onClick:b=>Q(u)},{default:a(()=>[o(f,null,{default:a(()=>[o(oe)]),_:1})]),_:2},1032,["onClick"])),[[p,["monitor:job:remove"]]])]),_:2},1024),o(g,{content:"执行一次",placement:"top"},{default:a(()=>[h((i(),r(l,{type:"success",link:"",class:"op-btn",onClick:b=>w(u)},{default:a(()=>[o(f,null,{default:a(()=>[o(ne)]),_:1})]),_:2},1032,["onClick"])),[[p,["monitor:job:changeStatus"]]])]),_:2},1024),o(g,{content:"任务详细",placement:"top"},{default:a(()=>[h((i(),r(l,{type:"info",link:"",class:"op-btn",onClick:b=>K(u)},{default:a(()=>[o(f,null,{default:a(()=>[o(le)]),_:1})]),_:2},1032,["onClick"])),[[p,["monitor:job:query"]]])]),_:2},1024),o(g,{content:"调度日志",placement:"top"},{default:a(()=>[h((i(),r(l,{type:"warning",link:"",class:"op-btn",onClick:b=>F(u)},{default:a(()=>[o(f,null,{default:a(()=>[o(se)]),_:1})]),_:2},1032,["onClick"])),[[p,["monitor:job:query"]]])]),_:2},1024)])]),_:1},8,["columns","data","loading","searchColumns","defaultPage"])):(i(),P("div",Re,[o(ue,{description:"正在加载表格配置..."})])),o(be,{ref_key:"jobFormDialogRef",ref:d,formFields:J.value,formOption:k.value,onSubmit:Y,onCancel:Z},null,8,["formFields","formOption"])])}}}),We=ve(Ie,[["__scopeId","data-v-7af06eda"]]);export{We as default};
