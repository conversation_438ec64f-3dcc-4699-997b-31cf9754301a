import{_ as N,C as W,a8 as j,A as g,d as J,r as a,f as _,l as B,o as L,k as u,h,Z as T,i as v,p as b,t as X}from"./index-DP10CBaW.js";import{V as Y}from"./index-B-A7bGAb.js";const A={class:"dialog-footer"},E=W({name:"JobLogFormDialog"}),I=Object.assign(E,{props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({dialogWidth:"800px",dialogHeight:"70vh"})}},emits:["submit","cancel"],setup(d,{expose:y,emit:w}){j(e=>({"0e1788a6":t.value.maxHeight,"39e7916c":t.value.minHeight||"auto","019a8099":t.value.overflowY,fd31eeda:t.value.padding}));const{proxy:x}=J(),{sys_common_status:U,sys_job_group:z}=x.useDict("sys_common_status","sys_job_group"),c=d,V=w,s=a(!1),p=a("view"),r=a("调度日志详细"),n=a({}),o=a(!1),C=g(()=>c.formFields.length?c.formFields.filter(e=>e.viewDisplay!==!1):[]),t=g(()=>{const e={overflow:"visible",padding:"20px 10px",overflowX:"hidden"};return o.value?{...e,maxHeight:"calc(100vh - 180px)",overflowY:"auto",overflowX:"hidden"}:{...e,maxHeight:c.formOption.dialogHeight||"70vh",overflowY:"auto",overflowX:"hidden",minHeight:"auto"}}),S=()=>{o.value=!o.value},D=(e,l,i={})=>{p.value="view",r.value=l||"调度日志详细",n.value=JSON.parse(JSON.stringify(i)),s.value=!0},m=()=>{s.value=!1},O=()=>{V("cancel"),m()},F=()=>{},H=()=>{n.value={},p.value="view",r.value="调度日志详细",o.value=!1};return y({openDialog:D,closeDialog:m,onSubmitSuccess:()=>{m()},onSubmitError:()=>{}}),(e,l)=>{const i=_("el-button"),k=_("el-dialog");return L(),B(k,{modelValue:s.value,"onUpdate:modelValue":l[1]||(l[1]=f=>s.value=f),title:r.value,width:d.formOption.dialogWidth,"destroy-on-close":"","close-on-click-modal":!1,fullscreen:o.value,onClosed:H,onOpen:F,class:"custom-dialog"},{footer:u(()=>[h("span",A,[v(i,{class:"custom-btn",onClick:S},{default:u(()=>[b(X(o.value?"退出全屏":"全屏显示"),1)]),_:1}),v(i,{class:"custom-btn",onClick:O},{default:u(()=>[b(" 关 闭 ")]),_:1})])]),default:u(()=>[h("div",{class:"dialog-content view-mode",style:T(t.value)},[v(Y,{modelValue:n.value,"onUpdate:modelValue":l[0]||(l[0]=f=>n.value=f),fields:C.value,labelWidth:d.formOption.labelWidth||"120px"},null,8,["modelValue","fields","labelWidth"])],4)]),_:1},8,["modelValue","title","width","fullscreen"])}}}),M=N(I,[["__scopeId","data-v-71d187a5"]]);export{M as default};
