import{_ as oe,C as se,a8 as de,A as E,d as ne,r as s,f as I,l as T,o as N,k as u,h as V,Z as ue,N as re,a2 as ie,i as c,p as v,m as ce,t as B,R as X}from"./index-DP10CBaW.js";import{F as fe}from"./index-BylsdGrt.js";import{V as ve}from"./index-B-A7bGAb.js";import{t as me,r as pe}from"./menu-D5wKpgmc.js";import{e as he}from"./role-FWqI-CVH.js";const ye={class:"tree-controls"},ke={class:"tree-controls"},ge={class:"view-menu-tree"},Ce={class:"dialog-footer"},be=se({name:"RoleFormDialog"}),we=Object.assign(be,{props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({dialogWidth:"600px",dialogHeight:"70vh"})}},emits:["submit","cancel"],setup(x,{expose:Y,emit:j}){de(l=>({"7b1b83d0":w.value.maxHeight,"24d949cf":w.value.minHeight||"auto",f87c6e88:w.value.overflowY,"63f7fd76":w.value.padding}));const{proxy:Ve}=ne(),g=x,H=j,S=s(!1),f=s("add"),U=s("新增角色"),O=s(null),o=s({}),m=s(!1),p=s(!1),d=s(null),h=s(null),k=s([]),C=s([]),K=s(!1),F=s(!1),D=s(!0),_=s(!1),b=s(!1),A=E(()=>g.formFields.length?f.value==="add"?g.formFields.filter(l=>l.addDisplay!==!1):f.value==="edit"?g.formFields.filter(l=>l.editDisplay!==!1):g.formFields.filter(l=>l.viewDisplay!==!1):[]),w=E(()=>{const l={overflow:"visible",padding:"20px 10px",overflowX:"hidden"};return b.value?{...l,maxHeight:"calc(100vh - 180px)",overflowY:"auto",overflowX:"hidden"}:{...l,maxHeight:g.formOption.dialogHeight||"70vh",overflowY:"auto",overflowX:"hidden",minHeight:"auto"}}),z=()=>{b.value=!b.value},Z=async(l,e,a={})=>{f.value=l,U.value=e,o.value={roleId:void 0,roleName:void 0,roleKey:void 0,roleSort:0,status:"0",menuIds:[],deptIds:[],menuCheckStrictly:!0,deptCheckStrictly:!0,remark:void 0,dataScope:"1",...a},K.value=!1,F.value=!1,D.value=!0,_.value=!1,d.value&&d.value.setCheckedKeys([]),S.value=!0,await X(),await q(),(l==="edit"||l==="view")&&a.roleId&&await G(a.roleId)},R=()=>{S.value=!1},q=async()=>{try{const l=await me();k.value=l.data}catch(l){console.error("获取菜单树失败:",l)}},G=async l=>{try{const e=await pe(l);return k.value=e.menus,await X(),e.checkedKeys&&e.checkedKeys.length>0&&setTimeout(()=>{d.value&&(d.value.setCheckedKeys([]),e.checkedKeys.forEach(a=>{d.value&&d.value.setChecked(a,!0,!1)}))},100),e}catch(e){console.error("获取角色菜单树失败:",e)}},J=async l=>{try{const e=await he(l);return C.value=e.depts,e}catch(e){console.error("获取部门树失败:",e)}},M=(l,e)=>{var a,n;if(e==="menu"){let r=k.value;for(let i=0;i<r.length;i++)(a=d.value)!=null&&a.store.nodesMap[r[i].id]&&(d.value.store.nodesMap[r[i].id].expanded=l)}else if(e==="dept"){let r=C.value;for(let i=0;i<r.length;i++)(n=h.value)!=null&&n.store.nodesMap[r[i].id]&&(h.value.store.nodesMap[r[i].id].expanded=l)}},W=(l,e)=>{var a,n;e==="menu"?(a=d.value)==null||a.setCheckedNodes(l?k.value:[]):e==="dept"&&((n=h.value)==null||n.setCheckedNodes(l?C.value:[]))},L=(l,e)=>{e==="menu"?o.value.menuCheckStrictly=!!l:e==="dept"&&(o.value.deptCheckStrictly=!!l)},P=()=>{var a,n;let l=((a=d.value)==null?void 0:a.getCheckedKeys())||[],e=((n=d.value)==null?void 0:n.getHalfCheckedKeys())||[];return l.unshift.apply(l,e),l},Q=()=>{var a,n;let l=((a=h.value)==null?void 0:a.getCheckedKeys())||[],e=((n=h.value)==null?void 0:n.getHalfCheckedKeys())||[];return l.unshift.apply(l,e),l},$=(l,e)=>{l==="dataScope"&&e==="2"&&J(o.value.roleId)},ee=async()=>{if(!(m.value||p.value)&&O.value)try{m.value=!0,p.value=!0,await O.value.validate(),o.value.menuIds=P(),o.value.dataScope==="2"&&(o.value.deptIds=Q()),H("submit",{type:f.value,data:o.value})}catch{m.value=!1,p.value=!1}},le=()=>{H("cancel"),R()},ae=()=>{m.value=!1,p.value=!1},te=()=>{o.value={},k.value=[],C.value=[],d.value&&d.value.setCheckedKeys([]),h.value&&h.value.setCheckedKeys([]),K.value=!1,F.value=!1,D.value=!0,_.value=!1,m.value=!1,p.value=!1};return Y({openDialog:Z,closeDialog:R,onSubmitSuccess:()=>{m.value=!1,p.value=!1,R()},onSubmitError:()=>{m.value=!1,p.value=!1}}),(l,e)=>{const a=I("el-checkbox"),n=I("el-tree"),r=I("el-button"),i=I("el-dialog");return N(),T(i,{modelValue:S.value,"onUpdate:modelValue":e[14]||(e[14]=y=>S.value=y),title:U.value,width:x.formOption.dialogWidth,"destroy-on-close":"","close-on-click-modal":!1,fullscreen:b.value,onClosed:te,onOpen:ae,class:"custom-dialog"},{footer:u(()=>[V("span",Ce,[c(r,{class:"custom-btn",onClick:z},{default:u(()=>[v(B(b.value?"退出全屏":"全屏显示"),1)]),_:1}),c(r,{class:"custom-btn",onClick:le},{default:u(()=>[v(B(f.value==="view"?"关闭":"取消"),1)]),_:1}),f.value!=="view"?(N(),T(r,{key:0,type:"primary",class:"custom-btn",onClick:ee,loading:m.value,disabled:p.value},{default:u(()=>[v(" 确 认 ")]),_:1},8,["loading","disabled"])):ce("",!0)])]),default:u(()=>[V("div",{class:re(["dialog-content",{"view-mode":f.value==="view"}]),style:ue(w.value)},[f.value!=="view"?(N(),T(fe,{key:0,ref_key:"formListRef",ref:O,modelValue:o.value,"onUpdate:modelValue":e[12]||(e[12]=y=>o.value=y),fields:A.value,"is-view":f.value==="view",showActions:!1,labelWidth:x.formOption.labelWidth,inline:!1,onFieldChange:$},ie({menuIds:u(({row:y})=>[V("div",ye,[c(a,{modelValue:K.value,"onUpdate:modelValue":e[0]||(e[0]=t=>K.value=t),onChange:e[1]||(e[1]=t=>M(t,"menu"))},{default:u(()=>[v("展开/折叠")]),_:1},8,["modelValue"]),c(a,{modelValue:F.value,"onUpdate:modelValue":e[2]||(e[2]=t=>F.value=t),onChange:e[3]||(e[3]=t=>W(t,"menu"))},{default:u(()=>[v("全选/全不选")]),_:1},8,["modelValue"]),c(a,{modelValue:o.value.menuCheckStrictly,"onUpdate:modelValue":e[4]||(e[4]=t=>o.value.menuCheckStrictly=t),onChange:e[5]||(e[5]=t=>L(t,"menu"))},{default:u(()=>[v("父子联动")]),_:1},8,["modelValue"])]),c(n,{class:"tree-border",data:k.value,"show-checkbox":"",ref_key:"menuRef",ref:d,"node-key":"id","check-strictly":!o.value.menuCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),_:2},[o.value.dataScope==="2"?{name:"deptIds",fn:u(({row:y})=>[V("div",ke,[c(a,{modelValue:D.value,"onUpdate:modelValue":e[6]||(e[6]=t=>D.value=t),onChange:e[7]||(e[7]=t=>M(t,"dept"))},{default:u(()=>[v("展开/折叠")]),_:1},8,["modelValue"]),c(a,{modelValue:_.value,"onUpdate:modelValue":e[8]||(e[8]=t=>_.value=t),onChange:e[9]||(e[9]=t=>W(t,"dept"))},{default:u(()=>[v("全选/全不选")]),_:1},8,["modelValue"]),c(a,{modelValue:o.value.deptCheckStrictly,"onUpdate:modelValue":e[10]||(e[10]=t=>o.value.deptCheckStrictly=t),onChange:e[11]||(e[11]=t=>L(t,"dept"))},{default:u(()=>[v("父子联动")]),_:1},8,["modelValue"])]),c(n,{class:"tree-border",data:C.value,"show-checkbox":"","default-expand-all":"",ref_key:"deptRef",ref:h,"node-key":"id","check-strictly":!o.value.deptCheckStrictly,"empty-text":"加载中，请稍候",props:{label:"label",children:"children"}},null,8,["data","check-strictly"])]),key:"0"}:void 0]),1032,["modelValue","fields","is-view","labelWidth"])):(N(),T(ve,{key:1,modelValue:o.value,"onUpdate:modelValue":e[13]||(e[13]=y=>o.value=y),fields:A.value,labelWidth:x.formOption.labelWidth},{menuIds:u(({row:y})=>[V("div",ge,[c(n,{class:"tree-border view-tree",data:k.value,"show-checkbox":"",ref_key:"menuRef",ref:d,"node-key":"id","check-strictly":!1,"default-expand-all":!0,props:{label:"label",children:"children"},disabled:""},null,8,["data"])])]),_:1},8,["modelValue","fields","labelWidth"]))],6)]),_:1},8,["modelValue","title","width","fullscreen"])}}}),Te=oe(we,[["__scopeId","data-v-a4956dff"]]);export{Te as default};
