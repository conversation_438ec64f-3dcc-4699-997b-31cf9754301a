import{_ as j,C as z,a8 as E,A as w,d as I,r as s,f as _,l as v,o as p,k as c,h as x,Z as R,N as Z,i as D,m as q,p as b,t as k}from"./index-DP10CBaW.js";import{F as G}from"./index-BylsdGrt.js";import{V as J}from"./index-B-A7bGAb.js";const K={class:"dialog-footer"},M=z({name:"DictFormDialog"}),P=Object.assign(M,{props:{formFields:{type:Array,default:()=>[]},formOption:{type:Object,default:()=>({dialogWidth:"600px",dialogHeight:"60vh",labelWidth:"100px"})}},emits:["submit","cancel"],setup(f,{expose:S,emit:O}){E(e=>({"7c9afd67":F.value.maxHeight}));const{proxy:Q}=I(),n=f,y=O,m=s(!1),l=s("add"),C=s("新增字典"),g=s(null),i=s({}),t=s(!1),o=s(!1),u=s(!1),V=w(()=>n.formFields.length?l.value==="add"?n.formFields.filter(e=>e.addDisplay!==!1):l.value==="edit"?n.formFields.filter(e=>e.editDisplay!==!1):n.formFields.filter(e=>e.viewDisplay!==!1):[]),F=w(()=>u.value?{maxHeight:"calc(100vh - 150px)",height:"auto"}:{maxHeight:n.formOption.dialogHeight||"60vh",height:"auto"}),W=()=>{u.value=!u.value},H=(e,a,d={})=>{l.value=e,C.value=a,i.value={...d},m.value=!0},h=()=>{m.value=!1},L=(e,a)=>{},N=async()=>{if(!(t.value||o.value)&&g.value)try{t.value=!0,o.value=!0,await g.value.validate(),y("submit",{type:l.value,data:i.value})}catch{t.value=!1,o.value=!1}},A=()=>{y("cancel"),h()},B=()=>{t.value=!1,o.value=!1},T=()=>{i.value={},t.value=!1,o.value=!1};return S({openDialog:H,closeDialog:h,onSubmitSuccess:()=>{t.value=!1,o.value=!1,h()},onSubmitError:()=>{t.value=!1,o.value=!1}}),(e,a)=>{const d=_("el-button"),U=_("el-dialog");return p(),v(U,{modelValue:m.value,"onUpdate:modelValue":a[2]||(a[2]=r=>m.value=r),title:C.value,width:f.formOption.dialogWidth,"destroy-on-close":"","close-on-click-modal":!1,fullscreen:u.value,onClosed:T,onOpen:B,class:"custom-dialog"},{footer:c(()=>[x("span",K,[D(d,{class:"custom-btn",onClick:W},{default:c(()=>[b(k(u.value?"退出全屏":"全屏显示"),1)]),_:1}),D(d,{class:"custom-btn",onClick:A},{default:c(()=>[b(k(l.value==="view"?"关闭":"取消"),1)]),_:1}),l.value!=="view"?(p(),v(d,{key:0,type:"primary",class:"custom-btn",onClick:N,loading:t.value,disabled:o.value},{default:c(()=>[b(" 确 认 ")]),_:1},8,["loading","disabled"])):q("",!0)])]),default:c(()=>[x("div",{class:Z(["dialog-content",{"view-mode":l.value==="view"}]),style:R(F.value)},[l.value!=="view"?(p(),v(G,{key:0,ref_key:"formListRef",ref:g,modelValue:i.value,"onUpdate:modelValue":a[0]||(a[0]=r=>i.value=r),fields:V.value,"is-view":l.value==="view",showActions:!1,labelWidth:f.formOption.labelWidth,inline:!1,onFieldChange:L},null,8,["modelValue","fields","is-view","labelWidth"])):(p(),v(J,{key:1,modelValue:i.value,"onUpdate:modelValue":a[1]||(a[1]=r=>i.value=r),fields:V.value,labelWidth:f.formOption.labelWidth},null,8,["modelValue","fields","labelWidth"]))],6)]),_:1},8,["modelValue","title","width","fullscreen"])}}}),ae=j(P,[["__scopeId","data-v-9e3deeb9"]]);export{ae as default};
